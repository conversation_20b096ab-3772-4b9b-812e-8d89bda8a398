{"name": "@development/image-display", "version": "0.2.87", "private": false, "main": "./dist/image-display.umd.js", "module": "./dist/image-display.umd.min.js", "exports": {".": {"import": "./dist/image-display.umd.js", "require": "./dist/image-display.umd.min.js"}}, "publishConfig": {"@development:registry": "https://gitlab.com/api/v4/projects/25328404/packages/npm/"}, "scripts": {"serve": "vue-cli-service serve", "build": "NODE_ENV=production vue-cli-service build --target lib --inline-vue --name image-display ./src/build.js", "lint": "vue-cli-service lint"}, "dependencies": {"@development/linarc-design-components": "^0.6.94", "@riophae/vue-treeselect": "^0.4.0", "core-js": "^3.6.5", "lodash": "^4.17.21", "moment": "^2.29.2", "v-click-outside": "^3.1.2", "vue": "2.6.14", "vuex": "^3.6.2", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "7.12.9", "@fortawesome/fontawesome-svg-core": "^1.2.29", "@fortawesome/free-regular-svg-icons": "^5.13.1", "@fortawesome/free-solid-svg-icons": "^5.13.1", "@fortawesome/vue-fontawesome": "^0.1.10", "@vue/cli-plugin-babel": "4.5.17", "@vue/cli-plugin-eslint": "4.5.17", "@vue/cli-service": "4.5.17", "@vue/eslint-config-prettier": "^6.0.0", "axios": "^0.27.2", "babel-eslint": "10.1.0", "babel-loader": "8.2.5", "bootstrap-vue": "2.0.0-rc.1", "eslint": "^6.7.2", "eslint-config-prettier": "^8.3.0", "eslint-loader": "^4.0.2", "eslint-plugin-lodash": "^7.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "eslint-plugin-vue-scoped-css": "^1.3.0", "lint-staged": "^11.2.3", "module": "^1.2.5", "node-sass": "^5.0.0", "prettier": "^2.4.1", "sass": "^1.26.10", "sass-loader": "^9.0.2", "vue-js-modal": "^2.0.0-rc.6", "vue-loader": "15.9.8", "vue-resize-sensor": "^2.0.0", "vue-select": "^3.10.5", "vue-shortkey": "^3.1.7", "vue-spinner": "^1.0.3", "vue-template-compiler": "2.6.14", "worker-loader": "^2.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended", "@vue/prettier"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "sideEffects": true, "files": ["dist"]}