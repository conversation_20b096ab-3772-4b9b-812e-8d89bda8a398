function getProdExternals() {
  return {};
}
//  const BundleAnalyzerPlugin = require('webpack-bundle-analyzer')
//     .BundleAnalyzerPlugin;
const webpack = require("webpack");
module.exports = {
  runtimeCompiler: true,
  css: { extract: false },
  configureWebpack: {
    externals: process.env.NODE_ENV === "production" ? getProdExternals() : {},
    plugins: [
      // new BundleAnalyzerPlugin(),
      new webpack.ContextReplacementPlugin(/moment[/\\]locale$/, /en|es/),
    ],
    output: {
      libraryExport: "default",
    },
  }
};