module.exports = {
    root: true,
    env: {
      node: true,
      browser: true,
      es6: true,
    },
    extends: [
      'plugin:vue/recommended',
      'eslint:recommended',
      'prettier',
      'plugin:prettier/recommended',
      'plugin:vue-scoped-css/recommended',
    ],
    plugins: ['lodash'],
    rules: {
      eqeqeq: ['error', 'always'],
      'vue/component-name-in-template-casing': ['error', 'PascalCase'],
      'vue-scoped-css/enforce-style-type': ['error', { allows: ['scoped'] }],
      'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
      'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
      'lodash/prefer-lodash-method': [2, { ignoreMethods: ['find', 'replace'] }],
      'lodash/prefer-noop': 'off',
      'lodash/prefer-constant': 'off',
      'lodash/import-scope': [2, 'method'],
      'lodash/prefer-get': ['error', 2],
      //'no-useless-escape': 'off',
      'no-plusplus': 'off',
      'no-shadow': 'off',
      'import/prefer-default-export': 'off',
      'default-case': 'off',
      'no-param-reassign': 'off',
      'no-console': ['off'],
      'func-names': 'off',
      'no-process-exit': 'off',
      'object-shorthand': 'off',
      'class-methods-use-this': 'off',
      //'vue/no-v-html': 'off',
      'vue/attribute-hyphenation': 'off',
      'prettier/prettier': 0,
    },
    parserOptions: {
      requireConfigFile: false,
      parser: 'babel-eslint',
      ecmaVersion: 8,
      sourceType: 'module',
    },
  };
  