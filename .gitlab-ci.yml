image: node:14.20.1

stages:
  - build
  - deploy
  - tag

build:
  stage: build
  only:
    - develop
  script:
    - echo "@development:registry=https://gitlab.com/api/v4/groups/4921253/-/packages/npm/" >> .npmrc
    - echo "//gitlab.com/api/v4/groups/4921253/-/packages/npm/:_authToken=${PRIVATE_ACCESS_TOKEN}" >> .npmrc
    - echo "//gitlab.com/api/v4/projects/32421938/packages/npm/:_authToken=${PRIVATE_ACCESS_TOKEN}" >> .npmrc
    - yarn
    - yarn build
  artifacts:
    paths:
      - "dist/"
      - node_modules/
    when: "on_success"
    expire_in: "1 day"

deploy:
  stage: deploy
  dependencies:
    - build
  only:
    - develop
  script:
    - echo > .npmrc
    - echo "@development:registry=https://gitlab.com/api/v4/projects/25328404/packages/npm/" >> .npmrc
    - echo "//gitlab.com/api/v4/projects/25328404/packages/npm/:_authToken=${PRIVATE_ACCESS_TOKEN}" >> .npmrc
    - npm publish

tag:
  stage: tag
  dependencies:
    - deploy
  only:
    - develop
  script:
    - PACKAGE_VERSION=$(node -p "require('./package.json').version")
    - TAG_NAME="v$PACKAGE_VERSION"
    - git remote set-url origin https://oauth2:${PRIVATE_ACCESS_TOKEN}@gitlab.com/linarc/development/image-display.git
    - git tag $TAG_NAME
    - git push origin $TAG_NAME --force