/**
 * Use this file to register any variables or functions that should be available globally
 * ideally you should make it available via the window object
 * as well as the Vue prototype for access throughout the app
 * (register globals with care, only when it makes since to be accessible app wide)
 */
import Vue from "vue";
import {
  Button,
  Icon,
  Dropdown,
  Checkbox,
  Modal,
  Input,
  Slider,
  ColorPicker,
  Avatar,
  Box,
  ProgressBar,
  Tag,
  AlertWrapper,
  Grid,
  Toggle,
  NoData,
  SmartTable,
  config,
  Radio,
  AttachmentModal,
  rbac,
  SpreadSheet,
  AutoComplete,
  SpreadSheetDataWrapper,
  SpreadSheetV2,
} from "@development/linarc-design-components";
import moment from "moment";

Vue.mixin(rbac.RbacMixin);

Vue.prototype.$designComponentsConfig = config;
Vue.prototype.$moment = moment;
Vue.prototype.$axios = require("axios");

Vue.component("Button", Button);
Vue.component("Icon", Icon);
Vue.component("SpreadSheet", SpreadSheet);
Vue.component("Dropdown", Dropdown);
Vue.component("Checkbox", Checkbox);
Vue.component("SpreadSheetDataWrapper",SpreadSheetDataWrapper)
Vue.component("SpreadSheetV2",SpreadSheetV2)
Vue.component("AutoComplete",AutoComplete)
Vue.component("Modal", Modal);
Vue.component("Input", Input);
Vue.component("Toggle", Toggle);
Vue.component("Slider", Slider);
Vue.component("ColorPicker", ColorPicker);
Vue.component("Avatar", Avatar);
Vue.component("Box", Box);
Vue.component("ProgressBar", ProgressBar);
Vue.component("Tag", Tag);
Vue.component("AlertWrapper", AlertWrapper);
Vue.component("Grid", Grid);
Vue.component("NoData", NoData);
Vue.component("SmartTable", SmartTable);
Vue.component("Radio", Radio);
Vue.component("AttachmentModal", AttachmentModal);
