class Config {
  init(baseURL, accessToken, userId, prjId,pcmId) {
    this.setBaseUrl(baseURL);
    this.accessToken = accessToken;
    this.userId = userId;
    this.prjId = prjId;
    this.pcmId = pcmId;
  }

  setPcmId(pcmId) {
    this.pcmId = pcmId;
  }

  setPrjId(prjId) {
    this.prjId = prjId;
  }

  setBaseUrl(baseURL) {
    this.baseURL = baseURL;
  }

  getBaseUrl() { 
    return this.baseURL;
  }

  getAccessToken() {
    return this.accessToken;
  }

  getUserId() {
    return this.userId;
  }

  getProjectId() {
    return this.prjId;
  }

  getProjectCompanyId() {
    return this.pcmId;
  }
}

export default new Config();
