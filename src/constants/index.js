const MARKUP_MODES = {
    READ: 'read',
    UPDATE: 'update',
    CREATE: 'create'
}

const DRAFT_MARKUP_ID = 'draft101';

export const DRAFT_MARKUP = {
    id: 'draft-markup',
    title: 'Draft',
}

export const MARKUP_TYPES = {
    MARKUP: 'MARKUP',
    TAKE_OFF : 'TAKEOFF'
}

export const PLAN_MARKUP_MODES = {
    EDIT: "edit",
    DRAFT: "draft",
    READONLY: "readonly",
};

export const GRID_V2_COLUMN_TYPE = {
    SHORT_TEXT: 'ShortText',
    NUMBER: 'NumberInput',
    DATE: 'DateInput',
    SLOT: 'CustomSlot',
    PHONE_NUMBER: 'PhoneNumberInput',
    DURATION: 'Duration',
    COLOR: 'ColorInput',
    RADIO: 'RadioInput',
    TEXT_WITH_COLOR_BOX: 'TextWithColorBox',
    SEQUENCE: 'Sequence',
    SELECT: 'SelectInput',
  };

export const SPREADSHEET_FIELD_TYPE = {
    SHORT_TEXT: 'short-text',
    NUMBER: 'number',
    DECIMAL: 'decimal',
    DATE: 'date',
    SINGLE_SELECT: 'single-select',
    SLOT: 'custom-slot',
    AUTO_COMPLETE: 'auto-complete',
  };

export const AccessTypes = {
    PUBLIC: 'PB',
    PRIVATE: 'PR',
    RESTRICTED: 'RE',
}
export const BATCH_STATUS = {
    BATCH_STATUS_PENDING : "PEN",
    BATCH_STATUS_PROCESSING : "PCS",
    BATCH_STATUS_FAILED : "FAL",
    BATCH_STATUS_DONE : "DON",
}

export const BATCH_STATUS_TEXT = {
    "PEN" : 'Upload Pending',
    "PCS" : 'Upload In Progress',
    "FAL" : 'Upload Failed',
    "DON" : 'Upload Completed',
}


export default {
    MARKUP_MODES,
    DRAFT_MARKUP_ID,
    PLAN_MARKUP_MODES,
    GRID_V2_COLUMN_TYPE,
    SPREADSHEET_FIELD_TYPE,
    BATCH_STATUS,
    BATCH_STATUS_TEXT
}