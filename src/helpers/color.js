import map from "lodash/map";
export const hexToRGB = (hex, alpha) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);

  if (alpha) {
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  } else {
    return `rgb(${r}, ${g}, ${b})`;
  }
};

export const RGBToHex = (rgb = [0, 0, 0]) => {
  const hex = [rgb[0].toString(16), rgb[1].toString(16), rgb[2].toString(16)];

  return (
    "#" +
    map(hex, (x) => {
      const y = parseInt(x, 10);
      return (y < 16 ? "0" : "") + y.toString(16);
    }).join("")
  );
};
