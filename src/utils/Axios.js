import axios from "axios";
import config from "/src/config";
// const apiBaseUrl = config.getBaseUrl();
// const BearerToken=config.getAccessToken();
const version = "/api/v1";
const getUrl = (url) => {
  return `${config.getBaseUrl()}${version}${url}`;
};

export const Axios = {
  put: (url, data) => {
    try {
      return axios.put(url, data, {
        headers: {
          Authorization: config.getAccessToken(),
          pcmId: config.getProjectCompanyId(),
        },
        withCredentials: true,
      });
    } catch (error) {
      console.log(error);
    }
  },
  get: (url) => {
    console.log(url, "url", config);
    try {
      return axios.get(url, {
        headers: {
          Authorization: config.getAccessToken(),
          pcmId: config.getProjectCompanyId(),
        },
        withCredentials: true,
      });
    } catch (error) {
      console.log(error);
    }
  },
  post: (url, data) => {
    try {
      return axios.post(url, data, {
        headers: {
          Authorization: config.getAccessToken(),
          pcmId: config.getProjectCompanyId(),
        },
        withCredentials: true,
      });
    } catch (error) {
      console.log(error);
    }
  },
  patch: async (url, data) => {
    try {
      const response = await axios.patch(url, data, {
        headers: {
          Authorization: config.getAccessToken(),
          pcmId: config.getProjectCompanyId(),
        },
        withCredentials: true,
      });
      return response;
    } catch (error) {
      return error.response;
    }
  },
  delete: (url) => {
    // try {
    return axios.delete(url, {
      headers: {
        Authorization: config.getAccessToken(),
        pcmId: config.getProjectCompanyId(),
      },
      withCredentials: true,
    });
    // } catch (error) {
    //   console.log(error,'is this error');
    // }
  },
};

export const urls = {
  createBatch: (psm_id) => {
    return getUrl(`/plan/set/${psm_id}/batch/`);
  },
  setAccessUrl: (obj_id) => {
    // set planset access
    return getUrl(`/shared/object-permission/planset/${obj_id}/`);
  },
  getProjectSets: (prj_id) => {
    return getUrl(`/plan/${prj_id}/sets/`);
  },
  getBatchDetails: (pbt_id) => {
    return getUrl(`/plan/batch/${pbt_id}/`);
  },
  getProjectBatches: (pcm_id) => {
    return getUrl(`/plan/${pcm_id}/batches/`);
  },
  publishBatch: (pbt_id) => {
    return getUrl(`/plan/batch/${pbt_id}/publish/`);
  },
  deleteBatch: (pbt_id) => {
    return getUrl(`/plan/batch/${pbt_id}/delete/`);
  },
  getAllBatchFiles: (pbt_id) => {
    return getUrl(`/plan/batch/${pbt_id}/files/?plf_usable_file=True`);
  },
  getUserUploadedFiles: (pbt_id) => {
    return getUrl(`/plan/batch/${pbt_id}/files/?plf_user_uploaded=True`);
  },
  uploadFilesToBatch: (pbt_id) => {
    return getUrl(`/plan/batch/${pbt_id}/files/`);
  },
  getS3Key: () => {
    return getUrl(`/plan/pre-signed-post/`);
  },
  getBulkS3KeyUpload: () => {
    return getUrl(`/plan/bulk-pre-signed-post/`);
  },
  uploadFilesToS3: (pbt_id) => {
    return getUrl(`/plan/batch/${pbt_id}/files/lambda-upload/ `);
  },
  createNewSet: () => {
    return getUrl(`/plan/set/`);
  },
  getAllFileIds: (prj_id) => {
    return getUrl(`/plan/${prj_id}/files/`);
  },
  processBatchFiles(pbt_id) {
    return getUrl(`/plan/batch/${pbt_id}/process-index/`);
  },
  deletePlfId: (plf_id) => {
    return getUrl(`/plan/file/${plf_id}/delete/`);
  },
  extractSheetCodeGemini: (plf_id) => {
    return getUrl(`/plan/sheet-extract/${plf_id}/`);
  },
  extractSheetCodeGeminiBatch: () => {
    return getUrl(`/plan/bulk-sheets-extract/`);
  },
  markupEdit: (prj_id, plm_id) => {
    return getUrl(`/plan/${prj_id}/markup/edit/${plm_id}/`);
  },
  companyList: (prj_id) => {
    return getUrl(`/project/directory/getCompanies/${prj_id}/`);
  },
  markupAccess: (plm_id) => {
    return getUrl(`/plan/${plm_id}/markup_acl/`);
  },
  projectDetail: () => {
    return getUrl("/project/basic/");
  },
  deleteAccess: (plm_id, macl_id) => {
    return getUrl(`/plan/${plm_id}/markup_acl/${macl_id}`);
  },
  projectTeam: (pcm_id) => {
    return getUrl(`/project/members/${pcm_id}`);
  },
  scaleUrl: () => {
    return getUrl(`/plan/file/detail/`);
  },
  deleteMarkup: (prj_id, plm_id) => {
    return getUrl(`/plan/${prj_id}/markup/delete/${plm_id}/`);
  },
  planData: (prjId, sheetNo) => {
    return getUrl(`/plan/${prjId}/files/?plf_sheet_number=${sheetNo}`);
  },
  markupList: (prjId, plfId) => {
    return getUrl(`/plan/${prjId}/markup/${plfId}/list/`);
  },
  updateMarkup: (prjId, plmId) => {
    return getUrl(`/plan/${prjId}/markup/edit/${plmId}/`);
  },
  addMarkup: (plfId) => {
    return getUrl(`/plan/markup/${plfId}/`);
  },
  setMarkupPrivate: (markupId) => {
    return getUrl(`/shared/object-permission/plan_markup/${markupId}/`);
  },
  preSignedPost: () => {
    return getUrl(`/attachment/pre_signed_post/`);
  },
  patchFileDetails: () => {
    return getUrl(`/plan/file/detail/`);
  },
  getPlanTags: () => {
    return getUrl(`/plan/tags/`);
  },
  preSignedPostGdrive: (redirect_url) => {
    return getUrl(`/integrations/gdrive/oauth/token/?redirect=${redirect_url}`);
  },
  uploadPlanDataGdrive: (pbt_id) => {
    return getUrl(
      `/integrations/selected-files/plans/batch/${pbt_id}/?uploaded_from=drive`
    );
  },
  getAttachedImage: (objType, objId, id) => {
    return getUrl(`/attachment/attached_url/${objType}/${objId}/${id}/`);
  },
  getAttachedImageById: (id) => {
    return getUrl(`/attachment/attached_url/${id}/`);
  },
  getPlanFileDetails: (plf_id) => {
    return getUrl(`/plan/file/${plf_id}/`);
  },
  getSheetCompareImages: (plfId1, plfId2) => {
    return getUrl(`/plan/sheet-compare?sheet1=${plfId1}&sheet2=${plfId2}`);
  },
  getSheetCodeoptions(prjId, query) {
    return getUrl(`/plan/${prjId}/search_files/?search_sheet=${query}`);
  },
  getAllSheetListURL(prjId) {
    return getUrl(`/plan/${prjId}/files/`);
  },
  triggerPlansProcessing(pbt_id) {
    return getUrl(`/plan/plans-processing-trigger/${pbt_id}/`);
  },
};
