import { Axios, urls } from "/src/utils/Axios";
import forEach from "lodash/forEach";
import { normalizeAllFileIds } from "./normalize";
import { AccessTypes } from "../../../constants";
const actions = {
  async setExistingSets({ commit }, { prj_id }) {
    const data = await Axios.get(urls.getProjectSets(prj_id));
    const setIds = [];
    forEach(data.data, (item) => {
      setIds.push(item.psm_id);
    });
    commit("SET_EXISTING_SET", data.data);
    commit("SET_SET_IDS", {
      setIds: setIds,
      prj_id: prj_id,
    });
  },
  setActiveBatch({ commit }, { pbt_id }) {
    commit("SET_ACTIVE_BATCH", { pbt_id: pbt_id });
  },
  setActiveSet({ commit }, { psm_id }) {
    commit("SET_ACTIVE_SET", { psm_id: psm_id });
  },

  async createNewSet({ commit }, obj) {
    const data = await Axios.post(urls.createNewSet(), {
      psm_set_name: obj.setName,
      psm_set_prefix: obj.setPrefix,
      prj_id: obj.prjId,
    });
    commit("CREATE_NEW_SET", data.data);
    return data.data;
  },

  async setPrivateItem(_, { obj_id, prjId }) {
    const data = await Axios.patch(urls.setAccessUrl(obj_id), {
      access: AccessTypes.PUBLIC,
      permission_objects: [
        {
          prj_id: prjId,
        },
      ],
    });
    return data.data;
  },

  async setFileIds({ commit }, { prj_id }) {
    const data = await Axios.get(urls.getAllFileIds(prj_id));
    let newData = normalizeAllFileIds(data.data);
    commit("PlanUploadFile/SET_FILE_IDS", newData, { root: true });
  },

  async setPlanTags({ commit }) {
    let data = await Axios.get(urls.getPlanTags());
    // if (data.data.length === 0)
    //   data = {
    //     data: [
    //       {
    //         tag_id: "AR",
    //         tag_name: "Architect",
    //       },
    //     ],
    //   };
    commit("SET_PLAN_TAGS", { tags: data.data });
  },
};
export default actions;
