// import filter from "lodash/filter";
// import includes from "lodash/includes";
import forEach from "lodash/forEach";
import Vue from "vue";
import isArray from "lodash/isArray";
// import get from "lodash/get";
// import set from "lodash/set";

const mutations = {
  // SET_BATCH_IDS(state, { batchIds, prj_id }) {
  // if (state[prj_id] && state[prj_id].batchIds) {
  //   batchIds = filter(
  //     batchIds,
  //     (val) => !includes(state[prj_id].batchIds, val)
  //   );
  //   state[prj_id].batchIds = [...state[prj_id].batchIds, ...batchIds];
  // } else {
  //   state[prj_id] = { ...state[prj_id], batchIds: batchIds };
  // }
  // },
  SET_BATCH_IDS(state, { batchIds, prj_id }) {
    state[prj_id] = {
      ...state[prj_id],
      batchIds: batchIds,
    };
  },
  SET_SET_IDS(state, { setIds, prj_id }) {
    // if (state[prj_id] && state[prj_id].setIds) {
    //   setIds = filter(setIds, (val) => !includes(state[prj_id].setIds, val));
    //   state[prj_id].setIds = [...state[prj_id].setIds, ...setIds];
    // } else {
    //   state[prj_id] = { ...state[prj_id], setIds: setIds };
    // }
    state[prj_id]= { ...state[prj_id], setIds: [...setIds ]};
  },
  SET_EXISTING_SET(state, setById) {
    const data = {};
    forEach(setById, (item) => {
      data[item.psm_id] = item;
    });
    state.set.byId = {
      ...state.set.byId,
      ...data,
    };
  },
  SET_ACTIVE_BATCH(state, { pbt_id }) {
    state.activeBatch = pbt_id;
  },
  SET_ACTIVE_SET(state, { psm_id }) {
    state.activeSet = psm_id;
  },

  CREATE_NEW_SET(state, data) {
    if (!state[data.prj_id]) {
        Vue.set(state, data.prj_id, { setIds: [] });
    } else if (!isArray(state[data.prj_id].setIds)) {
        Vue.set(state[data.prj_id], 'setIds', []);
    }
    state[data.prj_id].setIds = [...state[data.prj_id].setIds, data.psm_id];
    state.set.byId = { ...state.set.byId, [data.psm_id]: data };
},

  SET_PLAN_TAGS(state, { tags }) {
    state.tags = [...tags];
  },
};

export default mutations;
