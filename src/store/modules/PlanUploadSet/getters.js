import forEach from "lodash/forEach";
import get from "lodash/get";

const getters = {
  getExistingSetList: (state) => (prjId) => {
    let setIds = [];
    setIds = state[prjId].setIds;

    let setList = [];

    forEach(setIds, (key) => {
      let obj = state.set.byId[key];
      setList = [...setList, obj];
    });

    return setList;
  },

  getActiveBatchId: (state) => {
    return get(state, "activeBatch", "");
  },
  getActiveBatchOwnerId: (state,getters) => {
    const currentPlanSetId= getters.getActiveSetId;
    return get(state, `set.byId.${currentPlanSetId}.psm_created_by`, "");
  },
  getActiveSetId: (state) => {
    return get(state, "activeSet", "");
  },

  getBatchIdsbyPrjId: (state) => (prjId) => {
    return state[prjId].batchIds;
  },

  getPlanTags: (state) => {
    const tags = [];
    forEach(state.tags, (item) => {
      tags.push({
        option: item.tag_name,
        value: item.tag_id,
      });
    });
    return tags;
  },
};
export default getters;
