import forEach from "lodash/forEach";
import get from "lodash/get";
const mutations = {
  SET_FILE(state, fileById) {
    const data = {};
    forEach(fileById, (item) => {
      data[item.plf_id] = item;
    });
    
    state.byId = {
      ...state.byId,
      ...data,
    };
  },

  SET_UPLOAD_STATUS(state, uploadStatus) {
    state.uploadStatus = uploadStatus;
  },

  UPDATE_FILE(state, file) {
    state.byId = {
      ...state.byId,
      [file.plf_id]: {
        ...get(state.byId, file.plf_id, {}),
        ...file,
      },
    };
  }

  // SET_FILE_IDS(state, data) {
  //   state.byId = {
  //     ...state.byId,
  //     ...data
  //   }
  // }
};

export default mutations;
