import map from "lodash/map";
import { normalizeUploadingFiles, normalizeUploadedFiles } from "./normalize";
const getters = {
  getUploadingFiles: (state, getters, rootState, rootGetters) => (pbt_id) => {
    let fileIds = rootGetters["PlanUploadBatch/getUploadingFileIds"](pbt_id);
    fileIds = map(fileIds, (item) => {
      return state.byId[item];
    });
    return normalizeUploadingFiles(fileIds);
  },
  // getProcessingFiles: (state, getters, rootState, rootGetters) => (pbt_id) => {
  //   let fileIds = rootGetters['PlanUploadBatch/getUploadingFileIds'](pbt_id);
  //   fileIds = map(fileIds, (item) => {
  //     const file = state.byId[item];
  //     if (file.plf_status !== 'DON') return state.byId[item];
  //   });
  //   return normalizeUploadingFiles(fileIds);
  // },
  getUploadedBatchFiles: (state, getters, rootState, rootGetters) => (pbt_id) => {
    let fileIds = rootGetters["PlanUploadBatch/getBatchFileIds"](pbt_id);
    fileIds = map(fileIds, (item) => {
      return state.byId[item];
    });
    return normalizeUploadedFiles(fileIds);
  },
  getBatchFiles: (state, getters, rootState, rootGetters) => (pbt_id) => {
    let fileIds = rootGetters["PlanUploadBatch/getBatchFileIds"](pbt_id);
    fileIds = map(fileIds, (item) => {
      return state.byId[item];
    });
    return normalizeUploadingFiles(fileIds);
  },
};

export default getters;
