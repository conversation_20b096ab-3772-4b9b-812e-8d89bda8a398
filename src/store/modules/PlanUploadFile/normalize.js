import { urls, Axios } from "/src/utils/Axios";
import forEach from "lodash/forEach";
import keys from "lodash/keys";
import axios from "axios";
import map from "lodash/map";
import get from "lodash/get";

export const uploadFileToS3 = async (item, file) => {
  let formData = new FormData();
  const preSignedResponse = await Axios.post(urls.getS3Key(), {
    s3_key: item.plf_s3_key,
  });

  let s3Fileds = preSignedResponse.data.fields;
  forEach(keys(s3Fileds), (key) => {
    formData.set(key, s3Fileds[key]);
  });
  formData.append("file", file);
  axios.post(preSignedResponse.data.url, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

export const normalizeUploadingFiles = (files) => {
  const mappedFiles = map(files, (file) => {
    return {
      plf_id: file.plf_id,
      plf_file_name: file.plf_uploaded_file_name,
      plf_status: get(UploadStates[file.plf_status], "label", ""),
      plf_uploaded_percentage: get(
        UploadStates[file.plf_status],
        "percentage",
        0
      ),
    };
  });
  // return filter(mappedFiles, (file) => file.plf_status !== "DONE");
  return mappedFiles;
};

export const normalizeUploadedFiles = (files) => {
  const mappedFiles = map(files, (file) => {
    return {
      plf_id: file.plf_id,
      plf_sheet_tag: file.plf_sheet_tag,
      plf_sheet_name: file.plf_sheet_name,
      plf_sheet_number: file.plf_sheet_number,
      plf_sheet_signed_url: file.plf_sheet_signed_url,
      thumbnail_url: file.thumbnail_url,
    };
  });
  return mappedFiles;
};

const UploadStates = {
  PEN: {
    label: "PENDING",
    value: "PEN",
    percentage: 0,
  },
  UPD: {
    label: "UPLOADED",
    value: "UPD",
    percentage: 3,
  },
  PCS: {
    label: "PROCESSING",
    value: "PCS",
    percentage: 6,
  },
  FAL: {
    label: "FAILED",
    value: "FAL",
    percentage: 10,
  },
  DON: {
    label: "DONE",
    value: "DON",
    percentage: 10,
  },
};
