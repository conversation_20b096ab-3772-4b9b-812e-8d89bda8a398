import { Axios, urls } from "/src/utils/Axios";
import forEach from "lodash/forEach";
import filter from "lodash/filter";
import { uploadFileToS3 } from "./normalize";
import debounce from "lodash/debounce";
const actions = {
  async setUploadingBatchFiles({ commit }, { pbt_id }) {
    const data = await Axios.get(urls.getUserUploadedFiles(pbt_id));
    const fileIds = [];
    forEach(data.data, (item) => {
      fileIds.push(item.plf_id);
    });
    commit("SET_FILE", data.data);
    commit(
      "PlanUploadBatch/SET_UPLOADING_FILE_IDS",
      {
        fileIds: fileIds,
        pbt_id: pbt_id,
      },
      {
        root: true,
      }
    );
  },
  async setAllBatchFiles({ commit }, { pbt_id }) {
    const data = await Axios.get(urls.getAllBatchFiles(pbt_id));
    const fileIds = [];
    forEach(data.data, (item) => {
      fileIds.push(item.plf_id);
    });
    commit("SET_FILE", data.data);
    commit(
      "PlanUploadBatch/SET_BATCH_FILE_IDS",
      {
        fileIds: fileIds,
        pbt_id: pbt_id,
      },
      {
        root: true,
      }
    );
  },
  async uploadFilesToBatch({ commit, dispatch }, { pbt_id, file }) {
    const file_names = [];
    forEach(file, (item) => {
      file_names.push(item.name);
    });
    const data = await Axios.post(urls.uploadFilesToBatch(pbt_id), {
      file_names: file_names,
    });
    console.log(commit);
    forEach(data.data, (item, index) => {
      uploadFileToS3(item, file[index]);
    });
    await dispatch("setUploadingBatchFiles", { pbt_id });

    dispatch("syncUploadingFiles", { pbt_id });
  },

  
  // this is the new actions which just created th plsn file entries not s3 upload
  async uploadPlanFilesToBatch(_, { pbt_id, file }) {
    const file_names = [];
    forEach(file, (item) => {
      file_names.push(item.name.replace(/ /g, "_"));
    });
    const data = await Axios.post(urls.uploadFilesToBatch(pbt_id), {
      file_names: file_names,
    });

    return data;
  },

  async triggerPlansProcessing(_, { pbt_id }) {
    const data = await Axios.post(urls.triggerPlansProcessing(pbt_id));
    return data;
  },

  syncUploadingFiles({ dispatch, rootGetters, commit }, { pbt_id }) {
    const processFiles = debounce(async () => {
      Axios.get(urls.processBatchFiles(pbt_id));
      // Update Batch Status after file processing
      const data = await Axios.get(urls.getBatchDetails(pbt_id));
      commit(
        "PlanUploadBatch/UPDATE_BATCH_DATA",
        { pbt_id: pbt_id, data: data.data },
        {
          root: true,
        }
      );
    }, 1100);
    const getAllfiles = () => {
      const uploadingFiles =
        rootGetters["PlanUploadFile/getUploadingFiles"](pbt_id);
      const completedUploads = filter(
        uploadingFiles,
        (item) => item.plf_status === "DONE" || item.plf_status === "FAILED"
      );
      if (completedUploads.length === uploadingFiles.length) {
        clearInterval(updateFilesInIntervals);
        if (uploadingFiles.find((item) => item.plf_status === "FAILED"))
          alert("Failed to upload, please delete and try again");
        else document.getElementById("plansFileUploadContinue").click();
      }
    };
    const updateFilesInIntervals = setInterval(() => {
      if (!document.hidden) {
        dispatch("setUploadingBatchFiles", { pbt_id });
        getAllfiles();
      }
      processFiles();
    }, 20000);
  },
  async patchFileDetails({ commit }, data) {
    try {
      let updatedFile = await Axios.patch(urls.patchFileDetails(), data);
      commit("UPDATE_FILE", updatedFile.data);
    } catch (error) {
      console.log(error);
    }
  },
};
export default actions;
