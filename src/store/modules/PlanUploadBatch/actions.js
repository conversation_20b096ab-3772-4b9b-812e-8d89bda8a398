import { Axios, urls } from "/src/utils/Axios";
import forEach from "lodash/forEach";
import plansConfig from "/src/config";
const actions = {
  async setProjectBatches({ commit }, { prj_id, pcm_id }) {
    const data = await Axios.get(urls.getProjectBatches(pcm_id));
    const batchIds = [];
    forEach(data.data, (item) => {
      batchIds.push(item.pbt_id);
    });
    commit("SET_BATCH", data.data);
    commit(
      "PlanUploadSet/SET_BATCH_IDS",
      { batchIds: batchIds, prj_id: prj_id },
      {
        root: true,
      }
    );
  },
  async createBatch({ commit, dispatch }, { psm_id }) {
    const data = await Axios.post(urls.createBatch(psm_id));
    console.log(commit);
    dispatch("setProjectBatches", { prj_id: data.data.prj_id , pcm_id: plansConfig.getProjectCompanyId()});
    return data.data;
  },

  async publishBatch({ dispatch }, { pbt_id }) {
    const resp = await Axios.patch(urls.publishBatch(pbt_id));
    dispatch("updateBatchDetails", { pbt_id });
    return resp;
  },

  async removeBatchFileId({ commit }, { pbt_id, plf_id }) {
    try {
      const response = await Axios.delete(urls.deletePlfId(plf_id));
      commit("REMOVE_BATCH_FILE_ID", { pbt_id, plf_id });
      return response;
    } catch (error) {
      console.error("Error removing batch file:", error);
      throw error;
    }
      
  },

  async deleteBatch({ dispatch }, { pbt_id, prj_id }) {
    const resp = await Axios.delete(urls.deleteBatch(pbt_id));
    dispatch("setProjectBatches", { prj_id: prj_id , pcm_id: plansConfig.getProjectCompanyId()});
    return resp;
  },

  async processBatchFiles({ commit }, { pbt_id }) {
    await Axios.get(urls.processBatchFiles(pbt_id));
    console.log(commit);
  },
  async updateBatchDetails({ commit }, { pbt_id }) {
    const data = await Axios.get(urls.getBatchDetails(pbt_id));
    commit("UPDATE_BATCH_DATA", { pbt_id: pbt_id, data: data.data });
  },
};

export default actions;
