import moment from "moment";

// import { BATCH_STATUS_PENDING,BATCH_STATUS_PROCESSING,BATCH_STATUS_FAILED, BATCH_STATUS_DONE, BATCH_STATUS_TEXT } from "@/constants";
import { 
  BATCH_STATUS, 
  BATCH_STATUS_TEXT 
} from '@/constants';
export const normalizedDate = (date) => {
  return date;
};

export const normalizedStatus = (status) => {
  switch (status) {
    case BATCH_STATUS.BATCH_STATUS_PENDING:
      return BATCH_STATUS_TEXT["PEN"];
    case BATCH_STATUS.BATCH_STATUS_PROCESSING:
      return BATCH_STATUS_TEXT["PCS"];
    case BATCH_STATUS.BATCH_STATUS_FAILED:
      return BATCH_STATUS_TEXT["FAL"];
    case BATCH_STATUS.BATCH_STATUS_DONE:
      return BATCH_STATUS_TEXT["DON"];
    default:
      return "";
  }
};

export const normalizeBatch = (data) => {
  data.pbt_created_on = moment(data.pbt_created_on).format("MM/DD/YYYY");
  if (data.pbt_status === "PEN") {
    data.pbt_status = BATCH_STATUS_TEXT["PEN"];
    return {
      ...data,
      progress: 4,
      color: "secondary",
    };
  } else if (data.pbt_status === "PCS") {
    data.pbt_status = BATCH_STATUS_TEXT["PCS"];
    return {
      ...data,
      progress: 7,
      color: "baseRoyalBlue",
    };
  } else if (data.pbt_status === "DON") {
    data.pbt_status = BATCH_STATUS_TEXT["DON"];
    return {
      ...data,
      progress: 10,
      color: "baseRoyalBlue",
    };
  } else {
    data.pbt_status = BATCH_STATUS_TEXT["FAL"];
    return {
      ...data,
      progress: 2,
      color: "decline",
    };
  }
};
