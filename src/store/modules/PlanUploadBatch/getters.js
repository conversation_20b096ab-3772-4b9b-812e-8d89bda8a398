import get from "lodash/get";
import { normalizedDate, normalizedStatus, normalizeBatch } from "./normalize";
const getters = {
  getBatchTitle: (state) => (pbt_id) => {
    return get(state.byId, `${pbt_id}.psm_set_name`, "");
  },
  getBatchSetPrefix: (state) => (pbt_id) => {
    return get(state.byId, `${pbt_id}.pbt_set_sequence`, "");
  },
  getBatchSequence: (state) => (pbt_id) => {
    return get(state.byId, `${pbt_id}.pbt_set_sequence`, "");
  },
  getUploadUser: (state) => (pbt_id) => {
    return get(state.byId, `${pbt_id}.pbt_uploaded_by`, "");
  },
  getUploadFullName: (state) => (pbt_id) => {
    return get(state.byId, `${pbt_id}.pbt_uploaded_by_name`, "");
  },
  getBatchStatus: (state) => (pbt_id) => {
    return normalizedStatus(get(state.byId, `${pbt_id}.pbt_status`, ""));
  },
  getBatchLastUpdated: (state) => (pbt_id) => {
    return normalizedDate(
      get(state.byId, `${pbt_id}.pbt_updated_on`, new Date())
    );
  },
  getBatchCreatedDate: (state) => (pbt_id) => {
    return normalizedDate(
      get(state.byId, `${pbt_id}.pbt_created_on`, '')
    );
  },
  getUploadingFileIds: (state) => (pbt_id) => {
    return get(state.byId, `${pbt_id}.uploadingFileIds`, []);
  },
  getBatchFileIds: (state) => (pbt_id) => {
    return get(state.byId, `${pbt_id}.batchFileIds`, []);
  },
  getBatchPublishedStatus: (state) => (pbt_id) => {
    return get(state.byId, `${pbt_id}.pbt_is_published`, false);
  },

  getBatchbyId: (state) => (pbt_id) => {
    return normalizeBatch(state.byId[pbt_id]);
    // return state.byId[pbt_id];
  },
};

export default getters;
