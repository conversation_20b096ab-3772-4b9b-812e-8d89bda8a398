import forEach from "lodash/forEach";
import filter from "lodash/filter";
const mutations = {
  SET_BATCH(state, batchById) {
    const data = {};
    forEach(batchById, (item) => {
      data[item.pbt_id] = item;
      if (state.byId[item.pbt_id]) {
        data[item.pbt_id].uploadingFileIds =
          state.byId[item.pbt_id].uploadingFileIds || [];
      } else {
        data[item.pbt_id].uploadingFileIds = [];
      }
      data[item.pbt_id].batchFileIds = [];
    });
    state.byId = {
      ...state.byId,
      ...data,
    };
  },

  SET_UPLOADING_FILE_IDS(state, { fileIds, pbt_id }) {
    // state.byId[pbt_id].uploadingFileIds = fileIds;
    state.byId[pbt_id] = {
      ...state.byId[pbt_id],
      uploadingFileIds: fileIds,
    };
  },
  SET_BATCH_FILE_IDS(state, { fileIds, pbt_id }) {
    //console.log("SET_BATCH_FILE_IDS", fileIds);
    // state.byId[pbt_id].batchFileIds = [...fileIds];

    state.byId[pbt_id] = {
      ...state.byId[pbt_id],
      batchFileIds: fileIds,
    };
  },
  REMOVE_BATCH_FILE_ID(state, { pbt_id,plf_id }) {

    if (!state.byId[pbt_id] || !state.byId[pbt_id].batchFileIds) {
        console.warn(`Batch ${pbt_id} or its file IDs not found in state`);
        return;
    }
      
    const batchFileIds = filter(state.byId[pbt_id].batchFileIds,
      (id) => id !== plf_id
    );
    state.byId[pbt_id] = {
      ...state.byId[pbt_id],
      batchFileIds,
    };
  },
  UPDATE_BATCH_DATA(state, { pbt_id, data }) {
    state.byId[pbt_id] = {
      ...state.byId[pbt_id],
      ...data,
    };
  },
};

export default mutations;
