/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2022-02-24 14:42:36
 * @modify date 2022-02-24 14:42:36
 * @desc [description]
 */

const state = () => ({
  owner: "",
  empList: null,
  scale: null,
  pcmIdArray: [],
  userIdArray: [],
  selectedCmpIds: [],
  selectedUserIds: [],
  companyDetails: null,
  companyList: [],
  planMarkupPropData: null,
  sheetSequence: [],
  filter: "ALL",
  draftData: null,
  markupMode: "create", // read update create modes
  activePlm_ids: [], // at least one layer is active those markups
  stroke: {
    color: "#f9c150",
    hardness: 7,
  },
  fill: {
    color: "#F9C150",
    hardness: 1,
  },
  text: {
    fontSize: 10,
    fontStyle: "Regular",
    color: "#000000",
  },
  sets: {
    byId: {},
  },
  plan: {
    byId: {},
  },
  planVersion: {
    byId: {},
  },
  planDrafts: {
    byId: {},
  },
  markups: {
    byId: {},
  },
  markupData: {
    normal: [],
    compound_elements: {
      lines: {},
      camera: {},
      ruler_area: {},
      distanceFactor: null,
    },
  },
  readOnly: false,
  selectedMarkup: {},
  selectedMarkupTool: "select",
});

export default state;
