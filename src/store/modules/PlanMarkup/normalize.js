/**
 * <AUTHOR> and <PERSON><PERSON><PERSON>
 * @email <EMAIL>
 * @create date 2022-02-24 14:42:36
 * @modify date 2022-02-24 14:42:36
 * @desc [description]
 */

import keys from "lodash/keys";
import forEach from "lodash/forEach";
import entries from "lodash/entries";
import values from "lodash/values";
import get from "lodash/get";
import map from "lodash/map";
import { DRAFT_MARKUP } from "@/constants";


// import getters from "./getters";

const isObjectEmpty = (obj) => {
  return keys(obj).length === 0;
};

export const normalizePlanData = (data) => {
  // let objKey = entries(data)[0];
  let dataEntries = entries(data);
  let versionObject = dataEntries[0][1];
  const obj = {
    planById: {},
  };
  let versiondArray = [];
  forEach(values(versionObject.versions), (value) => {
    versiondArray.push(value.plf_id);
  });
  obj["planById"]["selected_version"] = versionObject["versions"]["v1"].plf_id;
  obj["planById"]["current_version"] = versionObject["versions"]["v1"].plf_id;
  obj["planById"]["versions"] = versiondArray;
  obj[`planVersionById`] = versionObject.versions;
  return obj;
};

export const normalizeMarkupData = (data) => {
  // console.log(getters.getPlanMarkupPropData(), 'state');
  // const { markupDataById, markupIds } = normalizeData;
  let idsArray = [];
  let obj = {};
  // const
  forEach(data, (markup) => {
    markup["expanded"] = false;
    markup["checked"] = false;
    idsArray.push(markup.plm_id);
    let objects = [];
    if (markup.plm_markup && !isObjectEmpty(markup.plm_markup)) {
      if (markup.plm_markup.normal.length) {
        forEach(markup.plm_markup.normal, (ele) => {
          if (ele.type === "path") {
            objects.push({
              type: ele.layerName,
              data: ele, // ['key', 'value']
              checked: false,
              isActive: false,
            });
          } else {
            objects.push({
              type: ele.type,
              data: ele, // ['key', 'value']
              checked: false,
              isActive: false,
            });
          }
        });
      }
      if (!isObjectEmpty(markup.plm_markup.compound_elements.camera)) {
        forEach(entries(markup.plm_markup.compound_elements.camera), (ele) => {
          objects.push({
            type: "Attachment",
            data: ele, // ['key', 'value']
            checked: false,
          });
        });
      }
      if (!isObjectEmpty(markup.plm_markup.compound_elements.lines)) {
        forEach(entries(markup.plm_markup.compound_elements.lines), (ele) => {
          objects.push({
            type: "Line",
            data: ele, // ['key', 'value']
            checked: false,
          });
        });
      }
      if (!isObjectEmpty(markup.plm_markup.compound_elements.ruler_area)) {
        forEach(
          entries(markup.plm_markup.compound_elements.ruler_area),
          (ele) => {
            objects.push({
              type: "Area",
              data: ele, // ['key', 'value']
              checked: false,
            });
          }
        );
      }
    }
    markup.plm_markup = objects;
    obj[markup.plm_id] = markup;
  });
  return {
    markupIds: idsArray,
    markupDataById: obj,
  };
};

export const normalizeAllPlanData = (data, prjId) => {
  // plan -> byid   // {'prjId:sheetcode': { seleted_version, cureent, versions: ['v1', 'v2'], {} }
  // planVersion -> byid  // {'prjId:sheetcode:sheetversion': {}, '304:0:v1': {}, '304:0:v2': {},  '304:A0.2.2:v1': {}, ..... }
  let keysData = keys(data);
  const plan = {
    byId: {},
  };
  const planVersion = {
    byId: {},
  };

  forEach(keysData, (key) => {
    const versions = keys(data[key].versions);
    const versionData = map(versions, (version) => {
      return {
        plf_id: data[key].versions[version].plf_id,
        name: version,
      };
    });
    const selected_version = {
      plf_id: get(data[key], `versions[${versions[0]}].plf_id`, ""),
      name: versions[0],
    };

    plan.byId[`${prjId}:${key}`] = {
      selected_version: selected_version,
      current_version: get(data[key], `versions[${versions[0]}].plf_id`, ""),
      versions: versionData,
    };

    forEach(keys(data[key].versions), (eachVersion) => {
      let plf_id = data[key].versions[eachVersion].plf_id;

      let date = data[key].versions[eachVersion].plf_updated_on;

      planVersion.byId[plf_id] = {
        ...data[key].versions[eachVersion],
        markupIds: [],
        lastUpdated: date,
      };
    });
  });

  return {
    plan,
    planVersion,
  };
};

// export const normalizePlanListData = (data, getters) => {

//   let entireData = map(entries(data), (element) => {
//     let sheetName = split(element[0], ":")[1];
//     let searchKey = element[1].current_version;
//     console.log("Search Key", searchKey);
//     console.log("last updated value", element[1].lastUpdated);
//     return {
//       planName: sheetName,
//       versions: element[1].versions,
//       thumbnail: getters.getThumbnail(searchKey),
//       lastUpdated: getters.getLastUpdated(searchKey),
//       plfId: searchKey,
//     };
//   });
//   return entireData
// }

export const createDraftMarkup = (data) => {
  let obj = {};
  let markupArray = createMarkupArray(data);
  obj.checked = true;
  obj.expanded = true;
  obj.plm_access = "PR";
  obj.plm_id = "draft101";
  obj.plm_markup = markupArray;
  obj.plm_name = DRAFT_MARKUP.title;
  return obj;
};

export const createUpdateMarkup = (data) => {
  return createMarkupArray(data);
};

const createMarkupArray = (markup) => {
  let objects = [];
  if (markup.normal.length) {
    forEach(markup.normal, (ele) => {
      if (ele.type === "path") {
        objects.push({
          type: ele.layerName,
          data: ele, // ['key', 'value']
          checked: true,
          isActive: false,
        });
      } else {
        objects.push({
          type: ele.type,
          data: ele, // ['key', 'value']
          checked: true,
          isActive: false,
        });
      }
    });
  }
  if (!isObjectEmpty(markup.compound_elements.camera)) {
    forEach(entries(markup.compound_elements.camera), (ele) => {
      objects.push({
        type: "Attachment",
        data: ele, // ['key', 'value']
        checked: true,
      });
    });
  }
  if (!isObjectEmpty(markup.compound_elements.lines)) {
    forEach(entries(markup.compound_elements.lines), (ele) => {
      objects.push({
        type: "Line",
        data: ele, // ['key', 'value']
        checked: true,
      });
    });
  }
  if (!isObjectEmpty(markup.compound_elements.ruler_area)) {
    forEach(entries(markup.compound_elements.ruler_area), (ele) => {
      objects.push({
        type: "Area",
        data: ele, // ['key', 'value']
        checked: true,
      });
    });
  }
  return objects;
};
