/**
 * <AUTHOR> and <PERSON><PERSON><PERSON>
 * @email <EMAIL>
 * @create date 2022-02-24 14:42:36
 * @modify date 2022-02-24 14:42:36
 * @desc [description]
 */

// import keys from 'lodash/keys';
import forEach from "lodash/forEach";
// import config from "/src/config";
import values from "lodash/values";
import map from "lodash/map";

import {
  normalizePlanData,
  normalizeMarkupData,
  normalizeAllPlanData,
  createDraftMarkup,
  normalizeAllDraftsData,
  createUpdateMarkup,
} from "./normalize";
// import { normalizePlanData, normalizeMarkupData, normalizeAllPlanData, createDraftMarkup, normalizeAllDraftsData } from './normalize';
import { Axios, urls } from "/src/utils/Axios";

const actions = {
  async setAllDraftsData({ state, commit }, obj) {
    const responseData = await this.$axios.get(
      `${obj.apiBaseURL}/api/v1/plan/${state.prjId}/batches/`,
      {
        headers: {
          Authorization: obj.apiToken,
        },
      }
    );
    if (responseData.status === 200) {
      let normalizeData = normalizeAllDraftsData(
        responseData.data,
        state.prjId
      );
      commit("SET_ALL_DRAFTS_DATA", normalizeData);
    } else this.$toast.error("Error loading drafts Data");
  },
  // async setAllPlanData({ state, commit }, obj) {
  //   const responseData = await axios.get(
  //     `${obj.apiBaseURL}/api/v1/plan/${state.prjId}/files`,
  //     {
  //       headers: {
  //         Authorization: obj.apiToken,
  //       },
  //     }
  //   );
  //   if (responseData.status === 200) {
  //     let normalizeData = normalizeAllPlanData(responseData.data, state.prjId);
  //     commit("SET_ALL_PLAN_DATA", normalizeData);
  //   } else this.$toast.error("Error loading plans data");
  // },

  async setAllPlanData({ commit }, prj_id) {
    const responseData = await Axios.get(urls.getAllFileIds(prj_id));

    if (responseData.status === 200) {
      let normalizeData = normalizeAllPlanData(responseData.data, prj_id);
      commit("SET_ALL_PLAN_DATA", normalizeData);
    } else this.$toast.error("Error loading plans data");
  },

  // ------- API Actions. --------
  async getAllPlanData({ commit }, obj) {
    const responseData = await Axios.get(urls.getAllFileIds());

    if (responseData.status === 200) {
      let normalizeData = normalizeAllPlanData(responseData.data);
      const { planById, planVersionById } = normalizeData;
      commit("SET_PLAN_DATA", {
        prjId: obj.prjId,
        sheetCode: obj.sheetCode,
        planById,
      });
      commit("SET_PLAN_VERSIONS_DATA", {
        prjId: obj.prjId,
        sheetCode: obj.sheetCode,
        planVersionById,
      });
    } else this.$toast.error("Error loading plans data");
  },
  async getPlanData({ commit, state }, sheetNo) {
    // making it more functional modular
    if (!sheetNo) sheetNo = state.planMarkupPropData.sheetNumber;

    //check if the data already exist
    let searchKey = `${state.planMarkupPropData.prjId}:${sheetNo}`;
    if (state.plan.byId[searchKey]) {
      return;
    } else {
      const responseData = await Axios.get(
        urls.planData(state.planMarkupPropData.prjId, sheetNo)
      );
      if (responseData.status === 200) {
        // console.log(responseData);
        let normalizeData = normalizePlanData(responseData.data);
        const { planById, planVersionById } = normalizeData;
        commit("SET_PLAN_DATA", {
          prjId: state.prjId,
          sheetNumber: sheetNo,
          planById,
        });
        commit("SET_PLAN_VERSIONS_DATA", {
          planVersionById,
        });
      } else this.$toast.error("Error loading plans data");
    }
  },
  async getMarkupListData({ commit, state }, sheetNo) {
    // making it more functional modular
    if (!sheetNo) sheetNo = state.planMarkupPropData.sheetNumber;

    let searchKey = `${state.planMarkupPropData.prjId}:${sheetNo}`;
    let plfId = state.plan.byId[searchKey].current_version;
    if (!state.planVersion.byId[plfId].markupIds.length) {
      const resp = await Axios.get(
        urls.markupList(state.planMarkupPropData.prjId, plfId)
      );
      if (resp.status === 200) {
        let normalizeData = normalizeMarkupData(resp.data);
        const { markupDataById, markupIds } = normalizeData;
        commit("SET_MARKUP_DATA", markupDataById);
        commit("SET_MARKUP_IDS", {
          plfId,
          val: markupIds,
        });
      } else {
        this.$toast.error("Error Came while loading");
      }
    }
  },
  setDraftData({ state, commit }, data) {
    let val = null;
    if (state.markupMode === "create") {
      val = createDraftMarkup(data);
      commit("SET_DRAFT_MARKUP", val);
      // commit('SET_DRAFT_DATA', data);
    } else if (state.markupMode === "update") {
      val = createUpdateMarkup(data);
      commit("UPDATE_MARKUP_DATA", val);
    }
  },
  setPlfId({ commit }, plf_id) {
    commit("SET_PLF_ID", plf_id);
  },
  setMarkupMode({ commit }, mode) {
    commit("SET_MODE", mode);
  },
  setStroke({ commit }, stroke) {
    commit("SET_STROKE", stroke);
  },
  setFill({ commit }, fill) {
    commit("SET_FILL", fill);
  },
  setText({ commit }, text) {
    commit("SET_TEXT", text);
  },
  setSheetNumber({ commit }, sheetCode) {
    commit("SET_SHEET_NUMBER", sheetCode);
  },
  setProjectId({ commit }, prjId) {
    // console.log("prj_id is coming", prjId);
    commit("SET_PROJECT_ID", prjId);
  },
  setMarkupExpand({ commit }, id) {
    commit("SET_MARKUP_EXPAND", id);
  },
  unsetMarkupExpand({ commit }, id) {
    commit("UNSET_MARKUP_EXPAND", id);
  },
  checkMarkup({ commit, dispatch }, id) {
    commit("CHECK_MARKUP", id);
    dispatch("setMarkupData");
  },
  unCheckMarkup({ commit, dispatch }, id) {
    commit("UNCHECK_MARKUP", id);
    dispatch("setMarkupData");
  },
  checkLayer({ commit, dispatch }, val) {
    commit("CHECK_LAYER", val);
    dispatch("setMarkupData");
  },
  unCheckLayer({ commit, dispatch }, val) {
    commit("UNCHECK_LAYER", val);
    dispatch("setMarkupData");
  },
  setMarkupData({ state, commit }) {
    let obj = {
      normal: [],
      compound_elements: {
        lines: {},
        camera: {},
        ruler_area: {},
        distanceFactor: null,
      },
    };
    forEach(values(state.markups.byId), (markup) => {
      forEach(values(markup.plm_markup), (layer) => {
        if (layer.checked) {
          if (
            layer.type === "HighLighter" ||
            layer.type === "Arrow" ||
            layer.type === "Pen" ||
            layer.type === "rect" ||
            layer.type === "circle" ||
            layer.type === "i-text"
          ) {
            obj.normal.push(layer.data);
          } else if (layer.type === "Attachment") {
            obj.compound_elements.camera[`${layer.data[0]}`] = layer.data[1];
          } else if (layer.type === "Line") {
            obj.compound_elements.lines[`${layer.data[0]}`] = layer.data[1];
          } else if (layer.type === "Area") {
            obj.compound_elements.ruler_area[`${layer.data[0]}`] =
              layer.data[1];
          }
        }
      });
    });
    commit("SET_MARKUP_PROP_DATA", obj);
  },

  // Some Initial Actions

  setPlanMarkupPropData({ commit }, data) {
    commit("SET_PLAN_MARKUP_PROP_DATA", data);
  },

  async updateMarkup({ commit, state }, formdata) {
    const resp = await Axios.put(
      urls.updateMarkup(state.planMarkupPropData.prjId, state.activePlm_ids[0]),
      formdata
    );
    if (resp.status === 200) {
      let val = createUpdateMarkup(resp.data.plm_markup);
      commit("UPDATE_MARKUP", {
        name: resp.data.plm_name,
        data: val,
      });
    }
  },

  // for changing the draft
  changeDraft({ commit }, data) {
    commit("CHANGE_DRAFT", data);
  },

  // for setting up the filter value
  setFilter({ commit }, data) {
    commit("SET_FILTER", data);
  },

  //  addind sheet sequence
  addSheetSequence({ commit }, sheetNumber) {
    commit("ADD_SHEET_SEQUENCE", sheetNumber);
  },
  removeTopSheet({ commit }) {
    commit("REMOVE_TOP_SHEET");
  },
  async deletePlansFile({ commit }, plf_id) {
    await Axios.delete(urls.deletePlfId(plf_id));
    commit("DELETE_FILE_ID", plf_id);
  },
  async deletePlansFileVersion(_, plf_id) {
    await Axios.delete(urls.deletePlfId(plf_id));
    // commit("DELETE_FILE_ID", plf_id);
  },
  async extractSheetCodeGemini(_, { plf_id, image_url }) {
    await Axios.post(urls.extractSheetCodeGemini(plf_id), {
      image_url,
    });
  },
  async extractSheetCodeGeminiBatch(_, data) {
    await Axios.post(urls.extractSheetCodeGeminiBatch(), data);
  },
  // for markups
  async editMarkup({ state, commit }, body) {
    body.prj_id = state.planMarkupPropData.prjId;
    if (state.markupMode === "update") {
      let plfId = state.activePlm_ids[0];
      const data = await Axios.put(
        urls.markupEdit(state.planMarkupPropData.prjId, plfId),
        body
      );
      //have to update markup here
      // console.log(data);
      commit("CHANGE_ACCESS_TYPE", data.data);
      return data;
    } else if (state.markupMode === "create") {
      this.$toast.error("Markup need to be saved first");
    } else {
      this.$toast.error("Select At Least One Saved Markup To Change Access");
    }
  },
  async editMarkupTable({ state }, body) {
    body.prj_id = state.planMarkupPropData.prjId;
    let plfId = state.activePlm_ids[0];
    return await Axios.put(
      urls.markupEdit(state.planMarkupPropData.prjId, plfId),
      body
    );
  },
  async getCompanyList({ commit, state }) {
    if (state.markupMode === "update") {
      // let plm_id = state.activePlm_ids[0];
      const data = await Axios.get(
        urls.companyList(state.planMarkupPropData.prjId)
      );
      //have to update markup here
      commit("SET_COMPANY_LIST", data.data.Active_Companies);
    } else if (state.markupMode === "create") {
      this.$toast.error("Markup need to be saved first");
    } else {
      this.$toast.error("Select At Least One Saved Markup To Change Access");
    }
  },
  async getMarkupAcl({ state, commit }) {
    //get the details of cmp_id/user_id having access & push them to diff arrays
    let plm_id = state.activePlm_ids[0];
    let selectedCmpIds = [];
    let selectedUserIds = [];
    let owner = "";
    const response = await Axios.get(urls.markupAccess(plm_id));

    this.markup_acl = response.data;
    map(this.markup_acl, (item) => {
      // console.log(item, 'itemsdkjfs');
      if (item.user_id !== null) {
        selectedUserIds.push(item);
      } else if (item.pcm_id !== null) {
        selectedCmpIds.push(item);
      }
      if (item.macl_permission === "OWNER") {
        owner = item.user_id;
      }
    });
    let userIdArray = map(selectedUserIds, (item) => {
      return item.user_id;
    });
    // console.log('1', this.selectedCmpIds);
    let pcmIdArray = map(selectedCmpIds, (item) => {
      // console.log('2');
      return item.pcm_id;
    });
    commit("SET_OWNER", owner);
    commit("PCM_ID_ARRAY", pcmIdArray);
    commit("USER_ID_ARRAY", userIdArray);
    commit("SELECTED_CMP_IDS", selectedCmpIds);
    commit("SELECTED_USER_ID", selectedUserIds);
  },
  async getCompanyBasic({ commit }) {
    const data = await Axios.get(urls.projectDetail());
    const companydetails = data.data.company_sub_type;
    const val = {};
    forEach(companydetails, (obj) => {
      val[obj.cst_code] = obj.cst_name;
    });
    //  console.log(val, 'thee are the com details');
    commit("SET_COMPANY_DETAILS", val);
  },
  async deleteMarkupAccess({ state }, macl_id) {
    let plm_id = state.activePlm_ids[0];
    return await Axios.delete(urls.deleteAccess(plm_id, macl_id));
  },
  async postCompanyAccess({ state }, payload) {
    let plm_id = state.activePlm_ids[0];
    return await Axios.post(urls.markupAccess(plm_id), payload);
  },
  async getEmplyeeList({ state, commit }) {
    const data = await Axios.get(
      urls.projectTeam(state.planMarkupPropData.pcmId)
    );
    commit("SET_EMPLOYEE_LIST", data.data);
  },
  async setScaleFactor({ commit, state }, obj) {
    obj.plf_id = state.planMarkupPropData.plfId;
    await Axios.patch(urls.scaleUrl(), obj);
    commit("SET_SCALE_FACTOR", obj.plf_sheet_scale_factor);
  },
  async deletePlmMarkup({ commit, state }, plm_id) {
    if (plm_id === "draft101") {
      commit("UPDATE_AFTER_DELETE", plm_id);
      return true;
    } else {
      const res = await Axios.delete(
        urls.deleteMarkup(state.planMarkupPropData.prjId, plm_id)
      );
      commit("UPDATE_AFTER_DELETE", plm_id);
      if (res.status === 204) return true;
    }
  },
  undoPlmMarkup({ commit }, plm_id) {
    commit("UPDATE_AFTER_UNDO", plm_id);
  },
  redoPlmMarkup({ commit }, plm_id) {
    commit("UPDATE_AFTER_REDO", plm_id);
  },
  deletePlanMarkupDraft({ commit }, id) {
    commit("DELETE_MARKUP_DRAFT", id);
  },
  resetState({ commit }) {
    commit("RESET_STATE");
  },
  setDataForMarkupTable({ commit }, data) {
    commit("SET_DATA_FOR_MARKUP_TABLE", data);
  },
  setSelectedVersion({ commit }, version) {
    commit("SET_SELECTED_VERSION", version);
  },
  setSelectedMarkup({ commit }, markup) {
    commit("SET_SELECTED_MARKUP", markup);
  },
  setSelectedMarkupTool({ commit }, markupTool) {
    commit("SET_SELECTED_MARKUP_TOOL", markupTool);
  },
};

export default actions;
