/**
 * <AUTHOR> and <PERSON><PERSON><PERSON>
 * @email <EMAIL>
 * @create date 2022-02-24 14:42:36
 * @modify date 2022-02-24 14:42:36
 * @desc [description]
 */

import filter from "lodash/filter";
import map from "lodash/map";
import values from "lodash/values";
import split from "lodash/split";
import get from "lodash/get";
import entries from "lodash/entries";
import keys from "lodash/keys";

const getters = {
  //  import { normalizePlanListData } from './normalize';
  getAllPlans: (state, getters) => {
    let entireData = map(entries(state.plan.byId), (element) => {
      let sheetName = split(element[0], ":")[1];
      let searchKey = element[1].current_version;
      return {
        planName: sheetName,
        versions: element[1].versions,
        thumbnail: getters.getThumbnail(searchKey),
        lastUpdated: getters.getLastUpdated(searchKey),
        sheetTag: getters.getSheetTag(searchKey),
        plfId: searchKey,
      };
    });
    return entireData;
  },
  getAllVersion: (state) => {
    // let searchKey = `${get(state, "planMarkupPropData.prjId")}:${get(
    //   state,
    //   "planMarkupPropData.sheetNumber"
    // )}`;
    // return get(state, `plan.byId[${searchKey}].versions`);

    //use lodash get

    console.log(
      "getters.getAllVersion",
      state.planMarkupPropData.prjId,
      state.planMarkupPropData.sheetNumber,
      state.plan.byId
    );

    let searchKey = `${state.planMarkupPropData.prjId}:${state.planMarkupPropData.sheetNumber}`;
    return get(state, `plan.byId[${searchKey}].versions`, []);
  },
  getSelectedVersion: (state) => {
    let searchKey = `${get(state, "planMarkupPropData.prjId")}:${get(
      state,
      "planMarkupPropData.sheetNumber"
    )}`;
    const plan = get(state, `plan.byId[${searchKey}]`);
    return get(plan, `selected_version`, undefined);
    // let searchKey = `${state.planMarkupPropData.prjId}:${state.planMarkupPropData.sheetNumber}`;
    // return get(state,`plan.byId[${searchKey}].selected_version`);
  },
  getMarkupList: (state) => (plm_id) => state.markups.byId[plm_id],
  getMarkupPropData: (state) => state.markupData,
  getStroke: (state) => state.stroke,
  getFill: (state) => state.fill,
  getMode: (state) => state.markupMode,
  getText: (state) => state.text,
  getActivePlmIdsLength: (state) => state.activePlm_ids.length,
  getProjectId: (state) => {
    return state.prjId;
  },
  getThumbnail: (state) => (searchKey) => {
    return get(state.planVersion.byId, `${searchKey}.thumbnail_url`, "");
  },
  getDrafts: (state) => {
    return state.planDrafts.byId;
  },
  getExistingSetList: (state) => {
    return state.sets.byId;
  },
  getCurrentSheetData: (state) => {
    // let sheetNo = state.sheetSequence[0];

    // let searchKey = `${state.planMarkupPropData.prjId}:${sheetNo}`;

    // let plfId = "";
    // if (state.plan.byId[searchKey])
    //   plfId = state.plan.byId[searchKey].current_version;
    // return state.planVersion.byId[plfId];

    //lodash get method

    let sheetNo = get(state, `sheetSequence[0]`, "");
    let searchKey = `${get(state, "planMarkupPropData.prjId")}:${sheetNo}`;
    let plfId = "";
    if (state.plan.byId[searchKey])
      plfId = state.plan.byId[searchKey].current_version;
    return get(state, `planVersion.byId[${plfId}]`, {});
  },
  getSheetUrl: (state) => (prjId, sheetNumber) => {
    let searchKey = `${prjId}:${sheetNumber}`;
    let plfId = "";
    if (state.plan.byId[searchKey])
      plfId = state.plan.byId[searchKey].current_version;
    return get(state, `planVersion.byId[${plfId}].plf_sheet_signed_url`, {});
  },

  getSheetVersion: (state) => (prjId, sheetNumber) => {
    let searchKey = `${prjId}:${sheetNumber}`;
    let version = null;
    if (state.plan.byId[searchKey])
      version = state.plan.byId[searchKey].selected_version;
    return version;
  },

  getPlanMarkupPropData: (state) => state.planMarkupPropData,
  getTagName: (state) => {
    if (state.planMarkupPropData) {
      if (state.planMarkupPropData.objType === "rfiquestions") {
        return "RFI";
      } else if (state.planMarkupPropData.objType === "changeorder") {
        return "Change Order";
      } else if (state.planMarkupPropData.objType === "punchlistitem") {
        return "Punch List";
      } else if (state.planMarkupPropData.objType === "submittalregistry") {
        return "Submittal";
      }
    } else {
      return "PLAN";
    }
  },
  getFilterValue: (state) => state.filter,
  getSheetSequenceArray: (state) => state.sheetSequence,
  getFilterMarkupList: (state) => {
    let sheetNo = state.sheetSequence[state.sheetSequence.length - 1];
    let searchKey = `${state.planMarkupPropData.prjId}:${sheetNo}`;

    let plfId = "";
    if (state.plan.byId[searchKey])
      plfId = state.plan.byId[searchKey].current_version;

    let arr = values(state.markups.byId);
    // console.log(arr, 'arr');
    if (state.filter === "PLAN") {
      return filter(
        arr,
        (ele) => ele.app_model_name === "plansfile" && ele.plf_id === plfId
      );
    } else if (state.filter === "RFI") {
      return filter(
        arr,
        (ele) => ele.app_model_name === "rfiquestions" && ele.plf_id === plfId
      );
    } else if (state.filter === "Change Order") {
      return filter(
        arr,
        (ele) => ele.app_model_name === "changeorder" && ele.plf_id === plfId
      );
    } else if (state.filter === "Punch List") {
      return filter(
        arr,
        (ele) => ele.app_model_name === "punchlistitem" && ele.plf_id === plfId
      );
    } else if (state.filter === "Submittal") {
      return filter(
        arr,
        (ele) =>
          ele.app_model_name === "submittalregistry" && ele.plf_id === plfId
      );
    } else {
      return filter(arr, (ele) => ele.plf_id === plfId);
    }
  },
  getPlfId: (state) => {
    let sheetNo = state.sheetSequence[state.sheetSequence.length - 1];
    let searchKey = `${state.planMarkupPropData.prjId}:${sheetNo}`;
    return state.plan.byId[searchKey].current_version;
  },
  getLastUpdated: (state) => (searchKey) => {
    return get(state.planVersion.byId, `${searchKey}.lastUpdated`, "");
  },
  getSheetTag: (state) => (searchKey) => {
    return get(state.planVersion.byId, `${searchKey}.plf_sheet_tag`, "");
  },

  getCompList: (state) => state.companyList,
  getCurrentAccess: (state) => {
    if (state.activePlm_ids.length) {
      let plm_id = state.activePlm_ids[0];
      return state.markups.byId[plm_id].plm_access;
    }
  },
  getPlm: (state) => state.activePlm_ids[0],
  getCompanyDetails: (state) => state.companyDetails,
  getPcmIdArray: (state) => state.pcmIdArray,
  getUserIdArray: (state) => state.userIdArray,
  getSelectedCmpIds: (state) => state.selectedCmpIds,
  getSelectedUserIds: (state) => state.selectedUserIds,
  getEmpList: (state) => state.empList,
  getOwner: (state) => state.owner,
  getScale: (state) => state.scale,
  isDraftChecked: (state) => {
    if (state.markups.byId["draft101"]) {
      return state.markups.byId["draft101"].checked;
    } else {
      return false;
    }
  },
  getMarkupsFromId: (state) => (plm_id) => {
    return get(state.markups.byId, `${plm_id}.plm_markup`, []);
  },
  isMarkupExpand: (state) => (plm_id) => {
    return get(state.markups.byId, `${plm_id}.expanded`, true);
  },
  isMarkupSelected: (state) => (plm_id) => {
    // if(state.planMarkupPropData.plmId === plm_id) return true;
    return get(state.markups.byId, `${plm_id}.checked`, false);
  },
  getreadOnly: (state) => {
    return state.readOnly;
  },
  getSelectedMarkupId: (state) => {
    return get(state.selectedMarkup, "plm_markup.data.id", null);
  },
  getSelectedMarkupTool: (state) => {
    return get(state, "selectedMarkupTool", null);
  },
  getMarkupIdsByType: (state) => (type) => {
    const planMarkups = state.markups.byId;
    const markupids = keys(planMarkups);
    const filteredMarkupIds = filter(
      markupids,
      (id) => planMarkups[id].app_model_name === type || type === "all"
    );
    const markupObject = {};
    for (let i = 0; i < filteredMarkupIds.length; i++) {
      markupObject[filteredMarkupIds[i]] = planMarkups[filteredMarkupIds[i]];
    }
    return markupObject;
  },
};

export default getters;
