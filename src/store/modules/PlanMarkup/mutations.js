/**
 * <AUTHOR> and <PERSON><PERSON><PERSON>
 * @email <EMAIL>
 * @create date 2022-02-24 14:42:36
 * @modify date 2022-02-24 14:42:36
 * @desc [description]
 */

//  import keys from 'lodash/keys';
import values from "lodash/values";
import forEach from "lodash/forEach";
import includes from "lodash/includes";
import get from "lodash/get";
import Vue from "vue";
import constants from "@/constants/index";
import findIndex from "lodash/findIndex";

// import { identity } from 'lodash';

let undoRedoBuffer = [];
const mutations = {
  SET_PLAN_DATA(state, obj) {
    let keyName = `${state.planMarkupPropData.prjId}:${obj.sheetNumber}`;
    state.plan.byId[keyName] = obj.planById;
  },
  SET_PLAN_VERSIONS_DATA(state, obj) {
    let valueArray = values(obj.planVersionById);
    // let keyName = '';
    forEach(valueArray, (value) => {
      // keyName = `${obj.prjId}:${obj.sheetCode}:${key}`;
      value["markupIds"] = [];
      if (value.plf_id === state.planMarkupPropData.plfId) {
        state.scale = value.plf_sheet_scale_factor;
      }
      state.planVersion.byId[value.plf_id] = value;
    });
  },
  SET_PLF_ID(state, val) {
    state.plfId = val;
  },
  SET_SHEET_NUMBER(state, val) {
    state.sheetNumber = val;
  },
  SET_PROJECT_ID(state, val) {
    state.prjId = val;
  },
  SET_MARKUP_DATA(state, val) {
    state.markups.byId = { ...state.markups.byId, ...val };
  },
  SET_MARKUP_IDS(state, obj) {
    state.planVersion.byId[obj.plfId].markupIds = obj.val;
  },
  SET_MARKUP_EXPAND(state, val) {
    Vue.set(
      state.markups.byId[val],
      "expanded",
      !state.markups.byId[val]["expanded"]
    );
  },
  UNSET_MARKUP_EXPAND(state, val) {
    state.markups.byId[val].expanded = false;
  },
  CHECK_MARKUP(state, val) {
    state.markups.byId[val].checked = true;
    forEach(state.markups.byId[val].plm_markup, (layer) => {
      layer.checked = true;
    });
    if (!includes(state.activePlm_ids, val)) state.activePlm_ids.push(val);
    if (state.activePlm_ids.length === 0) {
      state.markupMode = constants.MARKUP_MODES.CREATE;
    } else if (state.activePlm_ids.length === 1) {
      if (state.activePlm_ids[0] === constants.DRAFT_MARKUP_ID) {
        state.markupMode = constants.MARKUP_MODES.CREATE;
      } else state.markupMode = constants.MARKUP_MODES.UPDATE;
    } else {
      state.markupMode = constants.MARKUP_MODES.READ;
    }
  },
  UNCHECK_MARKUP(state, val) {
    // Vue.set(state.markups.byId[val].checked, constants.DRAFT_MARKUP_ID, val)
    state.markups.byId[val].checked = false;
    forEach(state.markups.byId[val].plm_markup, (layer) => {
      layer.checked = false;
    });
    var i = state.activePlm_ids.indexOf(val);
    state.activePlm_ids.splice(i, 1);
    if (state.activePlm_ids.length === 0) {
      let obj = {
        normal: [],
        compound_elements: {
          lines: {},
          camera: {},
          ruler_area: {},
          distanceFactor: null,
        },
      };
      Vue.set(state, "markupData", obj);
      state.markupMode = constants.MARKUP_MODES.CREATE;
    } else if (state.activePlm_ids.length === 1) {
      if (state.activePlm_ids[0] === constants.DRAFT_MARKUP_ID) {
        state.markupMode = constants.MARKUP_MODES.CREATE;
      } else state.markupMode = constants.MARKUP_MODES.UPDATE;
    } else {
      state.markupMode = constants.MARKUP_MODES.READ;
    }
  },
  CHECK_LAYER(state, obj) {
    state.markups.byId[obj.plm_id].plm_markup[obj.index].checked = true;
    state.markups.byId[obj.plm_id].checked = true;
    if (!includes(state.activePlm_ids, obj.plm_id))
      state.activePlm_ids.push(obj.plm_id);
    if (state.activePlm_ids.length === 0) {
      state.markupMode = constants.MARKUP_MODES.CREATE;
    } else if (state.activePlm_ids.length === 1) {
      if (state.activePlm_ids[0] === constants.DRAFT_MARKUP_ID) {
        state.markupMode = constants.MARKUP_MODES.CREATE;
      } else state.markupMode = constants.MARKUP_MODES.UPDATE;
    } else {
      state.markupMode = constants.MARKUP_MODES.READ;
    }
  },
  UNCHECK_LAYER(state, obj) {
    state.markups.byId[obj.plm_id].plm_markup[obj.index].checked = false;
    var f = 1;
    var arr = state.markups.byId[obj.plm_id].plm_markup;
    var l = state.markups.byId[obj.plm_id].plm_markup.length;
    for (let i = 0; i < l; i++) {
      if (arr[i].checked) {
        f = 0;
        break;
      }
    }
    if (f) {
      state.markups.byId[obj.plm_id].checked = false;
      var i = state.activePlm_ids.indexOf(obj.plm_id);
      state.activePlm_ids.splice(i, 1);
    }
    if (state.activePlm_ids.length === 0) {
      state.markupMode = constants.MARKUP_MODES.CREATE;
    } else if (state.activePlm_ids.length === 1) {
      if (state.activePlm_ids[0] === constants.DRAFT_MARKUP_ID) {
        state.markupMode = constants.MARKUP_MODES.CREATE;
      } else state.markupMode = constants.MARKUP_MODES.UPDATE;
    } else {
      state.markupMode = constants.MARKUP_MODES.READ;
    }
  },
  SET_MARKUP_PROP_DATA(state, obj) {
    state.markupData = obj;
  },
  SET_STROKE(state, obj) {
    if (obj.hardness) state.stroke.hardness = obj.hardness;
    if (obj.color) state.stroke.color = obj.color;
  },
  SET_FILL(state, obj) {
    if (obj.hardness) state.fill.hardness = obj.hardness;
    if (obj.color) state.fill.color = obj.color;
  },
  SET_MODE(state, mode) {
    state.markupMode = mode;
  },
  SET_TEXT(state, obj) {
    state.text = obj;
  },
  SET_DRAFT_DATA(state, data) {
    state.draftData = data;
  },
  SET_DRAFT_MARKUP(state, val) {
    let sheetNo = state.sheetSequence[state.sheetSequence.length - 1];
    let searchKey = `${state.planMarkupPropData.prjId}:${sheetNo}`;

    let plfId = state.plan.byId[searchKey].current_version;
    if (
      !includes(
        state.planVersion.byId[plfId].markupIds,
        constants.DRAFT_MARKUP_ID
      )
    ) {
      state.planVersion.byId[plfId].markupIds.push(constants.DRAFT_MARKUP_ID);
      val.app_model_name = state.planMarkupPropData.objType;
      val.plf_id = plfId;
      Vue.set(state.markups.byId, constants.DRAFT_MARKUP_ID, val);
    } else {
      // Vue.set(state.markups.byId[constants.DRAFT_MARKUP_ID], "plm_markup", [
      //   ...val.plm_markup,
      // ]);
      state.markups.byId[val.plm_id] = {
        ...state.markups.byId[val.plm_id],
        ...val,
      };
    }
  },
  SET_ALL_PLAN_DATA(state, val) {
    state.plan.byId = val.plan.byId;
    state.planVersion.byId = val.planVersion.byId;
  },
  SET_ALL_DRAFTS_DATA(state, val) {
    state.planDrafts.byId = val.byId;
  },
  SET_EXISTING_SET_API(state, val) {
    state.sets.byId = val;
  },
  SET_NEW_SET(state, val) {
    let prj_id = val.prjId;
    state.sets.byId[`${prj_id}`].push(val);
  },

  // Initial mutations
  SET_PLAN_MARKUP_PROP_DATA(state, data) {
    state.planMarkupPropData = data;
  },
  //changing draft mutation
  CHANGE_DRAFT(state, data) {
    let sheetNo = state.sheetSequence[state.sheetSequence.length - 1];
    let searchKey = `${state.planMarkupPropData.prjId}:${sheetNo}`;

    let plfId = state.plan.byId[searchKey].current_version;
    let arr = state.planVersion.byId[plfId].markupIds;
    let ind = arr.indexOf(constants.DRAFT_MARKUP_ID);
    arr.splice(ind, 1);
    arr.push(data.plm_id);
    let val = state.markups.byId[constants.DRAFT_MARKUP_ID];
    val.plm_name = data.plm_name;
    val.plm_id = data.plm_id;

    // for converting it to a update mode
    let i = state.activePlm_ids.indexOf(constants.DRAFT_MARKUP_ID);
    state.activePlm_ids.splice(i, 1);
    state.activePlm_ids.push(data.plm_id);
    state.markupMode = constants.MARKUP_MODES.UPDATE;
    delete state.markups.byId.draft101;
    Vue.set(state.markups.byId, data.plm_id, val);
  },
  UPDATE_MARKUP_DATA(state, data) {
    let plm_id = state.activePlm_ids[0];
    let val = state.markups.byId[plm_id];
    val.plm_markup = data;
    if (state.markupMode === constants.MARKUP_MODES.UPDATE) {
      Vue.set(state.markups.byId, plm_id, val);
    }
  },
  UPDATE_MARKUP(state, obj) {
    let plm_id = state.activePlm_ids[0];
    let val = state.markups.byId[plm_id];
    val.plm_markup = obj.data;
    val.plm_name = obj.name;
    val.app_model_name = state.planMarkupPropData.objType;
    if (state.markupMode === constants.MARKUP_MODES.UPDATE) {
      Vue.set(state.markups.byId, plm_id, val);
    }
  },
  SET_FILTER(state, data) {
    state.filter = data;
  },
  ADD_SHEET_SEQUENCE(state, sheetNumber) {
    state.sheetSequence.push(sheetNumber);
  },
  REMOVE_TOP_SHEET(state) {
    state.sheetSequence.splice(-1, 1);
  },
  DELETE_FILE_ID(state, val) {
    let prjId = state.prjId;
    let sheetName = state.planVersion.byId[val].plf_sheet_name;
    /* eslint-disable no-unused-vars */
    const { [`${prjId}:${sheetName}`]: removedObject, ...newPlanObj } =
      state.plan.byId;
    const { [val]: removedVersion, ...newVersionsObj } = state.planVersion.byId;
    /* eslint-disable no-unused-vars */
    state.plan.byId = newPlanObj;
    state.planVersion.byId = newVersionsObj;
  },
  SET_COMPANY_LIST(state, data) {
    state.companyList = data;
  },
  SET_COMPANY_DETAILS(state, data) {
    state.companyDetails = data;
  },
  SET_ACL_DATA(state, data) {
    state.aclData = data;
  },
  PCM_ID_ARRAY(state, data) {
    state.pcmIdArray = data;
  },
  USER_ID_ARRAY(state, data) {
    state.userIdArray = data;
  },
  SELECTED_CMP_IDS(state, data) {
    state.selectedCmpIds = data;
  },
  SELECTED_USER_ID(state, data) {
    state.selectedUserIds = data;
  },
  SET_EMPLOYEE_LIST(state, data) {
    state.empList = data;
  },
  SET_OWNER(state, data) {
    state.owner = data;
  },
  CHANGE_ACCESS_TYPE(state, data) {
    let plm_id = state.activePlm_ids[0];
    let val = state.markups.byId[plm_id];
    val.plm_access = data.plm_access;
    Vue.set(state.markups.byId, plm_id, val);
  },
  SET_SCALE_FACTOR(state, data) {
    state.scale = data;
  },
  UPDATE_AFTER_DELETE(state, plm_id) {
    Vue.delete(state.markups.byId, plm_id);
    // delete state.markups.byId.plm_id;
  },
  UPDATE_AFTER_UNDO(state, plm_id) {
    const markUps = get(state.markups.byId, plm_id);
    if (markUps.plm_markup)
      if (markUps.plm_markup.length > 0) {
        undoRedoBuffer.push(markUps.plm_markup[markUps.plm_markup.length - 1]);
        markUps.plm_markup.pop();
        Vue.set(state.markups.byId, plm_id, markUps);
      }
    // else if (markUps.plm_markup.length === 0) {
    //   Vue.delete(state.markups.byId, plm_id);
    // }
  },
  DELETE_MARKUP_DRAFT(state, id) {
    const draftObjects = get(state.markups.byId, constants.DRAFT_MARKUP_ID);
    if (draftObjects.plm_markup) {
      const draftObject = findIndex(
        draftObjects.plm_markup,
        (obj) => obj.data.id === id
      );
      if (draftObject !== -1) {
        draftObjects.plm_markup.splice(draftObject, 1);
        Vue.set(state.markups.byId, constants.DRAFT_MARKUP_ID, draftObjects);
      }
    }
  },
  UPDATE_AFTER_REDO(state, plm_id) {
    const markUps = get(state.markups.byId, plm_id);
    if (markUps.plm_markup)
      if (undoRedoBuffer.length) {
        markUps.plm_markup.push(undoRedoBuffer[undoRedoBuffer.length - 1]);
        undoRedoBuffer.pop();
        Vue.set(state.markups.byId, plm_id, markUps);
      }
  },
  SET_READ_ACCESS(state, access) {
    state.readOnly = access;
  },
  RESET_STATE(state) {
    state.planMarkupPropData = null;
    state.filter = "ALL";
    state.draftData = null;
    state.markupMode = "create"; // read update create modes
    state.activePlm_ids = []; // at least one layer is active those markups
    state.markups = {
      byId: {},
    };
    state.markupData = {
      normal: [],
      compound_elements: {
        lines: {},
        camera: {},
        ruler_area: {},
        distanceFactor: null,
      },
    };
  },
  SET_DATA_FOR_MARKUP_TABLE(state, data) {
    state.planMarkupPropData = {};
    state.planMarkupPropData.prjId = data.prjId;
    state.planMarkupPropData.pcmId = data.pcmId;
    state.activePlm_ids = [];
    state.activePlm_ids.push(data.plmId);
    state.markupMode = "update";
  },
  SET_SELECTED_VERSION(state, data) {
    let searchKey = `${state.planMarkupPropData.prjId}:${state.planMarkupPropData.sheetNumber}`;
    state.plan.byId[searchKey].selected_version = data;
    state.plan.byId[searchKey].current_version = data.plf_id;
  },
  SET_SELECTED_MARKUP(state, data) {
    state.selectedMarkup = {
      ...state.selectedMarkup,
      ...data,
    };
  },
  SET_SELECTED_MARKUP_TOOL(state, data) {
    Vue.set(state, "selectedMarkupTool", data);
  },
};

export default mutations;
