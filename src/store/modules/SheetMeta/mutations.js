import { normalizeMarkups } from "./normalize";
const mutations = {
  SET_SHEET_META_DATA(state, data) {
    state.sheetMetaById = {
      ...state.sheetMetaById,
      [data.sheetId]: data.metaData,
    };
  },
  SET_MARKUP_DATA(state, data) {
    const { markupIds, markupById } = normalizeMarkups(data);

    state.sheetMetaById = {
      ...state.sheetMetaById,
      [data.sheetId]: {
        ...state.sheetMetaById[data.sheetId],
        markupIds,
      },
    };
    state.sheetMarkupById = {
      ...state.sheetMarkupById,
      ...markupById,
    };
  },
};

export default mutations;
