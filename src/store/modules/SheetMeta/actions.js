import { Axios, urls } from "/src/utils/Axios";
import get from "lodash/get";

const actions = {
  async setSheetMeta({ commit }, { sheetId, metaData }) {
    commit("SET_SHEET_META_DATA", {
      sheetId,
      metaData,
    });
  },
  async setMarkupData({ commit }, { sheetId, markupData }) {
    commit("SET_MARKUP_DATA", {
      sheetId,
      markupData,
    });
  },
  async setPlanMarkupData({ dispatch }, { prjId, sheetId }) {
    const resp = await Axios.get(urls.markupList(prjId, sheetId));
    dispatch("setMarkupData", {
      sheetId,
      markupData: get(resp, "data"),
    });
  },
};

export default actions;
