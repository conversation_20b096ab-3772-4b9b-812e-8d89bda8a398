import get from "lodash/get";
import keys from "lodash/keys";
import forEach from "lodash/forEach";
export const normalizeSheetVerions = (prjId, sheetVersions) => {
  const sheetNumber = keys(sheetVersions)[0];
  let versions = [];
  let sheetMeta = {};

  for (let key in get(sheetVersions[sheetNumber], "versions", {})) {
    const plfId = get(
      sheetVersions[sheetNumber],
      `versions[${key}].plf_id`,
      undefined
    );
    versions.push(plfId);
    sheetMeta[plfId] = {
      ...get(sheetVersions[sheetNumber], `versions[${key}]`, {}),
    };
  }

  const key = sheetNumber;
  const value = versions;
  return { key, value, sheetMeta };
};

export const normalizeSheetCodes = (sheetCodes) => {
  const options = [];
  forEach(sheetCodes, (sheetCode) => {
    options.push({
      value: sheetCode.plf_id,
      // label: sheetCode.plf_sheet_number + '' + sheetCode.plf_sheet_name,
      label: `${sheetCode.plf_sheet_number} - V ${sheetCode.plf_sheet_version}`,
    });
  });
  return options;
};
