import { Axios, urls } from "/src/utils/Axios";
import { normalizeSheetCodes } from "./normalize";
import forEach from "lodash/forEach";
import last from "lodash/last";
import keys from "lodash/keys";

const actions = {
  async setPlanMarkup({ commit }, { plfId, prjId }) {
    const planMarkup = await Axios.get(urls.markupList(prjId, plfId));
    await commit("SET_PLAN_MARKUP", planMarkup.data);
    return planMarkup.data;
  },
  async setSheetVersions({ commit }, { sheetNumber, prjId }) {
    const sheetVersions = await Axios.get(urls.planData(prjId, sheetNumber));
    await commit("SET_SHEET_VERSIONS", {
      prjId,
      sheetVersions: sheetVersions.data,
    });
  },
  async setSheetMeta({ commit }, { sheetId, metaData }) {
    commit("SET_SHEET_META_DATA", {
      sheetId,
      metaData,
    });
  },
  async updateSheetMeta(_, data) {
    await Axios.patch(urls.patchFileDetails(), data);
  },
  setMarkupObjects({ commit }, markupObjects) {
    commit("SET_MARKUP_OBJECTS", markupObjects);
  },
  setMarkupJson({ commit }, markupJson) {
    commit("SET_MARKUP_JSON", markupJson);
  },
  setMarkupCanvas({ commit }, markupCanvas) {
    commit("SET_MARKUP_CANVAS", markupCanvas);
  },
  setPaneZoom({ commit }, paneZoom) {
    commit("SET_PANE_ZOOM", paneZoom);
  },
  updateMarkupObject({ commit }, { elementId, markupObject }) {
    commit("UPDATE_MARKUP_OBJECT", { elementId, markupObject });
  },
  updateMarkupJson({ commit }, { elementId, markupJson }) {
    commit("UPDATE_MARKUP_JSON", { elementId, markupJson });
  },
  addMarkupObject({ commit }, { plmId, elementId, markupObject, markupJson }) {
    commit("ADD_MARKUP_OBJECT", { plmId, elementId, markupObject, markupJson });
  },
  addRulerScale({ commit }, { plmId, rulerScale }) {
    commit("ADD_RULER_SCALE", { plmId, rulerScale });
  },
  resetDraftMarkup({ commit }) {
    commit("RESET_DRAFT_MARKUP");
  },
  setCurrentMarkupId({ commit }, currentMarkupId) {
    commit("SET_CURRENT_MARKUP_ID", currentMarkupId);
  },
  setMarkupMode({ commit }, markupMode) {
    commit("SET_MARKUP_MODE", markupMode);
  },

  async deletePlanSheet(_, sheetId) {
    try {
      const res = await Axios.delete(urls.deletePlfId(sheetId));
      return res;
    } catch (err) {
      return err;
    }
  },

  async deleteMarkupElement({ commit }, elementId) {
    commit("DELETE_MARKUP_ELEMENT", elementId);
  },
  async updatePlanMarkup({ state }, { markupData, prjId }) {
    return await Axios.put(
      urls.updateMarkup(prjId, state.currentMarkupId),
      markupData
    );
  },

  async getSheetCodeoptions(_, { prjId, query }) {
    try {
      const response = await Axios.get(urls.getSheetCodeoptions(prjId, query));
      return normalizeSheetCodes(response.data);
    } catch (error) {
      return error;
    }
  },

  async setSheetCompareImages({ commit }, { plfId1, plfId2 }) {
    const sheetCompareImages = await Axios.get(
      urls.getSheetCompareImages(plfId1, plfId2)
    );
    commit("SET_SHEET_COMPARE_IMAGES", sheetCompareImages.data);
  },

  async getSheetLists({commit}, prjId) {
    const data = await Axios.get(urls.getAllFileIds(prjId));
    const finalizedSheetObject = {}
    forEach(data.data, (item) => {
      const version = last(keys(item.versions))
      finalizedSheetObject[item.versions[version].plf_id] = {
        label: item.versions[version].plf_sheet_number + " - " + item.versions[version].plf_sheet_name,
        id: item.versions[version].plf_id, 
        number : item.versions[version].plf_sheet_number ,
        plf_sort_order: item.versions[version].plf_sort_order
      }
    });
    commit("SET_SHEET_LIST", finalizedSheetObject);
  },

  switchMarkupType({commit}, type){
    commit("SWITCH_MARKUP_TYPE", type)
  }
};

export default actions;
