import get from "lodash/get";
import reduce from "lodash/reduce";
import { DRAFT_MARKUP } from "@/constants";



import PaneZoom from "@/components/PlansNew1/Classes/PaneZoom.js";

const getters = {
  getAllMarkupJson: (state) => {
    return state.markupJsonById;
  },
  getCurrentMarkupJson: (state) => {
    const markupId = get(state, "currentMarkupId", "");
    const elements = get(state, `planMarkupById.${markupId}.elements`, []);
    return reduce(
      elements,
      (markupJson, elementId) => {
        return {
          ...markupJson,
          [elementId]: state.markupJsonById[elementId],
        };
      },
      {}
    );
  },
  getModifiedMarkupJson: (state) => {
    const markupId = get(state, "currentMarkupId", "");
    const elements = get(state, `planMarkupById.${markupId}.elements`, []);
    const rulerScale = get(state, `planMarkupById.${markupId}.rulerScale`, []);
    // eslint-disable-next-line lodash/prefer-lodash-method

    function getStrokeAndFontSizeFromCanvas(elementId) {
      if (`markupJsonById.${elementId}.strokeWidth`)
        return {
          strokeWidth: PaneZoom.getStrokeWidthFromCanvas(
            get(state, `markupJsonById.${elementId}.strokeWidth`, 1)
          ),
        };
      else if (`markupJsonById.${elementId}.fontSize`)
        return {
          fontSize: PaneZoom.getFontSizeFromCanvas(
            get(state, `markupJsonById.${elementId}.fontSize`, 1)
          ),
        };
    }
    return {
      elements: reduce(
        elements,
        (markupJson, elementId) => {
          return {
            ...markupJson,
            [elementId]: {
              ...get(state, `markupJsonById.${elementId}`, {}),
              points: PaneZoom.getSheetListCordinatesFromCanvas(
                get(state, `markupJsonById.${elementId}.points`, [])
              ),
              // strokeWidth: PaneZoom.getStrokeWidthFromCanvas(
              //   get(state, `markupJsonById.${elementId}.strokeWidth`, 1)
              // ),
              ...getStrokeAndFontSizeFromCanvas(elementId),
              //actualPoints: get(state, `markupJsonById.${elementId}.points`, []),
            },
          };
        },
        {}
      ),
      rulerScale: rulerScale,
    };
  },
  getMarkupObjectById: (state) => (elementId) => {
    return get(state, `markupJsonById.${elementId}`, {});
  },
  getMarkupObjectsByPlmId: (state) => (plmId) => {
    return get(state, `planMarkupById.${plmId}.elements`, []);
  },
  getPlanMarkupById: (state) => (plmId) => {
    return get(state, `planMarkupById.${plmId}`, {});
  },
  sheetMetaById: (state) => (sheetId) => {
    return get(state, `sheetMetaById.${sheetId}`, {});
  },
  getMarkupIdsBySheetId: (state) => (sheetId) => {
    return get(state, `sheetMetaById.${sheetId}.markupIds`, []);
  },
  getVersionsBySheetId: (state) => (sheetId) => {
    const versions = state.sheetVersionsById[sheetId] || [];
    return versions;
  },
  getCurrentMarkupId: (state) => {
    return get(state, "currentMarkupId");
  },
  getCurrentMarkupMode: (state) => {
    return get(state, "markupMode");
  },
  isMarkupDraftMode: (state) => {
    return get(state, "currentMarkupId") === DRAFT_MARKUP.id;
  },
  getMarkupCreator: (state) => {
    const markupId = get(state, "currentMarkupId", "");
    const planMarkup = get(state, `planMarkupById.${markupId}`, {});
    return get(planMarkup, "plm_createdby.user_id", "");
  },
  getSheetCompareImages: (state) => {
    return get(state, "sheetCompare", {});
  },
  getSheetList: (state) => {
    // return get(state, "sheetList", {});
    const sheetList = get(state, "sheetList", {});

    // // return sort by plf_sort_order, use lodash methods
    // return sortBy(map(keys(sheetList), key => sheetList[key]), 'plf_sort_order');
    return sheetList;
  },
  getMarkupType: (state) => {
    return get(state, "markupType", "MARKUP");
  },
};
export default getters;
