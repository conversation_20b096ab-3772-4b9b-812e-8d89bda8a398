import forEach from "lodash/forEach";
import get from "lodash/get";
import keys from "lodash/keys";
import PaneZoom from "@/components/PlansNew1/Classes/PaneZoom.js";
import MarkupObjects from "@/components/PlansNew1/Classes/MarkupObjects.js";
import { normalizeSheetVerions } from "./normalize";
import { DRAFT_MARKUP } from "@/constants";

// getCanvasListCordinatesFromSheet
const mutations = {
  SET_PLAN_MARKUP(state, planMarkups) {
    const markups = planMarkups;
    const markupIds = [];
    forEach(markups, (markup) => {
      const markupObjects = get(markup, "plm_markup.elements", {});
      console.log("markupObjects before loop", markupObjects);
      for (let key in markupObjects) {
        if (markupObjects[key].points) {
          markupObjects[key].points = PaneZoom.getCanvasListCordinatesFromSheet(
            markupObjects[key].points
          );
          if (markupObjects[key].strokeWidth)
            markupObjects[key].strokeWidth = PaneZoom.getStrokeWidthFromSheet(
              markupObjects[key].strokeWidth
            );
          else if (markupObjects[key].fontSize)
            markupObjects[key].fontSize = PaneZoom.getFontSizeFromSheet(
              markupObjects[key].fontSize
            );
        }
      }

      state.planMarkupById[markup.plm_id] = {
        ...markup,
        elements: [...keys(markupObjects)],
      };
      console.log("markupObjects after loop", markupObjects);
      state.markupJsonById = {
        ...state.markupJsonById,
        ...markupObjects,
      };
      markupIds.push(markup.plm_id);
    });
    // sort markupid by plm_upadatedon
    markupIds.sort((a, b) => {
      const dateA = new Date(get(state.planMarkupById[a], "plm_upadatedon", 0));
      const dateB = new Date(get(state.planMarkupById[b], "plm_upadatedon", 0));
      return dateA - dateB;
    });

    if (markupIds.length > 0) {
      state.currentMarkupId = state.currentMarkupId
        ? state.currentMarkupId
        : markupIds[markupIds.length - 1];
      state.sheetMetaById = {
        ...state.sheetMetaById,
        [markups[0].plf_id]: {
          ...state.sheetMetaById[markups[0].plf_id],
          markupIds: markupIds,
        },
      };
    } else {
      state.currentMarkupId = DRAFT_MARKUP.id;
    }
  },
  SET_SHEET_META_DATA(state, data) {
    state.sheetMetaById = {
      ...state.sheetMetaById,
      [data.sheetId]: data.metaData,
    };
  },
  SET_SHEET_VERSIONS(state, { prjId, sheetVersions }) {
    const { key, value, sheetMeta } = normalizeSheetVerions(
      prjId,
      sheetVersions
    );
    state.sheetVersionsById = {
      ...state.sheetVersionsById,
      [key]: value,
    };
    state.sheetMetaById = {
      ...state.sheetMetaById,
      ...sheetMeta,
    };
  },
  SET_MARKUP_OBJECTS(state, markupObjects) {
    state.markupObjectById = {
      ...state.markupObjectById,
      ...markupObjects,
    };
  },
  SET_MARKUP_JSON(state, markupJson) {
    state.markupJsonById = {
      ...state.markupJsonById,
      ...markupJson,
    };
  },
  SET_MARKUP_CANVAS(state, markupCanvas) {
    console.log("markupCanvas", markupCanvas);
    state.markupCanvas = markupCanvas;
  },
  SET_PANE_ZOOM(state, paneZoom) {
    state.paneZoom = paneZoom;
  },
  UPDATE_MARKUP_OBJECT(state, { elementId, markupObject }) {
    state.markupObjectById = {
      ...state.markupObjectById,
      [elementId]: {
        ...state.markupObjectById[elementId],
        ...markupObject,
      },
    };
  },
  UPDATE_MARKUP_JSON(state, { elementId, markupJson }) {
    // console.log("UPDATE_MARKUP_JSON", elementId, markupJson);
    state.markupJsonById = {
      ...state.markupJsonById,
      [elementId]: {
        ...state.markupJsonById[elementId],
        ...markupJson,
      },
    };
  },
  ADD_MARKUP_OBJECT(state, { plmId, elementId, markupObject, markupJson }) {
    state.planMarkupById = {
      ...state.planMarkupById,
      [plmId]: {
        ...get(state.planMarkupById, [plmId], {}),
        elements: [
          ...get(state.planMarkupById, [plmId, "elements"], []),
          elementId,
        ],
      },
    };
    state.markupObjectById = {
      ...state.markupObjectById,
      [elementId]: markupObject,
    };
    state.markupJsonById = {
      ...state.markupJsonById,
      [elementId]: markupJson,
    };
  },
  RESET_DRAFT_MARKUP(state) {
    state.planMarkupById = {
      ...state.planMarkupById,
      [DRAFT_MARKUP.id]: {},
    };
  },
  ADD_RULER_SCALE(state, { plmId, rulerScale }) {
    state.planMarkupById = {
      ...state.planMarkupById,
      [plmId]: {
        ...get(state.planMarkupById, [plmId], {}),
        rulerScale: rulerScale,
      },
    };
  },
  SET_CURRENT_MARKUP_ID(state, markupId) {
    state.currentMarkupId = markupId;
  },
  SET_MARKUP_MODE(state, markupMode) {
    state.markupMode = markupMode;
  },
  DELETE_MARKUP_ELEMENT(state, elementId) {
    const plmId = get(state, "currentMarkupId", "");
    const elements = get(state.planMarkupById[plmId], "elements", []);
    const index = elements.indexOf(elementId);
    if (index > -1) {
      elements.splice(index, 1);
    }
    state.planMarkupById = {
      ...state.planMarkupById,
      [plmId]: {
        ...get(state.planMarkupById, [plmId], {}),
        elements: elements,
      },
    };
    delete state.markupObjectById[elementId];
    delete state.markupJsonById[elementId];
    MarkupObjects.removeMarkupObject(elementId);
  },
  SET_SHEET_COMPARE_IMAGES(state, sheetCompareResult) {
    state.sheetCompare = sheetCompareResult;
  },
  SET_SHEET_LIST(state, sheetList) {
    state.sheetList = { ...state.sheetList, ...sheetList };
  },
  SWITCH_MARKUP_TYPE(state, type){
    state.markupType = type
  }
};
export default mutations;
