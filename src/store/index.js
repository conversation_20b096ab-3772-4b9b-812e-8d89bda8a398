import Vue from "vue";
import Vuex from "vuex";
import PlanUploadFile from "./modules/PlanUploadFile";
import PlanUploadBatch from "./modules/PlanUploadBatch";
import PlanUploadSet from "./modules/PlanUploadSet";
import PlanMarkup from "./modules/PlanMarkup";
import MarkupCanvas from "./modules/MarkupCanvas";
import SheetMeta from "./modules/SheetMeta";
import grid from './modules/grid';
import rbac from './modules/rbac'

// import { AlertStore } from "@development/linarc-design-components";

// console.log(TableStore)

Vue.use(Vuex);

export default new Vuex.Store({
  modules: {
    PlanUploadBatch,
    PlanUploadFile,
    PlanUploadSet,
    PlanMarkup,
    MarkupCanvas,
    SheetMeta,
    grid,
    // Alerts: AlertStore,
    rbac
  },
  strict: process.env.NODE_ENV !== "production",
});
