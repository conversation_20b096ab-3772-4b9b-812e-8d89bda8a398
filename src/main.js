import Vue from "vue";
import App from "./App.vue";
import moment from "moment";
import XLSX from "xlsx";
// import App from '../test/testingAdminPlans.vue'
import { library } from "@fortawesome/fontawesome-svg-core";
import designComponents from "@development/linarc-design-components";
import MarkupJson from "@/components/PlansNew1/Classes/MarkupJson.js";
import { Select } from '@development/linarc-design-components';
import { Toggle } from '@development/linarc-design-components';

import { AccessModal, FileUpload } from '@development/linarc-design-components';
import MarkupObjects from "@/components/PlansNew1/Classes/MarkupObjects.js";
import MarkupCanvas from "@/components/PlansNew1/Classes/MarkupCanvas";
import {
  faTimesCircle,
  faArrowCircleDown,
  faUserPlus,
  faSearch,
  faFilter,
  faCalendarAlt,
  faUserAlt,
  faImages,
  faEnvelope,
  faPhoneSquareAlt,
  faPencilAlt,
  faHighlighter,
  faEye,
  faEyeSlash,
  faHandPointer,
  faArrowRight,
  faArrowLeft,
  faCircle as solidCircle,
  faRuler,
  faSync,
  faRulerCombined,
  faCamera,
  faUndo,
  faRedo,
  faBackward,
  faDownload,
  faTimes,
  faTrash,
  faICursor,
  faEdit,
} from "@fortawesome/free-solid-svg-icons";
import {
  faCircle,
  faSquare,
  faSave,
} from "@fortawesome/free-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import BootstrapVue from "bootstrap-vue";
import vClickOutside from "v-click-outside";
import store from "@/store/index.js";
import services from "./services";
Vue.prototype.$apiService = services;

import "@development/linarc-design-components/dist/linarc-design-components.css";
import "./styles/global.css";
import "./globals";

import CameraIcon from "/src/components/PlansNew1/Tools/camera.svg";
import LocationIcon from "/src/components/PlansNew1/Tools/location.svg";


MarkupCanvas.setCameraImageSrc(CameraIcon);
MarkupCanvas.setLocationImageSrc(LocationIcon);

library.add(
  faTimesCircle,
  faArrowCircleDown,
  faUserPlus,
  faSearch,
  faFilter,
  faCalendarAlt,
  faUserAlt,
  faImages,
  faEnvelope,
  faPhoneSquareAlt,
  faEye,
  faEyeSlash,
  faHandPointer,
  faArrowRight,
  faPencilAlt,
  faHighlighter,
  solidCircle,
  faRuler,
  faSync,
  faRulerCombined,
  faCamera,
  faUndo,
  faRedo,
  faBackward,
  faDownload,
  faTimes,
  faArrowLeft,
  faCircle,
  faSquare,
  faSave,
  faTrash,
  faICursor,
  faEdit
);

Vue.component("font-awesome-icon", FontAwesomeIcon);
Vue.component("Select", Select);
Vue.component("Toggle", Toggle);
Vue.component("AccessModal",AccessModal)
Vue.component("FileUpload", FileUpload)
MarkupJson.setStore(store);
MarkupObjects.setStore(store);
MarkupCanvas.setStore(store);

Vue.use(BootstrapVue);
Vue.use(require("vue-shortkey"));
Vue.config.productionTip = false;
Vue.use(vClickOutside);
Vue.use(designComponents, {
  moment,
  XLSX,
});
new Vue({
  store,
  render: (h) => h(App),
}).$mount("#app");

export default App;
