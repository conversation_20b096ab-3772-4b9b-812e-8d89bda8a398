<template>
  <div :class="[toggleState ? 'small' : 'big']" class="main-layout">
    <div class="middle-pane">
      <div v-if="showPageHeader" class="header">
        <div class="page-title">
          <slot name="header"></slot>
        </div>
        <div class="page-actions">
          <slot name="page-actions"></slot>
        </div>
      </div>
      <div
        :class="{ content_without_header: !showPageHeader }"
        :style="[
          displayScroll ? { overflow: 'auto' } : { overflow: 'hidden' },
          getMiddlePaneStyle,
        ]"
        class="content"
      >
        <slot name="content"></slot>
      </div>
    </div>
    <div v-if="$store.state.rightPane" class="right-pane">
      <RightPane @onClose="onPaneClose">
        <template slot="right-pane-header">
          <slot name="right-pane-header"></slot>
        </template>
        <template slot="right-pane-actions">
          <slot name="right-pane-actions"></slot>
        </template>
        <template slot="right-pane-content">
          <slot name="right-pane-content"></slot>
        </template>
        <template slot="right-pane-footer">
          <slot name="right-pane-footer"></slot>
        </template>
      </RightPane>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  props: {
    showPageHeader: {
      type: Boolean,
      default: true,
    },
    displayScroll: {
      type: Boolean,
      default: true,
    },
    contentBackground: {
      type: String,
      default: "#e7edf5",
    },
    noPadding: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapState({
      toggleState: (state) => state.toggle,
    }),
    getMiddlePaneStyle() {
      return {
        background: this.contentBackground,
        padding: this.noPadding ? "0px" : "10px",
      };
    },
  },
  methods: {
    onPaneClose() {
      this.$emit("onRightPaneClose", true);
    },
  },
};
</script>

<style lang="scss" scoped>
.main-layout {
  display: flex;
  overflow: auto;
  height: 100%;
  background: #e7edf5;
  flex: 1;

  .middle-pane {
    width: 100%;
    background: #e7edf5;
    // min-width: 600px;
    min-width: 400px;

    .header {
      height: 50px;
      width: 100%;
      border-bottom: 1.5px solid rgba(27, 28, 30, 0.15);
      position: relative;
      display: flex;
      align-items: center;

      .page-title {
        display: flex;
        flex: 1;
        height: 100%;
        align-items: center;
        font-weight: bold;
        font-size: 20px;
        padding-left: 20px;
        white-space: nowrap;
      }

      .page-actions {
        display: flex;
        padding-right: 20px;
      }
    }

    .content {
      height: calc(100% - 50px);
      position: relative;
    }

    .content_without_header {
      height: 100%;
    }

    .content::-webkit-scrollbar {
      width: 10px;
      height: 10px;
      background-color: #e5e5e5;
    }

    .content::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      background-color: #f5f5f5;
    }

    .content::-webkit-scrollbar-thumb {
      border-radius: 10px;
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      background-color: rgba(158, 152, 152, 0.6);
    }

    .content::-webkit-scrollbar-thumb:hover {
      background-color: rgba(158, 152, 152, 1);
    }
  }
}

.main-layout::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: #e5e5e5;
}

.main-layout::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}

.main-layout::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: rgba(158, 152, 152, 0.6);
}

.main-layout::-webkit-scrollbar-thumb:hover {
  background-color: rgba(158, 152, 152, 1);
}
</style>
