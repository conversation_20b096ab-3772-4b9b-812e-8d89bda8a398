<template>
  <div style="width:100%;">

    <b-container fluid class="bv-example-row">
      <transition-group name="plan-load" tag="b-row" appear>
        <b-col v-for="sheet in sheetsData" :id="sheet" :key="sheet" lg="2" md="4" sm="6" xs="12"  class="mw-100 fixSpaceXS tiles">
          <div class="highlight">
            <div>
              <img :src="thumbLoader(sheet)" class="plan-images" @click="clickOnSheet(sheet)">
            </div>
            <div>
              <div class="float-left">
                <div
                  @click="clickOnVersions(sheet)"
                >
                  <h6><b-badge class="versions" title="See Versions">{{currentVersion(sheet)}}</b-badge></h6>
                </div>
                <div class="sheet-title">{{sheet}} {{sheetTitle(sheet).toUpperCase()}}</div>
                
              </div>
              <div class="float-right">
                  <button title="Open markups" @click="clickOnMarkups(sheet)" class="markup"><font-awesome-icon :icon="['fas', 'edit']"/></button>
              </div>
            </div>

          </div>
        </b-col>
      </transition-group>
      <!-- </b-row> -->
    </b-container>
    <ModalPlanVersions
      :selectedSheet="selectedSheet"
      :sheetId="sheetId"
      :projectMembers="projectMembers"
      :onSheetClicked="onSheetClicked"
      :closeAll="closeAll"
      :clickOnMarkups="clickOnMarkups"
      />
    <ModalMarkupsVersions
      ref="markup-modal"

      :selectedSheet="selectedSheet"
      :sheetId="sheetId"
      :sheetMarkup="sheetMarkup"
      :sheetVersion="sheetVersion"
      :projectMembers="projectMembers"
      :onVersionClicked="onVersionClicked"

      :fetchmarkup="fetchmarkup"
    />
  </div>
</template>
<script>
import ModalPlanVersions from './modals/modalPlanVersions';
import ModalMarkupsVersions from './modals/modalMarkupVersion';
import keys from 'lodash/keys';
import filter from 'lodash/filter';
import startsWith from 'lodash/startsWith';

export default {
  name: 'plansList',
  components: {
    ModalPlanVersions,
    ModalMarkupsVersions
  },
  computed: {
    sheetsData() {
      return keys(this.plansData);
    },
    thumbLoader() {
      return sheet => this.baseURL+`${this.plansData[sheet].versions[this.plansData[sheet].current_version].thumb_src}`;
    },
    currentVersion() {
      return sheet => `${this.plansData[sheet].current_version}`;
    },
    sheetTitle() {
      return sheet => {
        const sheetTitle = this.plansData[sheet].sheet_title ? this.plansData[sheet].sheet_title : '';
        return `${sheetTitle}`;
      };
    },
    filterSheets() {
      return filterValue => {
        const allSheets = keys(this.plansData);
        if (filterValue) {
          return filter(allSheets, value => {
            if (startsWith(value, filterValue, 0)) return true;
            return false;
          });
        } return allSheets;
      };
    }
  },
  watch: {

  },
  data() {
    return {
      selectedSheet: null,
      sheetId: null,
      sheetMarkup: null,
      sheetVersion: null,
      filteredMarkups: null
    };
  },
  methods: {
    clickOnSheet(selectedSheet) {
      const version = this.plansData[selectedSheet].current_version;
      this.onSheetClicked(selectedSheet, version);
    },
    clickOnVersions(selectedSheet) {
      this.selectedSheet = this.plansData[selectedSheet];
      this.sheetId = selectedSheet;
      this.$modal.show('modal-plan-versions');
      // this.modalFlag = true;
    },
    async clickOnMarkups(selectedSheet, version) {
      this.$modal.hide('modal-plan-versions');
      this.selectedSheet = this.plansData[selectedSheet];
      this.sheetId = selectedSheet;
      // this.sheetMarkup = this.markups[selectedSheet];
      if (version) this.sheetVersion = version;
      else this.sheetVersion = this.plansData[selectedSheet].current_version;
      // console.log("Markup version clicked", this.sheetId,this.sheetVersion)
      this.$modal.show('modal-plan-markups');
      await this.$refs['markup-modal'].getMarkupList(this.sheetId,this.sheetVersion)
    },
    closeAll() {
      this.$modal.hide('modal-plan-versions');
      // this.modalFlag = false;
    }
  },
  props: {
    plansData: {
      type: Object,
      default: () => {}
    },
    onSheetClicked: {
      type: Function
    },
    onVersionClicked: {
      type: Function
    },
    markups: {
      type: Object,
      default: null
    },
    projectMembers: {
      type: Object,
      default: null
    },
    focusSheet: {
      type: String,
      default: null
    },
    baseURL:{
      type: String
    },
    getVersions:{
      type: Function
    },
    getMarkups:{
      type: Function
    },
    fetchmarkup:{
      type:Function
    }
  },
  mounted() {
    if (this.focusSheet) {
      const element = document.getElementById(this.focusSheet);
      element.scrollIntoView();
      element.style.border = '#316BAE solid 3px';
    }
  }
};
</script>

<style lang="scss" scoped>
@import "../../../node_modules/bootstrap/scss/bootstrap";

.centerAlign{
  text-align: center;
  margin: 5px 0;
}
.fixSpaceXS{
  margin: 0px;
}
.fixSpaceXS.tiles {
  // height: 150px;
  box-shadow: 0 2px 9px -1px rgba(0, 0, 0, 0.3);
  margin: 10px;
  min-width: 385px;
}
.plan-images {
  width: 320px;
  height: 250px;
}
.sheet-title {
  margin-bottom: 10px;
  white-space: nowrap;
  width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.versions{
  cursor: pointer;
}
.markup{
  border:none;
  background:none;
}
// .markup{
//   &:focus {
//     outline: none;
//   }
// }
.plan-load-enter-active{
  transition: all 0.3s ease;
}
.plan-load-enter{
  transform: scale(0.9);
  opacity: 0.8;
}
.plan-load-move {
  transition: transform 1s;
}

@media screen and (max-width: 993px) {
  .fixSpaceXS{
    margin: 0px;
  }
}
@media screen and (max-width: 992px) {
  .fixSpaceXS{
    margin: 10px 0px;
  }
}

</style>