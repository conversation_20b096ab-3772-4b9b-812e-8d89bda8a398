<template>
  <div id="modal-base">
    <div id="modal-container" :style="style">
      <div id="modal-header">
        <div id="modal-title">
          <slot name="modal-title"> Sample Title for modal </slot>
        </div>
        <div class="closeButton">
          <slot name="modal-close"></slot>
          <button
            @click="$emit('closeModal')"
          >
         X
          </button>
        </div>
      </div>
      <slot name="modal-body"></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    modalWidth: {
      default: 700,
      type: Number,
    },
  },
  computed: {
    style() {
      return `min-width:${this.modalWidth}px`;
    },
  },
};
</script>

<style scoped lang="scss">
#modal-base {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  z-index: 1;
  background-color: rgba(1, 1, 1, 0.4);
  #modal-container {
    position: relative;
    border-radius: 10px;
    // top: 50%;
    // left: 50%;
    // transform: translate(-50%, -50%);
    background: #ffffff;
    height: fit-content;
    width: fit-content;
    min-height: 100px;
    #modal-header {
      height: 42px;
      display: flex;
      justify-content: space-between;
      padding: 0px 14px;
      align-items: center;
      border-bottom: 1px solid #cccccc;
      user-select: none;
      #modal-title {
        font-family: Lato;
        font-style: normal;
        font-weight: 600;
        font-size: 20px;
        color: #4d4d4d;
      }
      img {
        cursor: pointer;
      }
    }
    .closeButton {
      display: flex;
      flex-direction: row;
    }
  }
}
</style>
