<template>
  <modal
    name="modal-plan-versions"
    class="modal-plan-versions"
    :width="500"
    :height="500"
    @close="close"
  >
    <div class="modal-version-container">
      <div class="modal-heading">
        Version History
      </div>
        <div class="sheet-title">{{sheetId}}
         <span v-if="isSheetTitlePresent">{{sheetTitle}}</span>
        </div>
        <div>
          <table class="header-table">
              <tr>
                  <td class="sheet">Version</td>
                  <td class="created-on">Created On</td>
                  <td class="created-by">Created By</td>
                  <td class="actions">Markups</td>
              </tr>
          </table>
          <div class="content-div">
            <table class="content-table">
                <tr v-for="(version, index) in allVersions"
                :key="index"
                >
                    <td class="sheet" @click="onSheetVersionClicked(version.id)">{{version.id}}</td>
                    <td class="created-on" @click="onSheetVersionClicked(version.id)">{{version.createdAt}}</td>
                    <td class="created-by" @click="onSheetVersionClicked(version.id)">{{version.createdBy}}</td>
                    <!-- <td>{{new Date().toLocaleDateString()}}</td> -->
                    <td class="actions">
                        <!-- <button class="open" title="Open Sheet" ><i class="fa fa-folder-open" aria-hidden="true" style="font-size:15px"></i></button> -->
                        <button class="markup" title="Open markups" @click="clickOnMarkup(version.id)">MK</button>
                    </td>
                </tr>
            </table>
          </div>
        </div>
    </div>
  </modal>
</template>

<script>
import keys from 'lodash/keys';
import forEach from 'lodash/forEach';
import toUpper from 'lodash/toUpper';
export default {
  name: 'modal-plan-versions',
  props: {
    sheetId: {
      type: String
    },
    selectedSheet: {
      type: Object,
      default: () => { 
        return {
        sheet_title: ''
        }
      }
    },
    projectMembers: {
      type: Object
    },
    onSheetClicked: {
      type: Function
    },
    clickOnMarkups: {
      type: Function
    },
    closeAll: {
      type: Function
    }
  },
  watch: {},
  data() {
    return {

    };
  },
  computed: {
    allVersions() {
      const versionObj = {};
      if (this.selectedSheet) {
        if (this.selectedSheet.versions) {
          forEach(keys(this.selectedSheet.versions).reverse(),version => {
            versionObj[version] = {};
            const date = new Date(this.selectedSheet.versions[version].creation_history.created_date);
            versionObj[version].id = version;
            versionObj[version].createdBy = this.selectedSheet.versions[version].creation_history.created_by;
            versionObj[version].createdAt = `${date.getMonth() + 1}-${date.getDate()}-${date.getFullYear()}`;
          });
        }
      }
      return versionObj;
    },

    sheetTitle() {
      if (this.selectedSheet) {
        if (this.selectedSheet.sheet_title) {
          return `${toUpper(this.selectedSheet.sheet_title)}`;
        }
      }
      return ' ';
    }
  },
  methods: {
    isSheetTitlePresent() {
      if (this.selectedSheet) {
        if (this.selectedSheet.sheet_title) return true;
      }
      return false;
    },
    onSheetVersionClicked(version) {
      this.onSheetClicked(this.sheetId, version);
    },
    clickOnMarkup(version) {
      console.log(this.sheetId, version);
      this.clickOnMarkups(this.sheetId, version);
    },
    close() {
      this.closeAll();
    },
    closeByEscape(e) { if (e.keyCode === 27) this.close(); }
  }
};
</script>

<style lang="scss" scoped>

.modal-version-container{
  padding: 10px;
  width: 100%;
  height: 500px;
  .modal-heading{
    font-size: 18px;
    font-weight: bolder;
    height: 40px;
    color: #316BAE;
    border: none;
  }
  .sheet-title {
    text-align: left;
    width: 90%;
    font-size: 14px;
    padding-left: 16px;
    font-weight: bolder;
    span {
      margin-left: 10px;
    }
  }
  .header-table {
    margin-top: 10px;
    width: 98%;
    tr{ 
      width: 100%;
      padding: 2px;
      font-weight: bold;
      font-size: 14px;
      height: 30px;
      color: #316BAE; 
      td {
        text-align: center;
      }
      td[class="sheet"] {
        width: 20%;
      }
      td[class="created-on"] {
        width: 30%;
      }
      td[class="created-by"] {
        width: 30%;
      }
      td[class="actions"] {
        width: 20%;
      }
    }
  }
  .content-div{
    width: 100%;
      max-height: 350px;
      overflow-y: auto;
    &::-webkit-scrollbar {
      width: 10px;
    }
    &::-webkit-scrollbar-thumb {
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
      -webkit-transform: rotate3d(0,0,0,0);
      transform: rotate3d(0,0,0,0);
      -webkit-transition: background-color .1s ease-out,margin .1s ease-out,height .1s ease-out;
      transition: background-color .1s ease-out,margin .1s ease-out,height .1s ease-out;
      background-color: #9EB4BE;
      margin: 5px 10px 5px 0;
      border-radius: 20px;
      height: calc(100% - 40%);
      display: block;
    }
    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
      -webkit-border-radius: 15px;
      border-radius: 15px;
    }
    .content-table {
      margin-top: 5px;
      width: 98%;
      tr {
        width: 100%;
        margin-top: 5px;
        font-weight: 600;
        height: 30px;
        padding: 2px;
        font-size: 13px;
        td {
          text-align: center;
          button {
            width: 50%;
            text-align: center;
            &:focus {
              outline: none;
            }
          }
        }
        td[class="sheet"] {
          width: 20%;
        }
        td[class="created-on"] {
          width: 30%;
        }
        td[class="created-by"] {
          width: 30%;
        }
        td[class="actions"] {
          width: 20%;
        }
        &:hover {
          // background: #eee;
          cursor: pointer;
          // font-weight: bold;
        }
      }
    }

  }
}
</style>