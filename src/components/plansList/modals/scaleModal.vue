<template>
    <modalBase @closeModal="$emit('close')">
        <template v-slot:modal-title >
            Enter the Scale in (ft/in)
        </template>
        <template
            v-slot:modal-body
        >
        <form class="modal-content-plans" @submit.prevent="$emit('doneScaling', scaleObj)">
            <div class="modal-main">
                <div class="label">Feet</div>
                <input style="padding:2px 10px 2px 10px" type="number" min="0" required v-model="scaleObj.feet"/>
            </div>
            <div class="modal-main">
                <div class="label" style="margin-left: 30px;">Inches</div>
                <input style="padding:2px 10px 2px 10px" type="number" min="0"  max="12" required v-model="scaleObj.inches"/>
            </div>
            <input type="submit" class="btn btn-submit"/>
        </form>
        </template>
    </modalBase>
</template>

<script>
import modalBase from '@/components/plansList/modals/modalBase.vue'
export default {
    components:{
        modalBase
    },
    data(){
        return{
            scaleObj:{
                feet: 0,
                inches: 0,
            }
        }
    },
}
</script>

<style lang="scss" scoped>
.modal-content-plans{
    height: 35px;
    display:flex;
    justify-content: space-around;
    flex-direction: row;
        align-items: baseline;
    .modal-main{
        display:flex;
        justify-content: space-around;
        align-items: center;
        margin-top: 8px;
        .label{
            margin-left: 15px;
        }
        input{
            width: 100px;
            height: 20px;
            border: 1px solid black;
            border-radius: 5px;
            padding:2px 10px 2px 10px;
            margin-left: 30px;
        }
    }
}

.btn{
    width:200px;
}
.btn-submit{
  margin-left:57px;
  padding: 0;
  border: none;
  background: #F25E45;
  cursor: pointer;
  border-radius: 5px;
  font-size: 14px;
  line-height: 14px;
  color: #fff;
  font-weight: bold;
  text-align: center;
  padding: 8px 10px;
  transition: all 0.15s ease-in-out;
}
input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
  margin: 0; 
}
</style>