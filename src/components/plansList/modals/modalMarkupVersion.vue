<template>
  <modal
    name="modal-plan-markups"
    class="modal-plan-markups"
    :width="500"
    :height="500"
    
  >
    <div class="modal-markup-container">
      <div class="heading">
      Markups
      </div>
        <div class="sheet-title">{{sheetId}}
             <span>{{sheetTitle}}</span>
        </div>
        <div v-if="markupArray.length > 0">
          <table class="header-table">
              <tr>
                  <td class="sheet">Markup Name</td>
                  <td class="created">Created By</td>
                  <td class="create-date">Created On</td>
                  <!-- <td class="actions">Actions</td> -->
              </tr>
          </table>
          <div class="content-div">
          <table class="content-table">
              <tr v-for="markup in markupArray" :key="markup.id" @click="clickOnMarkup(markup)" class="markup-rows">
                  <td class="sheet">{{markup.name}}</td>
                  <td class="created">
                    {{markup.createdBy}}
                  </td>
                  <td class="create-date">
                   {{markup.createdOn}}
                  </td>
                  <!-- <td class="actions">
                      <button class="open" title="Open Markup" ><i class="fa fa-folder-open" aria-hidden="true" style="font-size:15px"></i></button>
                  </td> -->
              </tr>
          </table>
          </div>
        </div>
        <div v-else class="no-data-heading">
          No Markups Yet!
        </div>
    </div>
  </modal>
</template>

<script>
import map from 'lodash/map';
import forEach from 'lodash/forEach';
import toUpper from 'lodash/toUpper';
import keys from 'lodash/keys';
import assign from 'lodash/assign';
export default {
  name: 'modal-plan-markups',
  props: {
    // sheetId: {
    //   type: String
    // },
    // selectedSheet: {
    //   type: Object,
    //   default: null
    // },
    // onSheetClicked: {
    //   type: Function
    // },
    // closeAll: {
    //   type: Function
    // }
    sheetMarkup: {
      type: Object,
      default: null
    },
    selectedSheet: {
      type: Object,
      default: null
    },
    sheetId: {
      type: String
    },
    sheetVersion: {
      type: String
    },
    projectMembers: {
      type: Object,
      default: null
    },
    onVersionClicked: {
      type: Function
    },
    fetchmarkup: {
      type: Function
    }
  },
  watch: {
    selectedSheet() {
      this.markupArray = [];
      this.updateSheetData();
    },
    sheetId() {
      this.markupArray = [];
      this.updateSheetData();
    },
    sheetVersion() {
      this.markupArray = [];
      this.updateSheetData();
    }
  },
  data() {
    return {
      markupArray: []
    };
  },
  computed: {
    sheetTitle() {
      if (this.selectedSheet) {
        if (this.selectedSheet.sheet_title) {
          return `${toUpper(this.selectedSheet.sheet_title)}`;
        }
      }
      return ' ';
    }
  },
  methods: {
    updateSheetData() {
      if (this.sheetMarkup !== null && this.sheetVersion !== null) {
        this.markupArray = [];
        forEach(keys(this.sheetMarkup.versions),version => {
          console.log(version);
          if (this.sheetMarkup.versions[version].sheetVersion) {
            if (this.sheetMarkup.versions[version].sheetVersion[this.sheetVersion]) {
              const fullObj = assign({}, this.sheetMarkup.versions[version]);
              fullObj.id = version;
              this.markupArray.push(fullObj);
            }
          }
        });
        this.markupArray = this.markupArray.reverse();
      } else this.markupArray = [];
    },
    clickOnMarkup(markup) {
      // console.log(this.sheetId, markup.id);
      this.onVersionClicked(this.sheetId, markup.markupObj, this.sheetVersion, markup.name);
    },
    async getMarkupList(sheetId, version){
      const markups = await this.fetchmarkup(sheetId, version)
      this.markupArray = map(markups,o => {
        return {
          id:o.plm_id,
          name:o.plm_name,
          createdBy:`${o.plm_createdby.emp_first_name} ${o.plm_createdby.emp_last_name}`,
          createdOn:o.plm_createdon,
          markupObj:o.plm_markup
        }
      })
    }
    // onSheetVersionClicked(version) {
    //   this.onSheetClicked(this.sheetId, version);
    // },
    // close() {
    //   this.closeAll();
    // },
    // closeByEscape(e) { if (e.keyCode === 27) this.close(); }
  },
};
</script>

<style lang="scss" scoped>
  .no-data-heading{
    margin-top: 50px;
    margin-left: 0%;
    width: 98%;
    text-align: center;
    font-size: 16px;
    padding-left: 16px;
    font-weight: bolder;
    margin-bottom: 7px;
  }
  .heading{
    font-size: 18px;
    font-weight: bolder;
    text-align: left;
    width: 90%;
    padding-left: 16px;
    color: #316BAE;
    margin-bottom: 7px;
    margin-top: 10px;

  }
  .modal-markup-container {
        padding: 10px;
        width: 100%;
        height: 500px;
      .sheet-title {
        text-align: left;
        width: 90%;
        font-size: 14px;
        padding-left: 16px;
        font-weight: bolder;
        span {
        margin-left: 10px;
        }
    }
    .header-table {
        margin-top: 10px;
        margin-left: -6%;
        width: 98%;
        tr{ 
          width: 100%;
          padding: 2px;
          font-weight: bold;
          font-size: 14px;
          height: 30px;
          color: #316BAE; 
          td {
              text-align: center;
          }
          td[class="sheet"] {
              width: 40%;
          }
          td[class="created"] {
              width: 30%;
          }
          td[class="create-date"] {
              width: 30%;
          }
          // td[class="actions"] {
          //     width: 10%;
          // }
        }
    }
    .content-div {
      width: 98%;
      max-height: 350px;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 10px;
      }
      &::-webkit-scrollbar-thumb {
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        -webkit-transform: rotate3d(0,0,0,0);
        transform: rotate3d(0,0,0,0);
        -webkit-transition: background-color .1s ease-out,margin .1s ease-out,height .1s ease-out;
        transition: background-color .1s ease-out,margin .1s ease-out,height .1s ease-out;
        background-color: #9EB4BE;
        margin: 5px 10px 5px 0;
        border-radius: 20px;
        height: calc(100% - 40%);
        display: block;
      }
      &::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
        -webkit-border-radius: 15px;
        border-radius: 15px;
      }
    .content-table {
        margin-top: 5px;
        margin-left: -5%;
        width: 100%;
        tr {
          width: 100%;
          margin-top: 5px;
          font-weight: 600;
          height: 30px;
          padding: 2px;
          font-size: 13px;
          td {
              text-align: center;
              button {
              width: 50%;
              text-align: center;
              &:focus {
                  outline: none;
              }
              }
          }
          td[class="sheet"] {
              width: 40%;
          }
          td[class="created"] {
              width: 30%;
          }
          td[class="create-date"] {
              width: 30%;
          }
          // td[class="actions"] {
          //     width: 10%;
          // }
          &:hover {
            cursor: pointer;
            font-weight: bold;
          }
        }
      }
    }
  }
</style>