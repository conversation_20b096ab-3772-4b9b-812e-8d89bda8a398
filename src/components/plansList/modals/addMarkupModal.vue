<template>
    <modalBase @closeModal="$emit('close')">
        <template v-slot:modal-title >
            Enter the Markup Name
        </template>
        <template
            v-slot:modal-body
        >
        <form class="modal-content-plans" @submit.prevent="$emit('done', markupName)">
            <div class="modal-main">
                <div class="label">Enter the Markup name</div>
                <input style="padding:2px 10px 2px 10px" type="text" required v-model="markupName"/>
            </div>
            <input type="submit" class="btn btn-submit"/>
        </form>
        </template>
    </modalBase>
</template>

<script>
import modalBase from '@/components/plansList/modals/modalBase.vue'
export default {
    components:{
        modalBase
    },
    data(){
        return{
            markupName:''
        }
    },
}
</script>

<style scoped lang="scss">
.modal-content-plans{
    height: 200px;
    display:flex;
    justify-content: space-around;
    flex-direction: column;
    .modal-main{
        display:flex;
        justify-content: space-around;
        input{
            width: 325px;
            height: 28px;
            border: 1px solid black;
            border-radius: 5px;
            padding:2px 10px 2px 10px;
        }
    }
}

.btn{
    width:200px;
}
.btn-submit{
  padding: 0;
  border: none;
  background: #F25E45;
  cursor: pointer;
  border-radius: 5px;
  font-size: 14px;
  line-height: 18px;
  color: #fff;
  font-weight: bold;
  text-align: center;
  padding: 8px 10px;
  transition: all 0.15s ease-in-out;
    margin: 0px auto;
}
</style>