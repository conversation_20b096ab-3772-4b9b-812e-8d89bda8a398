# Takeoff Calculations for Measurement Tools

This document explains the mathematical formulas used for calculating measurements in different shape tools as actually implemented in the codebase.

## 1. Rectangle

### Area
The area of a rectangle is calculated using the formula:
```
Area = Length × Width
```

Where:
- Length and Width are calibrated measurements in inches
- Implementation from Rectangle.js:
  ```javascript
  findArea(length, width) {
    const areaInches =
      get(this.getcalibratedFeetInches(length), "onlyInches") *
      get(this.getcalibratedFeetInches(width), "onlyInches");
    return areaInches;
  }
  ```

### Perimeter
The perimeter of a rectangle is calculated using the formula:
```
Perimeter = 2 × (Length + Width)
```

Where:
- Length and Width are calibrated measurements in inches
- Implementation from Rectangle.js:
  ```javascript
  findPerimeter(length, width) {
    const perimeterInches =
      2 *
      (get(this.getcalibratedFeetInches(length), "onlyInches") +
        get(this.getcalibratedFeetInches(width), "onlyInches"));
    return perimeterInches;
  }
  ```

## 2. Triangle

### Area
The area of a triangle is calculated using the formula:
```
Area = (Base × Height) ÷ 2
```

Where:
- Base is the length of one side of the triangle (calibrated to inches)
- Height is the height of the triangle (calibrated to inches)
- Implementation from Triangle.js:
  ```javascript
  findArea(length, width) {
    const areaInches =
      get(this.getcalibratedFeetInches(length), "onlyInches") *
      get(this.getcalibratedFeetInches(width), "onlyInches") *
      0.5;
    return areaInches;
  }
  ```

### Perimeter
The perimeter of a triangle is calculated using the formula:
```
Perimeter = Base + 2 × Hypotenuse
```

Where:
- Base is the length of one side of the triangle (calibrated to inches)
- Hypotenuse is calculated using the Pythagorean theorem:
  ```
  Hypotenuse = √[(Base÷2)² + Height²]
  ```
- Implementation from Triangle.js:
  ```javascript
  findPerimeter(length, width) {
    const baseLength = get(this.getcalibratedFeetInches(length), "onlyInches");
    const heightLength = get(this.getcalibratedFeetInches(width), "onlyInches");
    
    // For a triangle, we need to calculate the hypotenuse
    const hypotenuse = Math.sqrt(Math.pow(baseLength / 2, 2) + Math.pow(heightLength, 2));
    
    // Perimeter = base + 2 * hypotenuse
    const perimeterInches = baseLength + 2 * hypotenuse;
    return perimeterInches;
  }
  ```

## 3. Ellipse

### Area
The area of an ellipse is calculated using the formula:
```
Area = π × (Semi-major axis) × (Semi-minor axis)
```

Where:
- π (pi) is approximately 22/7
- Semi-major axis is half the length of the longest diameter
- Semi-minor axis is half the length of the shortest diameter
- Implementation from Ellipse.js:
  ```javascript
  findArea(length, width) {
    // Area formula for ellipse is pi * a * b
    const areaInches =
      ((((22 / 7) * get(this.getcalibratedFeetInches(length), "onlyInches")) /
        2) *
        get(this.getcalibratedFeetInches(width), "onlyInches")) /
      2;
    return areaInches;
  }
  ```

### Perimeter
The perimeter (circumference) of an ellipse is approximated using Ramanujan's formula:
```
Perimeter = π × (a + b) × [1 + (3h)/(10 + √(4-3h))]
```

Where:
- a is the semi-major axis
- b is the semi-minor axis 
- h = (a-b)²/(a+b)²
- Implementation from Ellipse.js:
  ```javascript
  findPerimeter(length, width) {
    const a = get(this.getcalibratedFeetInches(length), "onlyInches") / 2;
    const b = get(this.getcalibratedFeetInches(width), "onlyInches") / 2;
    
    const pi = 22/7;
    const h = Math.pow((a - b), 2) / Math.pow((a + b), 2);
    const perimeterInches = pi * (a + b) * (1 + (3 * h) / (10 + Math.sqrt(4 - 3 * h)));
    
    return perimeterInches;
  }
  ```

## 4. Polygon

### Area
The area of a polygon is calculated using the Shoelace formula:
```
Area = |∑(x₁y₂ + x₂y₃ + ... + xₙy₁) - ∑(y₁x₂ + y₂x₃ + ... + yₙx₁)| ÷ 2
```

Where:
- (xᵢ, yᵢ) are the coordinates of each vertex of the polygon
- Implementation from Polygon.js:
  ```javascript
  calculatePolygonArea() {
    // Implementation of the Shoelace formula (also known as the surveyor's formula)
    let area = 0;
    const numPoints = this.points.length;

    for (let i = 0; i < numPoints; i++) {
      const j = (i + 1) % numPoints;
      area += this.points[i].x * this.points[j].y;
      area -= this.points[j].x * this.points[i].y;
    }

    area = Math.abs(area) / 2;
    return area / Math.pow(this.getZoomFactor(), 2);
  }
  ```

### Perimeter
The perimeter of a polygon is calculated by summing the length of all its sides:
```
Perimeter = |P₁P₂| + |P₂P₃| + ... + |PₙP₁|
```

Where:
- |PᵢPⱼ| is the distance between consecutive vertices
- Implementation from Polygon.js:
  ```javascript
  calculatePolygonPerimeter() {
    let perimeter = 0;
    const numPoints = this.points.length;

    for (let i = 0; i < numPoints; i++) {
      const j = (i + 1) % numPoints;
      perimeter += this.getLength(i, j);
    }

    return perimeter;
  }
  ```

## 5. Line (and Arrow)

### Length
The length of a line is calculated using the Euclidean distance formula:
```
Length = √[(x₂ - x₁)² + (y₂ - y₁)²]
```

Where:
- (x₁, y₁) is the starting point of the line
- (x₂, y₂) is the ending point of the line
- Implementation from Shape.js:
  ```javascript
  getSheetPixelDistance({ x1, y1, x2, y2 }, zoomFactor = 1) {
    return Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2) / zoomFactor;
  }
  ```

## 6. Ruler

The ruler tool uses the same distance calculation as the line tool:
```
Length = √[(x₂ - x₁)² + (y₂ - y₁)²]
```

## Conversion from Pixels to Real-World Measurements

To convert pixel measurements to real-world units, a scale factor is applied based on calibration data:

### Linear measurements
```
RealWorldMeasurement = PixelMeasurement × ScaleFactor
```

Where:
- ScaleFactor = TotalRealWorldInches ÷ CanvasPx
- TotalRealWorldInches = (Feet × 12) + Inches
- Implementation from Shape.js:
  ```javascript
  getcalibratedFeetInches(distance) {
    const measurementScale = MarkupCanvas.getMeasurementScale();
    const canvasPx = get(measurementScale, "canvasPx", 0);
    const feet = get(measurementScale, "feet", 0);
    const inches = get(measurementScale, "inches", 0);

    const totalInces = 12 * parseFloat(feet) + parseFloat(inches);

    const sheetInches = (distance * totalInces) / canvasPx;

    //show feet and inches
    return {
      feet: Math.floor(sheetInches / 12),
      inches: Math.round(sheetInches % 12),
      onlyInches: sheetInches,
      text: `${Math.floor(sheetInches / 12)}' ${Math.round(sheetInches % 12)}"`,
    }
  }
  ```

### Area measurements
```
RealWorldArea = PixelArea × ScaleFactor²
```

Where:
- ScaleFactor = TotalRealWorldInches ÷ CanvasPx
- Implementation from Shape.js:
  ```javascript
  getcalibratedArea(pixelArea) {
    const measurementScale = MarkupCanvas.getMeasurementScale();
    const canvasPx = get(measurementScale, "canvasPx", 0);
    const feet = get(measurementScale, "feet", 0);
    const inches = get(measurementScale, "inches", 0);

    const totalInches = 12 * parseFloat(feet) + parseFloat(inches);
    
    // For area, we need to square the scale factor
    const scaleFactor = totalInches / canvasPx;
    const areaInSquareInches = pixelArea * scaleFactor * scaleFactor;
    
    // Convert to square feet
    const areaInSquareFeet = areaInSquareInches / 144;
    
    return {
      squareFeet: Math.floor(areaInSquareFeet),
      squareInches: areaInSquareInches,
      text: `${Math.floor(areaInSquareFeet)} ft² ${Math.round((areaInSquareFeet - Math.floor(areaInSquareFeet)) * 144)} in²`
    }
  }
  ```

The final measurements are typically displayed in:
- Linear measurements: feet and inches (e.g., 5' 6")
- Area measurements: square feet (e.g., 25 ft²)