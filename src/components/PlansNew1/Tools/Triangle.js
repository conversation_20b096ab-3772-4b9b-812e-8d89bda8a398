import Rectangle from "./Rectangle";
import get from "lodash/get";

export default class Triangle extends Rectangle {
  constructor(points, id, fill, stroke, strokeWidth) {
    super(points, id, fill, stroke, strokeWidth);
    this.type = "triangle";
    this.uniqueId = id;
    this.isSelected = false;
  }

  drawRectangle(ctx) {
    const [p1, p2, p3, p4] = this.points;

    // Find the mid-point of the first two points (M1)
    var M1 = { x: (p1.x + p2.x) / 2, y: (p1.y + p2.y) / 2 };

    // Draw the triangle
    ctx.beginPath();
    ctx.moveTo(M1.x, M1.y);
    ctx.lineTo(p4.x, p4.y);
    ctx.lineTo(p3.x, p3.y);
    ctx.closePath();

    ctx.strokeStyle = this.stroke;
    ctx.fillStyle = this.fill;
    ctx.lineWidth = this.getZoomFactor() * this.strokeWidth;
    ctx.stroke();
    // eslint-disable-next-line lodash/prefer-lodash-method
    ctx.fill();
    if (this.isSelected) {
      this.drawHandles(ctx);
      this.formBoundingRectangle(ctx);
    }
    if (this.isSelected || this.isFocused) {
      this.formBoundingRectangle(ctx);
      this.addLengthArea(ctx);
    }
  }

  findArea(length, width) {
    const areaInches =
      get(this.getcalibratedFeetInches(length), "onlyInches") *
      get(this.getcalibratedFeetInches(width), "onlyInches") *
      0.5;
    return areaInches;
  }

  findPerimeter(length, width) {
    const baseLength = get(this.getcalibratedFeetInches(length), "onlyInches");
    const heightLength = get(this.getcalibratedFeetInches(width), "onlyInches");
    
    // For a triangle, we need to calculate the hypotenuse
    const hypotenuse = Math.sqrt(Math.pow(baseLength / 2, 2) + Math.pow(heightLength, 2));
    
    // Perimeter = base + 2 * hypotenuse
    const perimeterInches = baseLength + 2 * hypotenuse;
    return perimeterInches;
  }

  getTakeOff() {
    return {
      length: get(
        this.getcalibratedFeetInches(this.getLength(0, 1)),
        "onlyInches"
      ),
      width: get(
        this.getcalibratedFeetInches(this.getLength(1, 2)),
        "onlyInches"
      ),
      area: this.findArea(this.getLength(0, 1), this.getLength(1, 2)),
      perimeter: this.findPerimeter(this.getLength(0, 1), this.getLength(1, 2)),
      points: this.points
    };
  }
}
