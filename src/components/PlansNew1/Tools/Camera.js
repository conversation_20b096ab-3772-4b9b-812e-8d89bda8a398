import Icon from "./Icon";
import MarkupCanvas from "../Classes/MarkupCanvas";

export default class Camera extends Icon {
  constructor(...args) {
    super(...args);
    this.type = "camera";
    this.iconSrc = MarkupCanvas.cameraImageSrc;

  }

  handleDoubleClick(e, readOnlyMode) {
    const { x, y } = this.getMousePosition(e);
    if (this.isPointInPath(x, y) && (this.isSelected || readOnlyMode))
      return this.uniqueId;
    else return null;
  }
}
