import Shape from "./Shape";
import PaneZoom from "../Classes/PaneZoom";

const fixedHandleSize = 8;

class Hyperlink extends Shape {
  constructor(points, id) {
    super(points[0].x, points[0].y);
    this.rawPoints = points;
    this.points = PaneZoom.getCalculatedListCordinates(
      PaneZoom.getCanvasListCordinatesFromSheet(points)
    );
    // this.points = points;

    this.handleSize = fixedHandleSize;
    this.selectedHandle = null;
    this.selectedRect = null;
    this.isSelected = false;
    this.isFocused = false;
    this.uniqueId = id;
    this.type = "hyperlink";
    this.fill = "#00FF004D";
  }

  draw(ctx) {
    this.drawHyperlink(ctx);
    // this.saveMarkUp();
  }

  redraw(points, ctx) {
    this.points = PaneZoom.getCalculatedListCordinates(
      PaneZoom.getCanvasListCordinatesFromSheet(points)
    );
    this.drawHyperlink(ctx);
  }

  drawHyperlink(ctx) {
    ctx.beginPath();
    ctx.moveTo(this.points[0].x, this.points[0].y);
    for (let i = 1; i < this.points.length; i++) {
      ctx.lineTo(this.points[i].x, this.points[i].y);
    }
    ctx.closePath();
    ctx.fillStyle = this.fill;

    // eslint-disable-next-line lodash/prefer-lodash-method
    ctx.fill();
  }

  getMousePosition(e) {
    // get mouse position on canvas relevant to viewport
    const canvas = document.getElementById("canvas");
    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  }

  isPointInPath(x, y) {
    const ctx = document.createElement("canvas").getContext("2d");
    ctx.beginPath();
    ctx.moveTo(this.points[0].x, this.points[0].y);
    for (let i = 1; i < this.points.length; i++) {
      ctx.lineTo(this.points[i].x, this.points[i].y);
    }
    ctx.closePath();
    return ctx.isPointInPath(x, y);
  }

  handleDoubleClick(e, triggerFunc) {
    const { x, y } = this.getMousePosition(e);
    if (this.isPointInPath(x, y)) {
      triggerFunc(this.uniqueId);
    }
  }
}

export default Hyperlink;
