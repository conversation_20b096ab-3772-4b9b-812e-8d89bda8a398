import MarkupCanvas from "../Classes/MarkupCanvas";
import PaneZoom from "../Classes/PaneZoom";
import Markup<PERSON>son from "../Classes/MarkupJson";

import map from "lodash/map";

const TOLERANCE = 5;

class Pencil {
  constructor(points, id, isDrawing, stroke, strokeWidth) {
    this.points = points;
    this.uniqueId = id;
    this.stroke = stroke || MarkupCanvas.getStroke();
    this.strokeWidth = strokeWidth || MarkupCanvas.getStrokeWidth();
    this.type = "pencil";
    this.isSelected = false;
    this.selectedPath = null;
    this.isDrawing = isDrawing;
    this.boundingRectPoints = this.getBoundingRectPoints();
  }

  draw(ctx) {
    this.drawPath(ctx);
    this.saveMarkUp();
  }

  setDrawingFalse() {
    this.isDrawing = false;
  }

  setStroke(stroke) {
    this.stroke = stroke;
  }

  setStrokeWidth(strokeWidth) {
    this.strokeWidth = strokeWidth;
  }

  getTolerance() {
    return TOLERANCE * PaneZoom.getZoomFactor();
  }

  drawPath(ctx, saveMarkUp) {
    ctx.beginPath();
    //ctx.moveTo(this.points[0].x, this.points[0].y);
    for (let i = 1; i < this.points.length; i++) {
      ctx.lineTo(this.points[i].x, this.points[i].y);
    }
    ctx.strokeStyle = this.stroke;
    ctx.lineWidth = this.strokeWidth * PaneZoom.getZoomFactor();
    ctx.stroke();
    saveMarkUp ? this.saveMarkUp() : null;
    if (this.isSelected) {
      this.drawBoundingBox(ctx);
    }
  }

  getBoundingRectPoints() {
    const points = this.points;
    //find all cordinates of rectangle that contains the path
    const xList = map(points, (point) => point.x);
    const yList = map(points, (point) => point.y);
    const xMin = Math.min(...xList);
    const xMax = Math.max(...xList);
    const yMin = Math.min(...yList);
    const yMax = Math.max(...yList);
    const width = xMax - xMin || 5 * PaneZoom.getZoomFactor();
    const height = yMax - yMin || 5 * PaneZoom.getZoomFactor();
    return [
      { x: xMin, y: yMin },
      { x: xMin + width, y: yMin },
      { x: xMin + width, y: yMin + height },
      { x: xMin, y: yMin + height },
    ];
  }

  drawBoundingBox(ctx) {
    this.boundingRectPoints = this.getBoundingRectPoints();
    //draw rectangle
    ctx.beginPath();
    for (let i = 0; i < this.boundingRectPoints.length; i++) {
      ctx.lineTo(this.boundingRectPoints[i].x, this.boundingRectPoints[i].y);
    }
    ctx.closePath();
    ctx.strokeStyle = "blue";
    ctx.lineWidth = 0.5 * PaneZoom.getZoomFactor();
    ctx.stroke();
  }

  saveMarkUp() {
    MarkupJson.updateMarkupElement({
      id: this.uniqueId,
      type: this.type,
      points: PaneZoom.getOriginalListCordinates(this.points),
      [this.type === "pencil" ? "stroke" : "fill"]: this.stroke,
      strokeWidth: this.strokeWidth,
    });
  }

  handleClick(e) {
    const { x, y } = this.getMousePosition(e);
    if (
      this.isPointOnPath(e) ||
      (this.isPointInBoundingBox(x, y) && this.isSelected)
    ) {
      this.isSelected = true;
      MarkupCanvas.selectMarkupElement(this.uniqueId);
    } else {
      this.isSelected = false;
      MarkupCanvas.unSelectMarkupElement(this.uniqueId);
    }
  }

  isPointInBoundingBox(x, y) {
    const points = this.boundingRectPoints;

    const ctx = document.createElement("canvas").getContext("2d");
    ctx.beginPath();
    ctx.moveTo(points[0].x, points[0].y);
    for (let i = 1; i < points.length; i++) {
      ctx.lineTo(points[i].x, points[i].y);
    }
    ctx.closePath();
    return ctx.isPointInPath(x, y);
  }

  redraw(points, ctx) {
    this.points = points;
    this.setCalculatedCordinates();
    this.drawPath(ctx, false);
  }

  setCalculatedCordinates() {
    this.points = PaneZoom.getCalculatedListCordinates(this.points);
  }

  handleMouseMove(e, ctx, drawImage) {
    //console.log(e, ctx, "move", this.isDrawing);
    const { x, y } = this.getMousePosition(e);
    if (this.isDrawing) {
      this.points.push({ x, y });
      this.drawPath(ctx, true);
    }
    if (this.selectedPath) {
      drawImage({
        redrawSelectedMarkup: false,
      });
      this.move(x, y);

      //ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

      setTimeout(() => {
        this.draw(ctx);
      }, 1);
    }
  }

  move(x, y) {
    const dx = x - this.points[0].x;
    const dy = y - this.points[0].y;
    for (let i = 0; i < this.points.length; i++) {
      this.points[i].x += dx;
      this.points[i].y += dy;
    }
  }

  handleMouseDown(e, ctx) {
    //this.isDrawing = true;
    //check if clicked on path
    const { x, y } = this.getMousePosition(e);
    if (this.isPointOnPath(e)) {
      this.drawBoundingBox(ctx);
      this.selectedPath = true;
    } else if (this.isPointInBoundingBox(x, y) && this.isSelected) {
      this.selectedPath = true;
    }
  }

  isPointOnPath(e) {
    const tolerance = this.getTolerance();
    for (let i = 0; i < this.points.length; i++) {
      const { x, y } = this.points[i];
      const point = this.getMousePosition(e);
      const distance = Math.sqrt(
        Math.pow(x - point.x, 2) + Math.pow(y - point.y, 2)
      );
      if (distance <= tolerance) {
        return true;
      }
    }
    return false;
  }

  handleMouseUp() {
    this.isDrawing = false;
    this.selectedPath = false;
    //this.saveMarkUp();
  }

  getMousePosition(e) {
    // get mouse position on canvas relevant to viewport
    const canvas = document.getElementById("canvas");
    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  }
}

export default Pencil;
