import Line from "./Line";
import MarkupCanvas from "../Classes/MarkupCanvas";

//const this.getHandleSizeWithZoom()*10 = 15;

const HEAD_TO_HANDLE_RATIO = 1;
class Arrow extends Line {
  constructor(points, id, stroke, strokeWidth) {
    super(points, id, stroke, strokeWidth);
    this.stroke = stroke || MarkupCanvas.getStroke();
    this.type = "arrow";
  }

  //override drawLine
  drawLine(ctx) {
    super.drawLine(ctx);
    //add head to arrow
    this.addArrowHead(ctx);
  }

  addArrowHead(ctx) {
    const x1 = this.points[0].x;
    const y1 = this.points[0].y;
    const x2 = this.points[1].x;
    const y2 = this.points[1].y;
    var angle = Math.atan2(y2 - y1, x2 - x1);

    // calculate the coordinates of the arrowhead
    var arrowX =
      x2 -
      this.getHandleSizeWithZoom() *
        (HEAD_TO_HANDLE_RATIO + this.strokeWidth) *
        Math.cos(angle - Math.PI / 6);
    var arrowY =
      y2 -
      this.getHandleSizeWithZoom() *
        (HEAD_TO_HANDLE_RATIO + this.strokeWidth) *
        Math.sin(angle - Math.PI / 6);

    // draw the arrowhead
    ctx.beginPath();
    ctx.moveTo(arrowX, arrowY);
    ctx.lineTo(x2, y2);
    ctx.lineTo(
      x2 -
        this.getHandleSizeWithZoom() *
          (HEAD_TO_HANDLE_RATIO + this.strokeWidth) *
          Math.cos(angle + Math.PI / 6),
      y2 -
        this.getHandleSizeWithZoom() *
          (HEAD_TO_HANDLE_RATIO + this.strokeWidth) *
          Math.sin(angle + Math.PI / 6)
    );
    ctx.fillStyle = this.stroke;
    // eslint-disable-next-line lodash/prefer-lodash-method
    ctx.fill();
  }
}
export default Arrow;
