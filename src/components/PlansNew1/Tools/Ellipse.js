import Rectangle from "./Rectangle";
import get from "lodash/get";

export default class Ellipse extends Rectangle {
  constructor(points, id, fill, stroke, strokeWidth) {
    super(points, id, fill, stroke, strokeWidth);
    this.type = "ellipse";
    this.uniqueId = id;
    this.isSelected = false;
  }

  drawRectangle(ctx) {
    const [p1, p2, p3, p4] = this.points;

    // Calculate the midpoints of each side
    const m1 = [(p1.x + p2.x) / 2, (p1.y + p2.y) / 2];
    const m2 = [(p2.x + p3.x) / 2, (p2.y + p3.y) / 2];
    const m3 = [(p3.x + p4.x) / 2, (p3.y + p4.y) / 2];
    const m4 = [(p4.x + p1.x) / 2, (p4.y + p1.y) / 2];

    // Calculate the center of the ellipse
    const cx = (m1[0] + m3[0]) / 2;
    const cy = (m1[1] + m3[1]) / 2;

    // Calculate the width and height of the ellipse
    const a =
      Math.sqrt(Math.pow(m2[0] - m4[0], 2) + Math.pow(m2[1] - m4[1], 2)) / 2;
    const b =
      Math.sqrt(Math.pow(m1[0] - m3[0], 2) + Math.pow(m1[1] - m3[1], 2)) / 2;

    ctx.strokeStyle = this.stroke;
    ctx.fillStyle = this.fill;
    ctx.lineWidth = this.getZoomFactor() * this.strokeWidth;
    ctx.beginPath();

    // Draw the ellipse
    //ctx.moveTo(m1[0], m1[1]);
    ctx.ellipse(cx, cy, a, b, 0, 0, 2 * Math.PI);
    ctx.stroke();
    // eslint-disable-next-line lodash/prefer-lodash-method
    ctx.fill();
    ctx.closePath();
    if (this.isSelected) {
      this.drawHandles(ctx);
      this.formBoundingRectangle(ctx);
    }

    // this.addMeasurement(ctx);
    if (this.isSelected || this.isFocused) {
      this.formBoundingRectangle(ctx);
      this.addLengthArea(ctx);
    }
  }

  findArea(length, width) {
    // Area formula for ellipse is pi * a * b
    const areaInches =
      ((((22 / 7) * get(this.getcalibratedFeetInches(length), "onlyInches")) /
        2) *
        get(this.getcalibratedFeetInches(width), "onlyInches")) /
      2;
    return areaInches;
  }
  
  findPerimeter(length, width) {
    // Using Ramanujan's approximation for ellipse perimeter
    const a = get(this.getcalibratedFeetInches(length), "onlyInches") / 2;
    const b = get(this.getcalibratedFeetInches(width), "onlyInches") / 2;
    
    // Ramanujan's approximation: P ≈ π * [3(a+b) - √((3a+b)(a+3b))]
    const pi = 22/7;
    const h = Math.pow((a - b), 2) / Math.pow((a + b), 2);
    const perimeterInches = pi * (a + b) * (1 + (3 * h) / (10 + Math.sqrt(4 - 3 * h)));
    
    return perimeterInches;
  }
  
  getTakeOff() {
    return {
      length: get(
        this.getcalibratedFeetInches(this.getLength(0, 1)),
        "onlyInches"
      ),
      width: get(
        this.getcalibratedFeetInches(this.getLength(1, 2)),
        "onlyInches"
      ),
      area: this.findArea(this.getLength(0, 1), this.getLength(1, 2)),
      perimeter: this.findPerimeter(this.getLength(0, 1), this.getLength(1, 2)),
      points: this.points
    };
  }
}
