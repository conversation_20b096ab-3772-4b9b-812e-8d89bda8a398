import Pencil from "./Pencil";

import MarkupCanvas from "../Classes/MarkupCanvas";

export default class Highlighter extends Pencil {
  constructor(points, id, isDrawing, stroke, strokeWidth) {
    super(points, id, isDrawing, stroke, strokeWidth);
    this.type = "highlighter";
    this.stroke = stroke || MarkupCanvas.getFill({ nonWhite: true });
    this.strokeWidth = strokeWidth || MarkupCanvas.getStrokeWidth() * 4;
  }
}
