import Rectangle from "./Rectangle";
import <PERSON><PERSON><PERSON><PERSON> from "../Classes/MarkupJson";
import PaneZoom from "../Classes/PaneZoom";

const DEFAULT_FONT_SIZE = 12;
//const BASE_FONT_SIZE = 6;

export default class Text extends Rectangle {
  constructor(points, id, text, stroke, strokeWidth, fontSize) {
    super(points, id, null, stroke, strokeWidth);
    this.type = "text";
    this.fontSize = fontSize || DEFAULT_FONT_SIZE;
    this.text = text;
    this.isSelected = false;
    this.uniqueId = id;
  }

  // getText() {
  //   const text = prompt("Enter text");
  //   return text;
  // }

  drawRectangle(ctx) {
    ctx.save();
    ctx.beginPath();

    ctx.font = `${this.fontSize * PaneZoom.getZoomFactor()}px Arial`;
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.fillStyle = this.stroke;
    this.points = this.getBoundingRectPoints(ctx);

    ctx.fillText(
      this.text,
      this.diagonalMidPoint("x"),
      this.diagonalMidPoint("y")
    );
    ctx.closePath();
    ctx.restore();

    if (this.isSelected) {
      this.formBoundingRectangle(ctx);
    }
  }

  setFontSize(fontSize) {
    console.log("fontSize", fontSize);
    this.fontSize = fontSize;
  }

  getBoundingRectPoints(ctx) {
    // Find the width and height of the text
    const textWidth =
      ctx.measureText(this.text).width + 2 * this.getZoomFactor();
    const textHeight = parseInt(ctx.font);
    const { x, y } =
      this.points.lenth === 1 ? this.points[0] : this.diagonalMidPoint();
    const [p1, p2, p3, p4] = [
      { x: x - textWidth / 2, y: y - textHeight },
      { x: x + textWidth / 2, y: y - textHeight },
      { x: x + textWidth / 2, y: y + textHeight },
      { x: x - textWidth / 2, y: y + textHeight },
    ];
    return [p1, p2, p3, p4];
  }

  saveMarkUp() {
    MarkupJson.updateMarkupElement({
      id: this.uniqueId,
      type: this.type,
      points: PaneZoom.getOriginalListCordinates(this.points),
      fill: this.fill,
      stroke: this.stroke,
      //strokeWidth: this.strokeWidth,
      text: this.text,
      fontSize: this.fontSize,
    });
  }

  diagonalMidPoint(variable) {
    const p1 = this.points[0];
    const p3 = this.points[2];
    const midPoint = {
      x: (p1.x + p3.x) / 2,
      y: (p1.y + p3.y) / 2,
    };
    return variable ? midPoint[variable] : midPoint;
  }
}
