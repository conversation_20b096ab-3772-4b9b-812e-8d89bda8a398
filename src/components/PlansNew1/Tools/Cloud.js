import Rectangle from "./Rectangle";
import get from "lodash/get";

class Cloud extends Rectangle {
  constructor(points, id, fill, stroke, strokeWidth) {
    super(points, id, fill, stroke, strokeWidth);
    this.type = "cloud";
  }

  drawRectangle(ctx) {
    ctx.beginPath();
    ctx.strokeStyle = this.stroke;
    ctx.fillStyle = this.fill;
    ctx.lineWidth = this.getZoomFactor() * this.strokeWidth;
    // Get the coordinates of the rectangle
    var x1 = this.points[0].x;
    var y1 = this.points[0].y;
    var x2 = this.points[1].x;
    var y2 = this.points[1].y;
    var y3 = this.points[2].y;

    var rectWidth = x2 - x1;
    var rectHeight = y3 - y2;
    var rectX = x1;
    var rectY = y1;

    // Set the radius of the arcs
    var radius = 4 * this.getZoomFactor();

    // Set the distance between the arcs
    var spacing = 2 * radius;

    // Draw the top side of the rectangle with U-shaped arcs
    for (let i = 0; i < rectWidth / spacing - 2; i++) {
      // Draw the left arc of the U shape
      ctx.beginPath();
      ctx.arc(rectX + radius + i * spacing, rectY + radius, radius, Math.PI, 0);
      ctx.stroke();

      // Draw the right arc of the U shape
      ctx.beginPath();
      ctx.arc(
        rectX + radius + i * spacing + spacing,
        rectY + radius,
        radius,
        Math.PI,
        0
      );
      ctx.stroke();
    }

    // Draw the right side of the rectangle with U-shaped arcs
    for (let i = 0; i < rectHeight / spacing - 1; i++) {
      // Draw the top arc of the U shape
      ctx.beginPath();
      ctx.arc(
        rectX + rectWidth - radius,
        rectY + radius + i * spacing,
        radius,
        -Math.PI / 2,
        Math.PI / 2
      );
      ctx.stroke();
    }

    // Draw the bottom side of the rectangle with U-shaped arcs
    for (let i = 0; i < rectWidth / spacing - 2; i++) {
      // Draw the left arc of the U shape
      ctx.beginPath();
      ctx.arc(
        rectX + radius + i * spacing,
        rectY + rectHeight - radius,
        radius,
        0,
        -Math.PI
      );
      ctx.stroke();

      // Draw the right arc of the U shape
      ctx.beginPath();
      ctx.arc(
        rectX + radius + i * spacing + spacing,
        rectY + rectHeight - radius,
        radius,
        0,
        -Math.PI
      );
      ctx.stroke();
    }

    // Draw the left side of the rectangle with U-shaped arcs
    for (var i = 0; i < rectHeight / spacing - 1; i++) {
      // Draw the top arc of the U shape
      ctx.beginPath();
      ctx.arc(
        rectX + radius,
        rectY + radius + i * spacing,
        radius,
        Math.PI / 2,
        -Math.PI / 2
      );
      ctx.stroke();
    }

    ctx.moveTo(this.points[0].x, this.points[0].y);
    for (let i = 1; i < this.points.length; i++) {
      ctx.lineTo(this.points[i].x, this.points[i].y);
    }
    // eslint-disable-next-line lodash/prefer-lodash-method
    ctx.fill();
    if (this.isSelected) {
      this.drawHandles(ctx);
      this.formBoundingRectangle(ctx);
    }

    if (this.isSelected || this.isFocused) {
      this.formBoundingRectangle(ctx);
      this.addLengthArea(ctx);
    }
  }


  getTakeOff() {
    return {
      length: get(
        this.getcalibratedFeetInches(this.getLength(0, 1)),
        "onlyInches"
      ),
      width: get(
        this.getcalibratedFeetInches(this.getLength(1, 2)),
        "onlyInches"
      ),
      area: this.findArea(this.getLength(0, 1), this.getLength(1, 2)),
      perimeter: this.findPerimeter(this.getLength(0, 1), this.getLength(1, 2)),
      points: this.points
    };
  }
}

export default Cloud;
