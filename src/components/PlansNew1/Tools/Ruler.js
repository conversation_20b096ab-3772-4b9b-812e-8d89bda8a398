import Line from "./Line";
// import MarkupCanvas from "../Classes/MarkupCanvas";
// import get from "lodash/get";

const CROSS_TO_HANDLE_RATIO = 0.5;

class Ruler extends Line {
  constructor(points, id, stroke, strokeWidth) {
    super(points, id, stroke, strokeWidth);
    this.type = "ruler";
  }

  //override drawLine
  drawLine(ctx) {
    //draw line between two points
    super.drawLine(ctx);
    //add measurement text
    this.addCrossMarksAtEnds(ctx);
    // const { x1, y1, x2, y2 } = {
    //   x1: this.points[0].x,
    //   y1: this.points[0].y,
    //   x2: this.points[1].x,
    //   y2: this.points[1].y,
    // };
    // this.addMeasurement({ ctx, x1, y1, x2, y2 });
  }

  // addMeasurement({ ctx, x1, y1, x2, y2 }) {
  //   const midpoint_x = (x1 + x2) / 2;
  //   const midpoint_y = (y1 + y2) / 2;

  //   // set text style
  //   ctx.fillStyle = MarkupCanvas.mesurementFontColor;
  //   //add fontsize based on zoom
  //   ctx.font = `${
  //     this.getZoomFactor() * MarkupCanvas.mesurementFontSize
  //   }px Arial`;
  //   ctx.textAlign = "center";
  //   ctx.textBaseline = "middle";

  //   // draw measurement text
  //   const distance =
  //     this.getcalibratedDistance(
  //       this.getSheetPixelDistance({ x1, y1, x2, y2 }, this.getZoomFactor())
  //     ) || 0;
  //   ctx.fillText(`${distance}`, midpoint_x, midpoint_y);
  // }


 // Duplicate , need to be fixed
  getSheetPixelDistance() {
    const x1 = this.points[0].x;
    const y1 = this.points[0].y;
    const x2 = this.points[1].x;
    const y2 = this.points[1].y;

    return Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2) / this.getZoomFactor();
  }

  addCrossMarksAtEnds(ctx) {
    ctx.strokeStyle = this.stroke;

    const crossSize = CROSS_TO_HANDLE_RATIO * this.getHandleSizeWithZoom();

    // Set the cross size
    // ctx.lineWidth = crossSize

    // Loop through each point
    for (var i = 0; i < this.points.length; i++) {
      var point = this.points[i];

      // Draw the cross
      ctx.beginPath();
      ctx.moveTo(point.x - crossSize, point.y - crossSize * 2);
      ctx.lineTo(point.x + crossSize, point.y + crossSize * 2);
      ctx.moveTo(point.x - crossSize, point.y + crossSize * 2);
      ctx.lineTo(point.x + crossSize, point.y - crossSize * 2);
      ctx.stroke();
    }
  }
}
export default Ruler;
