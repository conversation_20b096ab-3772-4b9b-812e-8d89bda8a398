//import MarkupCanvas from "../Classes/MarkupCanvas";
import get from "lodash/get";
import MarkupCanvas from "../Classes/MarkupCanvas";
class Shape {
  constructor(x, y) {
    this.x = x;
    this.y = y;
  }

  draw() {
    //implement in child classes
  }

  // getcalibratedDistance(distance) {
  //   const measurementScale = MarkupCanvas.getMeasurementScale();
  //   const canvasPx = get(measurementScale, "canvasPx", 0);
  //   const feet = get(measurementScale, "feet", 0);
  //   const inches = get(measurementScale, "inches", 0);

  //   const totalInces = 12 * parseFloat(feet) + parseFloat(inches);

  //   const sheetInches = (distance * totalInces) / canvasPx;

  //   //show feet and inches
  //   return `${Math.floor(sheetInches / 12)}' ${Math.round(sheetInches % 12)}"`;
  // }

  getcalibratedFeetInches(distance) {
    const measurementScale = MarkupCanvas.getMeasurementScale();
    const canvasPx = get(measurementScale, "canvasPx", 0);
    const feet = get(measurementScale, "feet", 0);
    const inches = get(measurementScale, "inches", 0);

    const totalInces = 12 * parseFloat(feet) + parseFloat(inches);

    const sheetInches = (distance * totalInces) / canvasPx;

    //show feet and inches
    return {
      feet: Math.floor(sheetInches / 12),
      inches: Math.round(sheetInches % 12),
      onlyInches: sheetInches,
      text: `${Math.floor(sheetInches / 12)}' ${Math.round(sheetInches % 12)}"`,
    }
  }

  getcalibratedArea(pixelArea) {
    const measurementScale = MarkupCanvas.getMeasurementScale();
    const canvasPx = get(measurementScale, "canvasPx", 0);
    const feet = get(measurementScale, "feet", 0);
    const inches = get(measurementScale, "inches", 0);

    const totalInches = 12 * parseFloat(feet) + parseFloat(inches);
    
    // For area, we need to square the scale factor
    // pixelArea * (totalInches/canvasPx)²
    const scaleFactor = totalInches / canvasPx;
    const areaInSquareInches = pixelArea * scaleFactor * scaleFactor;
    
    // Convert to square feet
    const areaInSquareFeet = areaInSquareInches / 144; // 12² = 144 sq inches in a sq foot
    
    return {
      squareFeet: Math.floor(areaInSquareFeet),
      squareInches: areaInSquareInches,
      text: `${Math.floor(areaInSquareFeet)} ft² ${Math.round((areaInSquareFeet - Math.floor(areaInSquareFeet)) * 144)} in²`
    }
  }

  getSheetPixelDistance({ x1, y1, x2, y2 }, zoomFactor = 1) {
    return Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2) / zoomFactor;
  }
  resize() {}

  move() {
    //implement in child classes
  }
}

export default Shape;
