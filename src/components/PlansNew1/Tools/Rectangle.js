import Shape from "./Shape";
import MarkupCanvas from "../Classes/MarkupCanvas";
// import MarkupObjects from "../Classes/MarkupObjects";
import PaneZoom from "../Classes/PaneZoom";
import Markup<PERSON>son from "../Classes/MarkupJson";
import get from "lodash/get";

const fixedHandleSize = 8;
const fixedWidth = 100;

class Rectangle extends Shape {
  constructor(points, id, fill, stroke, strokeWidth, opacity) {
    super(points[0].x, points[0].y);
    this.points = this.getVertices(points);
    this.handleSize = fixedHandleSize;
    this.selectedHandle = null;
    this.selectedRect = null;
    this.isSelected = false;
    this.isFocused = false;
    this.uniqueId = id;
    this.type = "rect";
    this.fill = fill || MarkupCanvas.getFill();
    this.stroke = stroke || MarkupCanvas.getStroke();
    this.strokeWidth = strokeWidth || MarkupCanvas.getStrokeWidth();
    this.opacity = opacity || MarkupCanvas.getOpacity();
  }

  getVertices(points) {
    if (points.length === 4) {
      return points;
    } else if (points.length === 1) {
      const fixedWidt = (fixedWidth * PaneZoom.getZoomFactor()) / 2;
      return [
        { x: points[0].x - fixedWidt / 2, y: points[0].y - fixedWidt / 2 },
        { x: points[0].x + fixedWidt / 2, y: points[0].y - fixedWidt / 2 },
        { x: points[0].x + fixedWidt / 2, y: points[0].y + fixedWidt / 2 },
        { x: points[0].x - fixedWidt / 2, y: points[0].y + fixedWidt / 2 },
      ];
    }
  }

  setFill(fill) {
    this.fill = fill;
  }

  setStroke(stroke) {
    this.stroke = stroke;
  }

  setStrokeWidth(strokeWidth) {
    this.strokeWidth = strokeWidth;
  }

  getZoomFactor() {
    return PaneZoom.getZoomFactor();
  }

  getHandleSizeWithZoom() {
    return this.handleSize * Math.pow(PaneZoom.getZoomFactor(), 1 / 2);
  }

  setCalculatedCordinates() {
    this.points = PaneZoom.getCalculatedListCordinates(this.points);
  }

  draw(ctx) {
    this.drawRectangle(ctx);
    this.saveMarkUp();
  }

  drawRectangle(ctx) {
    ctx.beginPath();
    ctx.moveTo(this.points[0].x, this.points[0].y);
    for (let i = 1; i < this.points.length; i++) {
      ctx.lineTo(this.points[i].x, this.points[i].y);
    }
    ctx.closePath();
    ctx.strokeStyle = this.stroke;
    ctx.fillStyle = this.fill;
    ctx.lineWidth = PaneZoom.getZoomFactor() * this.strokeWidth;
    ctx.stroke();
    // eslint-disable-next-line lodash/prefer-lodash-method
    ctx.fill();

    if (this.isSelected) {
      this.drawHandles(ctx);
      this.formBoundingRectangle(ctx);
    }
    if (this.isSelected || this.isFocused) {
      this.formBoundingRectangle(ctx);
      this.addLengthArea(ctx);
    }
  }

  getLength(point1, point2) {
    const length = this.getSheetPixelDistance(
      {
        x1: get(this.points[point1], "x"),
        y1: get(this.points[point1], "y"),
        x2: get(this.points[point2], "x"),
        y2: get(this.points[point2], "y"),
      },
      this.getZoomFactor()
    );

    return length;
  }

  findArea(length, width) {
    const areaInches =
      get(this.getcalibratedFeetInches(length), "onlyInches") *
      get(this.getcalibratedFeetInches(width), "onlyInches");
    return areaInches;
  }

  findPerimeter(length, width) {
    const perimeterInches =
      2 *
      (get(this.getcalibratedFeetInches(length), "onlyInches") +
        get(this.getcalibratedFeetInches(width), "onlyInches"));
    return perimeterInches;
  }

  getTakeOff() {
    return {
      length: get(
        this.getcalibratedFeetInches(this.getLength(0, 1)),
        "onlyInches"
      ),
      width: get(
        this.getcalibratedFeetInches(this.getLength(1, 2)),
        "onlyInches"
      ),
      area: this.findArea(this.getLength(0, 1), this.getLength(1, 2)),
      perimeter: this.findPerimeter(this.getLength(0, 1), this.getLength(1, 2)),
      points: this.points,
    };
  }

  addLengthArea(ctx) {
    const length = this.getLength(0, 1);
    const width = this.getLength(1, 2);

    this.addMeasurement({
      ctx,
      distance: get(this.getcalibratedFeetInches(length), "text"),
      point1: this.type === "triangle" ? this.points[2] : this.points[0],
      point2: this.type === "triangle" ? this.points[3] : this.points[1],
    });

    this.addMeasurement({
      ctx,
      distance: get(this.getcalibratedFeetInches(width), "text"),
      point1: this.points[1],
      point2: this.points[2],
    });

    const areaInches = this.findArea(length, width);

    this.addMeasurement({
      ctx,
      distance: `${(areaInches / 144).toFixed(1)} ft²`,
      point1: this.points[0],
      point2: this.points[2],
    });
  }

  addMeasurement({ ctx, distance, point1, point2 }) {
    const midpoint_x = (point1.x + point2.x) / 2;
    const midpoint_y = (point1.y + point2.y) / 2;
    ctx.fillStyle = MarkupCanvas.mesurementFontColor;
    //add fontsize based on zoom
    ctx.font = `${
      this.getZoomFactor() * MarkupCanvas.mesurementFontSize
    }px Arial`;
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";

    ctx.fillText(`${distance}`, midpoint_x, midpoint_y);
  }

  formBoundingRectangle(ctx) {
    ctx.beginPath();
    ctx.moveTo(this.points[0].x, this.points[0].y);
    for (let i = 1; i < this.points.length; i++) {
      ctx.lineTo(this.points[i].x, this.points[i].y);
    }
    ctx.closePath();
    ctx.strokeStyle = "blue";
    ctx.lineWidth = 0.5 * PaneZoom.getZoomFactor();
    ctx.stroke();
  }

  saveMarkUp() {
    const length = get(
      this.getcalibratedFeetInches(this.getLength(0, 1)),
      "text"
    );
    const width = get(
      this.getcalibratedFeetInches(this.getLength(1, 2)),
      "text"
    );
    MarkupJson.updateMarkupElement({
      id: this.uniqueId,
      type: this.type,
      // name: 'vishalashdkjahdj',
      points: PaneZoom.getOriginalListCordinates(this.points),
      fill: this.fill,
      stroke: this.stroke,
      strokeWidth: this.strokeWidth,
      opacity: this.opacity,
      measurements: {
        dimensions: `{LXW = ${length}X${width}}`,
      },
      takeoff: this.getTakeOff(),
      // top: this.getTop(),
      // left: this.getLeft(),
    });
  }

  redraw(points, ctx) {
    this.points = this.getVertices(points);
    this.setCalculatedCordinates();
    this.drawRectangle(ctx);
    this.saveMarkUp();
  }

  drawHandles(ctx) {
    const handleSize = this.getHandleSizeWithZoom();
    for (let i = 0; i < this.points.length; i++) {
      ctx.strokeStyle = "blue";
      ctx.lineWidth = 0.5 * PaneZoom.getZoomFactor();
      ctx.strokeRect(
        this.points[i].x - handleSize / 2,
        this.points[i].y - handleSize / 2,
        handleSize,
        handleSize
      );
    }
  }

  handleMouseDown(e) {
    const { x, y } = this.getMousePosition(e);
    const handleSize = this.getHandleSizeWithZoom();
    // check if clicked on a handle
    for (let i = 0; i < this.points.length; i++) {
      if (
        x >= this.points[i].x - handleSize &&
        x <= this.points[i].x + handleSize &&
        y >= this.points[i].y - handleSize &&
        y <= this.points[i].y + handleSize
      ) {
        this.selectedHandle = i + 1;
        //change cursor
        document.body.style.cursor = "pointer";
        return;
      }
      this.selectedHandle = null;
    }

    //check if clicked inside the shape

    if (this.isPointInPath(x, y)) {
      this.selectedRect = true;
    } else {
      this.selectedRect = false;
    }
  }

  handleMouseMove(e, ctx, drawImage) {
    const { x, y } = this.getMousePosition(e);
    if (this.selectedHandle) {
      drawImage({
        redrawSelectedMarkup: false,
      });
      this.resize(x, y);

      //ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
      setTimeout(() => {
        this.draw(ctx);
      }, 1);
    } else if (this.selectedRect) {
      drawImage({
        redrawSelectedMarkup: false,
      });
      this.move(x, y);

      //ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

      setTimeout(() => {
        this.draw(ctx);
      }, 1);
    }
  }

  resize(x, y) {
    const handle = this.selectedHandle;
    if (handle === 1) {
      this.points[0].x = x;
      this.points[0].y = y;
      this.points[3].x = x;
      this.points[1].y = y;
    } else if (handle === 2) {
      this.points[1].x = x;
      this.points[1].y = y;
      this.points[0].y = y;
      this.points[2].x = x;
    } else if (handle === 3) {
      this.points[2].x = x;
      this.points[2].y = y;
      this.points[1].x = x;
      this.points[3].y = y;
    } else if (handle === 4) {
      this.points[3].x = x;
      this.points[3].y = y;
      this.points[2].y = y;
      this.points[0].x = x;
    }
  }

  move(x, y) {
    const dx = x - this.points[0].x;
    const dy = y - this.points[0].y;
    for (let i = 0; i < this.points.length; i++) {
      this.points[i].x += dx;
      this.points[i].y += dy;
    }
  }

  handleMouseUp() {
    this.selectedHandle = null;
    this.selectedRect = null;
    //remove cursor pointer
    document.body.style.cursor = "default";
  }

  getMousePosition(e) {
    // get mouse position on canvas relevant to viewport
    const canvas = document.getElementById("canvas");
    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  }

  isPointInPath(x, y) {
    const ctx = document.createElement("canvas").getContext("2d");
    ctx.beginPath();
    ctx.moveTo(this.points[0].x, this.points[0].y);
    for (let i = 1; i < this.points.length; i++) {
      ctx.lineTo(this.points[i].x, this.points[i].y);
    }
    ctx.closePath();
    return ctx.isPointInPath(x, y);
  }

  handleClick(e) {
    const { x, y } = this.getMousePosition(e);
    if (this.isPointInPath(x, y)) {
      this.isSelected = true;
      MarkupCanvas.selectMarkupElement(this.uniqueId);
    } else {
      this.isSelected = false;
      MarkupCanvas.unSelectMarkupElement(this.uniqueId);
    }
  }

  handleFocus(e) {
    const { x, y } = this.getMousePosition(e);
    if (this.isPointInPath(x, y)) {
      this.isFocused = true;
    } else {
      this.isFocused = false;
    }
  }

  handleDoubleClick(e) {
    const { x, y } = this.getMousePosition(e);
    if (this.isPointInPath(x, y) && this.isSelected) return this.uniqueId;
    else return null;
  }
}

export default Rectangle;
