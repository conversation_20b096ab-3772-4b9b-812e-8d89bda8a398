import Shape from "./Shape";
import MarkupCanvas from "../Classes/MarkupCanvas";
// import MarkupObjects from "../Classes/MarkupObjects";
import PaneZoom from "../Classes/PaneZoom";
import Markup<PERSON>son from "../Classes/MarkupJson";
import get from "lodash/get";

const fixedHandleSize = 8;

class Polygon extends Shape {
  constructor(points, id, fill, stroke, strokeWidth, opacity) {
    super(points[0].x, points[0].y);
    this.points = points;
    this.handleSize = fixedHandleSize;
    this.selectedHandle = null;
    this.selectedRect = null;
    this.isSelected = false;
    this.isFocused = false;
    this.uniqueId = id;
    this.type = "polygon";
    this.fill = fill || MarkupCanvas.getFill();
    this.stroke = stroke || MarkupCanvas.getStroke();
    this.strokeWidth = strokeWidth || MarkupCanvas.getStrokeWidth();
    this.opacity = opacity || MarkupCanvas.getOpacity();

    console.log(points, id);
  }

  setFill(fill) {
    this.fill = fill;
  }

  setStroke(stroke) {
    this.stroke = stroke;
  }

  setStrokeWidth(strokeWidth) {
    this.strokeWidth = strokeWidth;
  }

  getZoomFactor() {
    return PaneZoom.getZoomFactor();
  }

  getHandleSizeWithZoom() {
    return this.handleSize * Math.pow(PaneZoom.getZoomFactor(), 1 / 2);
  }

  setCalculatedCordinates() {
    this.points = PaneZoom.getCalculatedListCordinates(this.points);
  }

  draw(ctx) {
    this.drawRectangle(ctx);
    this.drawRoundCircles(ctx);
    this.saveMarkUp();
  }

  drawRectangle(ctx) {
    ctx.beginPath();
    ctx.moveTo(this.points[0].x, this.points[0].y);

    for (let i = 1; i < this.points.length; i++) {
      ctx.lineTo(this.points[i].x, this.points[i].y);
    }

    ctx.strokeStyle = this.stroke;
    ctx.fillStyle = this.fill;
    ctx.lineWidth = PaneZoom.getZoomFactor() * this.strokeWidth;
    ctx.stroke();

    // eslint-disable-next-line lodash/prefer-lodash-method
    ctx.fill();

    if (this.isSelected) {
      this.drawHandles(ctx);
      this.formBoundingRectangle(ctx);
    }
    if (this.isSelected || this.isFocused) {
      this.formBoundingRectangle(ctx);
      this.addLengthArea(ctx);
    }
  }

  drawRoundCircles(ctx) {
    for (let i = 0; i < this.points.length; i++) {
      ctx.beginPath();
      // draw the point with the handle size circle
      ctx.arc(
        this.points[i].x,
        this.points[i].y,
        this.getHandleSizeWithZoom() / 2,
        0,
        2 * Math.PI
      );
      ctx.lineWidth = (PaneZoom.getZoomFactor() * this.strokeWidth) / 2;
      ctx.strokeStyle = "red";
      ctx.stroke();
    }
  }

  formBoundingRectangle(ctx) {
    ctx.beginPath();
    ctx.moveTo(this.points[0].x, this.points[0].y);
    for (let i = 1; i < this.points.length; i++) {
      ctx.lineTo(this.points[i].x, this.points[i].y);
    }
    ctx.closePath();
    ctx.strokeStyle = "blue";
    ctx.lineWidth = 0.5 * PaneZoom.getZoomFactor();
    ctx.stroke();
  }

  getLength(point1, point2) {
    const length = this.getSheetPixelDistance(
      {
        x1: get(this.points[point1], "x"),
        y1: get(this.points[point1], "y"),
        x2: get(this.points[point2], "x"),
        y2: get(this.points[point2], "y"),
      },
      this.getZoomFactor()
    );

    return length;
  }

  calculatePolygonArea() {
    // Implementation of the Shoelace formula (also known as the surveyor's formula)
    let area = 0;
    const numPoints = this.points.length;

    for (let i = 0; i < numPoints; i++) {
      const j = (i + 1) % numPoints;
      area += this.points[i].x * this.points[j].y;
      area -= this.points[j].x * this.points[i].y;
    }

    area = Math.abs(area) / 2;
    return area / Math.pow(this.getZoomFactor(), 2);
  }

  calculatePolygonPerimeter() {
    let perimeter = 0;
    const numPoints = this.points.length;

    for (let i = 0; i < numPoints; i++) {
      const j = (i + 1) % numPoints;
      perimeter += this.getLength(i, j);
    }

    return perimeter;
  }

  getTakeOff() {
    const areaPixels = this.calculatePolygonArea();
    const perimeterPixels = this.calculatePolygonPerimeter();

    return {
      area: get(this.getcalibratedArea(areaPixels), "squareInches"),
      perimeter: get(this.getcalibratedFeetInches(perimeterPixels), "onlyInches"),
      points: this.points,
    };
  }

  addMeasurement({ ctx, distance, point1, point2 }) {
    const midpoint_x = (point1.x + point2.x) / 2;
    const midpoint_y = (point1.y + point2.y) / 2;
    ctx.fillStyle = MarkupCanvas.mesurementFontColor;
    // Add fontsize based on zoom
    ctx.font = `${
      this.getZoomFactor() * MarkupCanvas.mesurementFontSize
    }px Arial`;
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";

    ctx.fillText(`${distance}`, midpoint_x, midpoint_y);
  }

  addLengthArea(ctx) {
    // Add measurements for each side of the polygon
    const numPoints = this.points.length;

    for (let i = 0; i < numPoints; i++) {
      const j = (i + 1) % numPoints;
      const length = this.getLength(i, j);

      this.addMeasurement({
        ctx,
        distance: get(this.getcalibratedFeetInches(length), "text"),
        point1: this.points[i],
        point2: this.points[j],
      });
    }

    // Add area measurement in the center of the polygon
    const areaPixels = this.calculatePolygonArea();
    const areaSqFt = get(this.getcalibratedArea(areaPixels), "squareFeet").toFixed(1);


    // Calculate centroid of the polygon for area text placement
    let centroid_x = 0;
    let centroid_y = 0;

    for (let i = 0; i < numPoints; i++) {
      centroid_x += this.points[i].x;
      centroid_y += this.points[i].y;
    }

    centroid_x /= numPoints;
    centroid_y /= numPoints;

    ctx.fillStyle = MarkupCanvas.mesurementFontColor;
    ctx.font = `${
      this.getZoomFactor() * MarkupCanvas.mesurementFontSize
    }px Arial`;
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";

    ctx.fillText(`${areaSqFt} ft²`, centroid_x, centroid_y);
  }

  saveMarkUp() {
    const takeoff = this.getTakeOff();

    // Generate dimensions text with perimeter
    const perimeterText = get(
      this.getcalibratedFeetInches(this.calculatePolygonPerimeter()),
      "text"
    );

    const areaInches = takeoff.area;
    const areaSqFt = (areaInches / 144).toFixed(1);

    MarkupJson.updateMarkupElement({
      id: this.uniqueId,
      type: this.type,
      // name: 'vishal asdhsakhkjk',
      points: PaneZoom.getOriginalListCordinates(this.points),
      fill: this.fill,
      stroke: this.stroke,
      strokeWidth: this.strokeWidth,
      opacity: this.opacity,
      measurements: {
        dimensions: `{P = ${perimeterText}, A = ${areaSqFt} ft²}`,
      },
      takeoff: takeoff,
    });
  }

  updatePolygonVertex(newPoint) {
    this.points = [...this.points, newPoint];
    let polygonClosed = false;

    // check if the  last point is close to the first point with handle size
    const handleSize = this.getHandleSizeWithZoom();
    if (
      Math.abs(this.points[0].x - newPoint.x) <= handleSize &&
      Math.abs(this.points[0].y - newPoint.y) <= handleSize
    ) {
      polygonClosed = true;
      // update the last point to the first point
      this.points = [
        ...this.points.slice(0, this.points.length - 1),
        this.points[0],
      ];
    }

    MarkupJson.updateMarkupElement({
      id: this.uniqueId,
      points: PaneZoom.getOriginalListCordinates(this.points),
    });

    return { polygonClosed };
  }

  redraw(points, ctx) {
    this.points = points;
    this.setCalculatedCordinates();
    this.drawRectangle(ctx);
    this.drawRoundCircles(ctx);
    this.saveMarkUp();
  }

  drawHandles(ctx) {
    const handleSize = this.getHandleSizeWithZoom();
    for (let i = 0; i < this.points.length; i++) {
      ctx.strokeStyle = "blue";
      ctx.lineWidth = 0.5 * PaneZoom.getZoomFactor();
      ctx.strokeRect(
        this.points[i].x - handleSize / 2,
        this.points[i].y - handleSize / 2,
        handleSize,
        handleSize
      );
    }
  }

  handleMouseDown(e) {
    const { x, y } = this.getMousePosition(e);
    const handleSize = this.getHandleSizeWithZoom();
    // check if clicked on a handle
    for (let i = 0; i < this.points.length; i++) {
      if (
        x >= this.points[i].x - handleSize &&
        x <= this.points[i].x + handleSize &&
        y >= this.points[i].y - handleSize &&
        y <= this.points[i].y + handleSize
      ) {
        this.selectedHandle = i + 1;
        //change cursor
        document.body.style.cursor = "pointer";
        return;
      }
      this.selectedHandle = null;
    }

    //check if clicked inside the shape

    if (this.isPointInPath(x, y)) {
      this.selectedRect = true;
    } else {
      this.selectedRect = false;
    }
  }

  handleMouseMove(e, ctx, drawImage) {
    const { x, y } = this.getMousePosition(e);
    if (this.selectedHandle) {
      drawImage({
        redrawSelectedMarkup: false,
      });
      this.resize(x, y);

      //ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
      setTimeout(() => {
        this.draw(ctx);
      }, 1);
    } else if (this.selectedRect) {
      drawImage({
        redrawSelectedMarkup: false,
      });
      this.move(x, y);

      //ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

      setTimeout(() => {
        this.draw(ctx);
      }, 1);
    }
  }

  resize(x, y) {
    const handle = this.selectedHandle;
    // if the handle is the last point, update the first point as well, and vice versa
    if (handle === 1 || handle === this.points.length) {
      this.points[0].x = x;
      this.points[0].y = y;
      this.points[this.points.length - 1].x = x;
      this.points[this.points.length - 1].y = y;
    }
    // update the selected point
    else {
      this.points[handle - 1].x = x;
      this.points[handle - 1].y = y;
    }
  }

  move(x, y) {
    const dx = x - this.points[0].x;
    const dy = y - this.points[0].y;
    for (let i = 0; i < this.points.length; i++) {
      this.points[i].x += dx;
      this.points[i].y += dy;
    }
  }

  handleMouseUp() {
    this.selectedHandle = null;
    this.selectedRect = null;
    //remove cursor pointer
    document.body.style.cursor = "default";
  }

  getMousePosition(e) {
    // get mouse position on canvas relevant to viewport
    const canvas = document.getElementById("canvas");
    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  }

  isPointInPath(x, y) {
    const ctx = document.createElement("canvas").getContext("2d");
    ctx.beginPath();
    ctx.moveTo(this.points[0].x, this.points[0].y);
    for (let i = 1; i < this.points.length; i++) {
      ctx.lineTo(this.points[i].x, this.points[i].y);
    }
    ctx.closePath();
    return ctx.isPointInPath(x, y);
  }

  handleClick(e) {
    const { x, y } = this.getMousePosition(e);
    if (this.isPointInPath(x, y)) {
      this.isSelected = true;
      MarkupCanvas.selectMarkupElement(this.uniqueId);
    } else {
      this.isSelected = false;
      MarkupCanvas.unSelectMarkupElement(this.uniqueId);
    }
  }

  handleFocus(e) {
    const { x, y } = this.getMousePosition(e);
    if (this.isPointInPath(x, y)) {
      this.isFocused = true;
    } else {
      this.isFocused = false;
    }
  }

  handleDoubleClick(e) {
    const { x, y } = this.getMousePosition(e);
    if (this.isPointInPath(x, y) && this.isSelected) return this.uniqueId;
    else return null;
  }
}

export default Polygon;
