import Shape from "./Shape";
import MarkupCanvas from "../Classes/MarkupCanvas";
import PaneZoom from "../Classes/PaneZoom";
import Markup<PERSON>son from "../Classes/MarkupJson";
import get from "lodash/get";

const fixedHandleSize = 8;
const fixedWidth = 100;

class Line extends Shape {
  constructor(points, id, stroke, strokeWidth) {
    super(points[0].x, points[0].y);
    this.points = this.getVertices(points);
    this.handleSize = fixedHandleSize;
    this.selectedHandle = null;
    this.selectedLine = null;
    this.isSelected = false;
    this.isFocused = false;
    this.uniqueId = id;
    this.stroke = stroke || MarkupCanvas.getStroke();
    this.strokeWidth = strokeWidth || MarkupCanvas.getStrokeWidth();
    this.boudingRectPoints = [];
    this.type = "line";
  }

  getVertices(points) {
    if (points.length === 2) {
      return points;
    } else if (points.length === 1) {
      const fixedWidt = (fixedWidth * PaneZoom.getZoomFactor()) / 2;
      return [
        { x: points[0].x - fixedWidt / 2, y: points[0].y },
        { x: points[0].x + fixedWidt / 2, y: points[0].y },
      ];
    }
  }

  getZoomFactor() {
    return PaneZoom.getZoomFactor();
  }

  formBoundingRectangle(ctx) {
    //draw a rectangle which should be parallel to the x and y axis ad connecting line end points as its diagonal
    const x1 = this.points[0].x;
    const y1 = this.points[0].y;
    const x2 = this.points[1].x;
    const y2 = this.points[1].y;

    // Calculate the width and height of the rectangle
    var width = Math.abs(x2 - x1);
    var height = Math.abs(y2 - y1);

    // Calculate the top-left corner of the rectangle
    var rectX = Math.min(x1, x2);
    var rectY = Math.min(y1, y2);

    function getRectVertices() {
      return [
        { x: rectX, y: rectY },
        { x: rectX + width, y: rectY },
        { x: rectX + width, y: rectY + height },
        { x: rectX, y: rectY + height },
      ];
    }

    this.boudingRectPoints = getRectVertices();

    // Draw the rectangle
    ctx.beginPath();
    ctx.rect(rectX, rectY, width, height);
    ctx.strokeStyle = "blue";
    ctx.lineWidth = 0.5 * PaneZoom.getZoomFactor();
    ctx.stroke();
  }

  setFill(fill) {
    this.fill = fill;
  }

  setStroke(stroke) {
    this.stroke = stroke;
  }

  setStrokeWidth(strokeWidth) {
    this.strokeWidth = strokeWidth;
  }

  getHandleSizeWithZoom() {
    return this.handleSize * Math.pow(PaneZoom.getZoomFactor(), 1 / 2);
  }

  setCalculatedCordinates() {
    this.points = PaneZoom.getCalculatedListCordinates(this.points);
  }

  draw(ctx) {
    this.drawLine(ctx);
    this.saveMarkUp();
  }

  drawLine(ctx) {
    ctx.beginPath();
    ctx.moveTo(this.points[0].x, this.points[0].y);
    for (let i = 1; i < this.points.length; i++) {
      ctx.lineTo(this.points[i].x, this.points[i].y);
    }
    ctx.closePath();
    ctx.strokeStyle = this.stroke;
    ctx.lineWidth = PaneZoom.getZoomFactor() * this.strokeWidth;
    ctx.stroke();
    const { x1, y1, x2, y2 } = {
      x1: this.points[0].x,
      y1: this.points[0].y,
      x2: this.points[1].x,
      y2: this.points[1].y,
    };

    if (this.isSelected) {
      this.drawHandles(ctx);
      this.formBoundingRectangle(ctx);
    }

    if (this.isSelected || this.isFocused) {
      this.formBoundingRectangle(ctx);
    }

    if (this.isSelected || this.isFocused || this.type === "ruler") {
      this.addMeasurement({ ctx, x1, y1, x2, y2 });
    }
  }
  getDistance(format = "text") {
    const { x1, y1, x2, y2 } = {
      x1: this.points[0].x,
      y1: this.points[0].y,
      x2: this.points[1].x,
      y2: this.points[1].y,
    };
    const distance =
      get(
        this.getcalibratedFeetInches(
          this.getSheetPixelDistance({ x1, y1, x2, y2 }, this.getZoomFactor())
        ),
        format
      ) || 0;
    return distance;
  }

  getTakeOff() {
    return {
      length: this.getDistance("onlyInches"),
      points: this.points,
    };
  }

  addMeasurement({ ctx, x1, y1, x2, y2 }) {
    const midpoint_x = (x1 + x2) / 2;
    const midpoint_y = (y1 + y2) / 2;

    // set text style
    ctx.fillStyle = MarkupCanvas.mesurementFontColor;
    //add fontsize based on zoom
    ctx.font = `${
      this.getZoomFactor() * MarkupCanvas.mesurementFontSize
    }px Arial`;
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";

    // draw measurement text
    const distance = this.getDistance("text");
    ctx.fillText(`${distance}`, midpoint_x, midpoint_y);
  }

  saveMarkUp() {
    MarkupJson.updateMarkupElement({
      id: this.uniqueId,
      type: this.type,
      // name: this.type,
      points: PaneZoom.getOriginalListCordinates(this.points),
      stroke: this.stroke,
      strokeWidth: this.strokeWidth,
      measurements: {
        dimensions: `{L = ${this.getDistance()}}`,
      },
      takeoff: this.getTakeOff(),
    });
  }

  redraw(points, ctx) {
    this.points = this.getVertices(points);
    this.setCalculatedCordinates();
    this.drawLine(ctx);
    this.saveMarkUp();
  }

  drawHandles(ctx) {
    const handleSize = this.getHandleSizeWithZoom();
    for (let i = 0; i < this.points.length; i++) {
      ctx.strokeStyle = "blue";
      ctx.lineWidth = 0.5 * PaneZoom.getZoomFactor();
      ctx.strokeRect(
        this.points[i].x - handleSize / 2,
        this.points[i].y - handleSize / 2,
        handleSize,
        handleSize
      );
    }
  }

  handleMouseDown(e) {
    const { x, y } = this.getMousePosition(e);
    const handleSize = this.getHandleSizeWithZoom();
    // check if clicked on a handle
    for (let i = 0; i < this.points.length; i++) {
      if (
        x >= this.points[i].x - handleSize &&
        x <= this.points[i].x + handleSize &&
        y >= this.points[i].y - handleSize &&
        y <= this.points[i].y + handleSize
      ) {
        this.selectedHandle = i + 1;
        //change cursor
        document.body.style.cursor = "pointer";
        return;
      }
      this.selectedHandle = null;
    }

    //check if clicked on the line
    if (this.isPointInPath(x, y)) {
      this.selectedLine = true;
    } else {
      this.selectedLine = false;
    }
  }

  isPointOnLine(x, y) {
    const x1 = this.points[0].x;
    const y1 = this.points[0].y;
    const x2 = this.points[1].x;
    const y2 = this.points[1].y;
    const tolerance = 5;
    if (Math.abs(x1 - x2) <= tolerance) {
      return (
        Math.abs(x - x1) <= tolerance &&
        y >= Math.min(y1, y2) - tolerance &&
        y <= Math.max(y1, y2) + tolerance
      );
    } else {
      const m = (y2 - y1) / (x2 - x1);
      const c = y1 - m * x1;
      const yExpected = m * x + c;
      return (
        Math.abs(y - yExpected) < tolerance &&
        x >= Math.min(x1, x2) - tolerance &&
        x <= Math.max(x1, x2) + tolerance
      );
    }
  }

  isPointInPath(x, y) {
    if (this.boudingRectPoints.length === 0) return false;
    const ctx = document.createElement("canvas").getContext("2d");
    ctx.beginPath();
    ctx.moveTo(this.boudingRectPoints[0].x, this.boudingRectPoints[0].y);
    for (let i = 1; i < this.boudingRectPoints.length; i++) {
      ctx.lineTo(this.boudingRectPoints[i].x, this.boudingRectPoints[i].y);
    }
    ctx.closePath();
    return ctx.isPointInPath(x, y);
  }

  handleMouseMove(e, ctx, drawImage) {
    const { x, y } = this.getMousePosition(e);
    if (this.selectedHandle) {
      drawImage({
        redrawSelectedMarkup: false,
      });
      this.resize(x, y);

      setTimeout(() => {
        this.draw(ctx);
      }, 1);
    } else if (this.selectedLine) {
      drawImage({
        redrawSelectedMarkup: false,
      });
      this.move(x, y);

      setTimeout(() => {
        this.draw(ctx);
      }, 1);
    }
  }

  resize(x, y) {
    //resize for line
    const handle = this.selectedHandle;
    if (handle === 1) {
      this.points[0].x = x;
      this.points[0].y = y;
    } else if (handle === 2) {
      this.points[1].x = x;
      this.points[1].y = y;
    }
  }

  move(x, y) {
    //move for line
    const dx = x - this.points[0].x;
    const dy = y - this.points[0].y;
    for (let i = 0; i < this.points.length; i++) {
      this.points[i].x += dx;
      this.points[i].y += dy;
    }
  }

  handleMouseUp() {
    this.selectedHandle = null;
    this.selectedLine = null;
    //remove cursor pointer
    document.body.style.cursor = "default";
  }

  getMousePosition(e) {
    // get mouse position on canvas relevant to viewport
    const canvas = document.getElementById("canvas");
    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  }

  handleClick(e) {
    //handle click for line
    const { x, y } = this.getMousePosition(e);
    if (
      this.isPointOnLine(x, y) ||
      (this.isPointInPath(x, y) && this.isSelected)
    ) {
      this.isSelected = true;
      MarkupCanvas.selectMarkupElement(this.uniqueId);
    } else {
      this.isSelected = false;
      MarkupCanvas.unSelectMarkupElement(this.uniqueId);
    }
  }

  handleFocus(e) {
    //handle click for line
    const { x, y } = this.getMousePosition(e);
    if (
      this.isPointOnLine(x, y) ||
      (this.isPointInPath(x, y) && this.isFocused)
    ) {
      this.isFocused = true;
    } else {
      this.isFocused = false;
    }
  }

  handleDoubleClick(e) {
    const { x, y } = this.getMousePosition(e);
    if (this.isPointOnLine(x, y) && this.isSelected) {
      return this.uniqueId;
    }
    return null;
  }
}

export default Line;
