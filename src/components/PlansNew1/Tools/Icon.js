import Rectangle from "./Rectangle";



//import iconSrc from "./camera.svg";
export default class Icon extends Rectangle {
  constructor(points, id, fill, stroke, strokeWidth, iconSrc) {
    super(points, id, fill, stroke, strokeWidth);
    this.type = "icon";
    this.uniqueId = id;
    this.isSelected = false;
    this.iconSrc = iconSrc;
  }

  getWidth() {
    return this.points[1].x - this.points[0].x;
  }

  getHeight() {
    return this.points[2].y - this.points[1].y;
  }

  drawRectangle(ctx) {
    const image = new Image();
    image.src = this.iconSrc;

    image.onload = () => {
      ctx.drawImage(
        image,
        this.points[0].x,
        this.points[0].y,
        this.getWidth(),
        this.getHeight()
      );
    };
    //this.drawRectangle(ctx);
    if (this.isSelected) {
      this.drawHandles(ctx);
      this.formBoundingRectangle(ctx);
    }
  }
}
