/* eslint-disable lodash/prefer-lodash-method */
export const throttle = (cb, delay) => {
  let wait = false;

  return (...args) => {
    if (wait) {
      return;
    }

    cb(...args);
    wait = true;
    setTimeout(() => {
      wait = false;
    }, delay);
  };
};

export const generateUUID = () => {
  let uuid = "",
    i,
    random;
  for (i = 0; i < 32; i++) {
    random = (Math.random() * 16) | 0;
    if (i === 8 || i === 12 || i === 16 || i === 20) {
      uuid += "-";
    }
    uuid += (i === 12 ? 4 : i === 16 ? (random & 3) | 8 : random).toString(16);
  }
  return uuid;
};

// calculate color with color (rgb) and opacity
export const colorWithOpacity = (hexcode, opacity) => {
  // convert hex code to RGB values
  var r = parseInt(hexcode.slice(1, 3), 16);
  var g = parseInt(hexcode.slice(3, 5), 16);
  var b = parseInt(hexcode.slice(5, 7), 16);

  // convert opacity percentage to alpha value (0-255)
  var alpha = Math.round((opacity / 100) * 255);

  // return new hex code with opacity
  var alphaHex = alpha.toString(16).toUpperCase().padStart(2, "0");
  var newHexcode =
    "#" +
    r.toString(16).toUpperCase().padStart(2, "0") +
    g.toString(16).toUpperCase().padStart(2, "0") +
    b.toString(16).toUpperCase().padStart(2, "0") +
    alphaHex;
  return newHexcode;
};
