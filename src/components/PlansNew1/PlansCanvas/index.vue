<template>
  <div id="parent" class="canvas-container">
    <PulseLoader
      :loading="isPlansLoading"
      color="#f57947"
      class="pulse-loader"
    ></PulseLoader>
    <canvas id="canvas" ref="canvas" class="canvas-element" />
    <input
      id="fileInput"
      ref="componentRef"
      type="file"
      style="display: none"
      multiple
      accept="image/*"
      @change="onCameraImageAdd"
    />
  </div>
</template>

<script>
import PaneZoom from "../Classes/PaneZoom";
import MarkupCanvas from "../Classes/MarkupCanvas";
import MarkupJson from "../Classes/MarkupJson";
import Rectangle from "../Tools/Rectangle";
import Line from "../Tools/Line";
import Arrow from "../Tools/Arrow";
import Ruler from "../Tools/Ruler";
import Cloud from "../Tools/Cloud";
import Pencil from "../Tools/Pencil";
import Text from "../Tools/Text";
import Ellipse from "../Tools/Ellipse";
import Triangle from "../Tools/Triangle";
import Polygon from "../Tools/Polygon";
import Highlighter from "../Tools/Highlighter";
import Camera from "../Tools/Camera";
import Location from "../Tools/Location";
import Hyperlink from "../Tools/Hyperlink";
import debounce from "lodash/debounce";

import CameraUploadMixin from "./CameraUploadMixin";

import MarkupObjects from "../Classes/MarkupObjects";
import HyperlinkObjects from "../Classes/HyperlinkObjects";
import { throttle } from "../util";

import { mapActions, mapGetters } from "vuex";
import PulseLoader from "vue-spinner/src/PulseLoader";
import { PLAN_MARKUP_MODES } from "@/constants";

import forEach from "lodash/forEach";
import get from "lodash/get";
import find from "lodash/find";

const THROTTLE_DELAY = 100;

export default {
  name: "PlansCanvasCore",
  components: {
    PulseLoader,
  },
  mixins: [CameraUploadMixin],
  props: {
    sheetUrl: {
      type: String,
      // default: require("/assets/plans.jpg"),
      default: "",
    },
    objMarkup: {
      type: Object,
      default: () => {},
    },
    sheetId: {
      type: String,
      default: "",
    },
    prjId: {
      type: String,
      default: "",
    },
    sheetHyperlinks: {
      type: Array,
      default: () => [],
    },
    onClickHyperlink: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {

      addedEventListners: false,
      addedHyperlinkEventListners: false,
      //sheetUrl:
      //  "https://plans-linarc-qa.s3.amazonaws.com/Sqr2QiWJNrQbiTFW/QbAhm8BYs46SSmdS/nYJXGiDGk4wrCYKn/636ed54f-b998-48b4-b7bb-30a65427abea.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAYBJJYMOLDEJ6WC3S%2F20230317%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20230317T055959Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=63b83ee4a0f4758ae8ebfd7a48c5c26e2feb5f7a12f66c7693a8b00cc799a1f2",
      tools: {},
      tool: null,
      savedMarkups: {},
      isDrawingWithPencil: false,
      isPlansLoading: true,
      imageLoaded: false,
      selectedRulerObject: null,
      selectedCameraObject: null,
      imageMeta: null,
      tempPolygonId: null,
      hyperlinkData: [],
      planSheetCaliberated: false,
      cachedImage: null, // Cache the loaded image
      cachedImageUrl: null, // Track which URL is cached
    };
  },
  computed: {
    ...mapGetters({
      getCurrentMarkupJson: "MarkupCanvas/getCurrentMarkupJson",
      sheetMetaById: "MarkupCanvas/sheetMetaById",
      getCurrentMarkupId: "MarkupCanvas/getCurrentMarkupId",
      getCurrentMarkupMode: "MarkupCanvas/getCurrentMarkupMode",
    }),
    isReadOnlyMode() {
      return MarkupCanvas.getReadOnly();
    },

    getPlfId() {
      return this.sheetId;
    },
    getMarkupId() {
      return this.getCurrentMarkupId;
    },
  },
  watch: {
    sheetUrl(newUrl, oldUrl) {
      if (newUrl !== oldUrl) {
        // Clear cache when URL changes
        this.cachedImage = null;
        this.cachedImageUrl = null;
        this.imageLoaded = false;
      }
    }
  },
  async mounted() {
    this.hyperlinkData = this.sheetHyperlinks;

    //reset zoom , pane , markupcanvas objects
    this.resetSheet();

    //set Default Stroke Color and fill color
    this.setStroke({
      color: MarkupCanvas.getStroke(),
    });
    this.setFill({
      color: MarkupCanvas.getFill(),
      hardness: MarkupCanvas.getOpacity(),
    });

    //setPaneZoom and markupcanvas to vuex
    // this.setMarkupCanvas(MarkupCanvas);
    // this.setPaneZoom(PaneZoom);

    this.tools = MarkupCanvas.getAllTools();
    const canvas = document.getElementById("canvas");

    if (this.sheetUrl) {
      const parent = document.getElementById("parent");

      canvas.width = parent.offsetWidth;
      canvas.height = parent.offsetHeight;

      await this.drawCanvasWithSheet({
        redrawSelectedMarkup: true,
      });

      //store vuex data
      await this.setPlanMarkup({
        plfId: this.sheetId,
        prjId: this.prjId,
      });
      //set ReadOnly Mode
      MarkupCanvas.setReadOnly(
        !(
          this.getCurrentMarkupMode === PLAN_MARKUP_MODES.EDIT ||
          this.getCurrentMarkupMode === PLAN_MARKUP_MODES.DRAFT
        )
      );
      // this.setCaliberationScale(this.sheetId);
      this.savedMarkups = this.getCurrentMarkupJson;
      await this.drawSavedMarkups();
    }
    canvas.addEventListener("mousedown", (e) => {
      if (this.checkifClickedonCanvasImage(e)) {
        switch (this.tool) {
          case "rect":
            this.setRectangle(e.offsetX, e.offsetY);
            break;
          case "line":
            this.setLine(e.offsetX, e.offsetY);
            break;
          case "arrow":
            this.setArrow(e.offsetX, e.offsetY);
            break;
          case "ruler":
            this.setRuler(e.offsetX, e.offsetY);
            //this.$emit("showScaleModal");
            break;
          case "cloud":
            this.setCloud(e.offsetX, e.offsetY);
            break;
          case "pencil":
            this.setPencil(e.offsetX, e.offsetY);
            this.isDrawingWithPencil = true;
            break;
          case "text":
            //this.setText(e.offsetX, e.offsetY);
            this.addTextFromModal(e.offsetX, e.offsetY, "add");
            break;
          case "ellipse":
            this.setEllipse(e.offsetX, e.offsetY);
            break;
          case "triangle":
            this.setTriangle(e.offsetX, e.offsetY);
            break;
          case "highlighter":
            this.setHighlighter(e.offsetX, e.offsetY);
            this.isDrawingWithPencil = true;
            break;
          case "camera":
            this.setCamera(e.offsetX, e.offsetY);
            break;
          case "locationpin":
            this.setLocation(e.offsetX, e.offsetY);
            break;
          case "polygon":
            if (this.tempPolygonId)
              this.updatePolygon({ x: e.offsetX, y: e.offsetY });
            else this.setPolygon(e.offsetX, e.offsetY);

            break;
          default:
            break;
        }
        if (this.tool !== "polygon") this.tool = null;
      }
    });
  },
  methods: {
    ...mapActions({
      setPlanMarkup: "MarkupCanvas/setPlanMarkup",
      updateMarkupObject: "MarkupCanvas/updateMarkupObject",
      updateMarkupJson: "MarkupCanvas/updateMarkupJson",
      addMarkupObject: "MarkupCanvas/addMarkupObject",
      setStroke: "PlanMarkup/setStroke",
      setFill: "PlanMarkup/setFill",
      setMarkupCanvas: "MarkupCanvas/setMarkupCanvas",
      setPaneZoom: "MarkupCanvas/setPaneZoom",
      resetDraftMarkup: "MarkupCanvas/resetDraftMarkup",
      addRulerScale: "MarkupCanvas/addRulerScale",
      updateSheetMeta: "MarkupCanvas/updateSheetMeta",
    }),
    toolSelected(tool) {
      this.tool = tool;
    },
    onCameraImageAdd(event) {
      this.handleFileChange(
        event,
        this.objMarkup,
        this.selectedCameraObject.uniqueId
      );
    },
    addTextFromModal(x, y, mode) {
      setTimeout(() => {
        this.$emit("openTextModal", { x, y, mode });
      }, 100);
      //this.$emit("openTextModal", { x, y });
    },
    async canvasToBlob() {
      //generate blob file to upload to s3
      PaneZoom.resetPaneZoom();
      this.imageLoaded = false;
      await this.drawCanvasWithSheet({
        redrawSelectedMarkup: true,
      });

      //return blob after 100 ms
      return new Promise((resolve) => {
        setTimeout(async () => {
          const canvas = document.getElementById("canvas");
          const blob = await new Promise((resolve) => canvas.toBlob(resolve));
          resolve({
            imageSrc: blob,
            thumbSrc: blob,
          });
        }, 100);
      });
    },
    resetSheet() {
      this.resetDraftMarkup();
      PaneZoom.resetPaneZoom();
      MarkupCanvas.resetMarkupCanvas();
      MarkupObjects.resetMarkupObjects();
      MarkupJson.resetMarkupJson();
      HyperlinkObjects.resetHyperlinkObjects();
      // Clear image cache
      this.cachedImage = null;
      this.cachedImageUrl = null;
      this.imageLoaded = false;
    },
    setColor(color) {
      if (color.type === "stroke") {
        MarkupCanvas.setStroke(color.color);
        this.setSelectedElementColor(color.color, color.type);
      } else {
        MarkupCanvas.setFill(color.color);
        this.setSelectedElementColor(MarkupCanvas.getFill(), color.type);
      }
    },

    setOpacity(opacity) {
      MarkupCanvas.setOpacity(opacity);
      this.setSelectedElementColor(MarkupCanvas.getFill(), "fill");
    },

    setStrokeWidth(val) {
      const width = val * PaneZoom.scale;
      MarkupCanvas.setStrokeWidth(width);
      this.setSelectedElementColor(MarkupCanvas.getStroke(), "stroke", width);
    },

    setSelectedElementColor(color, type, width) {
      const selectedElements = MarkupCanvas.getSelectedMarkupElements();
      if (selectedElements.length > 0) {
        for (let i = 0; i < selectedElements.length; i++) {
          const object = MarkupObjects.getMarkupObject(selectedElements[i]);
          if (type === "stroke") {
            if (width) object.setStrokeWidth(width);
            else object.type === "highlighter" ? "" : object.setStroke(color);
            if (object.type === "text" && width) object.setFontSize(width);
            object.saveMarkUp();
          } else {
            object.type === "highlighter"
              ? object.setStroke(color)
              : object.setFill(color);
            object.saveMarkUp();
          }
        }
      }
      this.drawCanvasWithSheet({
        redrawSelectedMarkup: true,
      });
    },

    drawSavedMarkups() {
      const savedMarkups = this.savedMarkups;
      for (let key in savedMarkups) {
        const markup = savedMarkups[key];

        switch (markup.type) {
          case "arrow":
            MarkupObjects.addMarkupObject(
              markup.id,
              new Arrow(
                markup.points,
                markup.id,
                markup.stroke,
                markup.strokeWidth
              )
            );
            break;
          case "ruler":
            MarkupObjects.addMarkupObject(
              markup.id,
              new Ruler(
                markup.points,
                markup.id,
                markup.stroke,
                markup.strokeWidth
              )
            );
            break;
          case "line":
            MarkupObjects.addMarkupObject(
              markup.id,
              new Line(
                markup.points,
                markup.id,
                markup.stroke,
                markup.strokeWidth
              )
            );
            break;
          case "rect":
            MarkupObjects.addMarkupObject(
              markup.id,
              new Rectangle(
                markup.points,
                markup.id,
                markup.fill,
                markup.stroke,
                markup.strokeWidth
              )
            );
            break;
          case "cloud":
            MarkupObjects.addMarkupObject(
              markup.id,
              new Cloud(
                markup.points,
                markup.id,
                markup.fill,
                markup.stroke,
                markup.strokeWidth
              )
            );
            break;
          case "triangle":
            MarkupObjects.addMarkupObject(
              markup.id,
              new Triangle(
                markup.points,
                markup.id,
                markup.fill,
                markup.stroke,
                markup.strokeWidth
              )
            );
            break;
          case "ellipse":
            MarkupObjects.addMarkupObject(
              markup.id,
              new Ellipse(
                markup.points,
                markup.id,
                markup.fill,
                markup.stroke,
                markup.strokeWidth
              )
            );
            break;
          case "text":
            MarkupObjects.addMarkupObject(
              markup.id,
              new Text(
                markup.points,
                markup.id,
                markup.text,
                markup.stroke,
                markup.strokeWidth,
                markup.fontSize
              )
            );
            break;
          case "pencil":
            MarkupObjects.addMarkupObject(
              markup.id,
              new Pencil(
                markup.points,
                markup.id,
                false,
                markup.stroke,
                markup.strokeWidth,
                false
              )
            );
            break;
          case "highlighter":
            MarkupObjects.addMarkupObject(
              markup.id,
              new Highlighter(
                markup.points,
                markup.id,
                false,
                markup.fill,
                markup.strokeWidth
              )
            );
            break;
          case "camera":
            MarkupObjects.addMarkupObject(
              markup.id,
              new Camera(markup.points, markup.id)
            );
            break;
          case "locationpin":
            MarkupObjects.addMarkupObject(
              markup.id,
              new Location(markup.points, markup.id)
            );
            break;
          case "polygon":
            MarkupObjects.addMarkupObject(
              markup.id,
              new Polygon(
                markup.points,
                markup.id,
                markup.fill,
                markup.stroke,
                markup.strokeWidth
              )
            );
            break;
        }
      }
      MarkupJson.setSavedMarkupElements(savedMarkups);
    },
    updateMarkupJsonElements(id) {
      this.savedMarkups = this.getCurrentMarkupJson;
      MarkupJson.deleteMarkupElement(id);
    },

    async drawCanvasWithSheet({ redrawSelectedMarkup }) {
      const canvas = this.$refs.canvas;
      if (!canvas.getContext("2d")) return;
      const ctx = canvas.getContext("2d");
      const scope = this;

      // Check if we can use cached image
      if (scope.cachedImage && scope.cachedImageUrl === scope.sheetUrl) {
        scope.renderCachedImage(ctx, canvas, scope.cachedImage, { redrawSelectedMarkup });
        return;
      }

      function addImage({ redrawSelectedMarkup }) {
        const image = new Image();
        image.crossOrigin = "anonymous";
        image.src = scope.sheetUrl;
        // console.log(
        //   image.width,
        //   image.height,
        //   "Actual image.width, image.height",
        //   scope.sheetUrl
        // );

        // determine the scaling factor
        const widthScale = canvas.offsetWidth / image.width;
        const heightScale = canvas.offsetHeight / image.height;
        const scale = Math.min(widthScale, heightScale);
        if (
          scale !== Infinity &&
          scale !== PaneZoom.scale &&
          scale !== 0.12345
        ) {
          PaneZoom.setScale({
            scale,
            scaleX: widthScale,
            scaleY: heightScale,
          });

          scope
            .setPlanMarkup({
              plfId: scope.sheetId,
              prjId: scope.prjId,
            })
            .then(
              () => {
                scope.savedMarkups = scope.getCurrentMarkupJson;
                scope.drawSavedMarkups();
                scope.drawHyperlinkReferences();
                scope.setCaliberationScale(scope.sheetId);
                scope.planSheetCaliberated = true;
              },
              (err) => {
                console.log(err, "err");
              }
            )
            .then(() => {
              addImage({ redrawSelectedMarkup });
            });
        }

        // scale the image
        image.width *= scale;
        image.height *= scale;

        if (image.width && image.height) {
          scope.imageMeta = {
            width: image.width,
            height: image.height,
          };

          // console.log(
          //   image.width,
          //   image.height,
          //   scale,
          //   "Canvas image.width, image.height,scale"
          // );
        }

        // if (PaneZoom.getPaneInitialized() === false) {
        //   PaneZoom.setPane(
        //     (canvas.width / 2 - image.width / 2),
        //     canvas.height / 2 - image.height / 2
        //   );
        //   PaneZoom.setPaneInitialized(true);
        // }

        const setPositionForImage = (canvas, image) => {
          if (image.width && image.height) {
            PaneZoom.setPaneX(canvas.offsetWidth / 2 - image.width / 2);
            scope.optimizeCanvasImage(canvas, ctx);
            scope.imageLoaded = true;
          }
        };

        image.onload = () => {
          // Cache the loaded image
          scope.cachedImage = image;
          scope.cachedImageUrl = scope.sheetUrl;

          if (!scope.imageLoaded) {
            setPositionForImage(canvas, image);
          }

          scope.renderCachedImage(ctx, canvas, image, { redrawSelectedMarkup });
        };

        image.onerror = () => {
          console.log("error");
        };

        if (!image.complete) {
          const throttleAddImage = debounce(() => {
            // console.log("request animation frame", scope.sheetUrl, image);
            window.requestAnimationFrame(() => {
              addImage({ redrawSelectedMarkup });
              // console.log("animation frame");
            });
          }, 500);
          throttleAddImage();
          scope.isPlansLoading = true;
        } else {
          scope.isPlansLoading = false;
        }
      }

      await addImage({ redrawSelectedMarkup });

      if (!scope.addedEventListners) addDragListeners(canvas, ctx);
      if (!scope.addedHyperlinkEventListners)
        addHyperlinkListeners(canvas, ctx);

      function addDragListeners(canvas, ctx) {
        scope.addedEventListners = true;
        let isDragging = false;
        let lastX = 0;
        let lastY = 0;

        canvas.addEventListener("mousedown", (e) => {
          e.preventDefault();
          isDragging = true;
          lastX = e.clientX;
          lastY = e.clientY;
          //test event delegation

          const throttleMouseDown = throttle(() => {
            for (const key in MarkupObjects.getMarkupObjects()) {
              const object = MarkupObjects.getMarkupObject(key);
              object.handleMouseDown(e, ctx, addImage);
            }
          }, THROTTLE_DELAY);
          if (!scope.isReadOnlyMode) throttleMouseDown();
        });

        canvas.addEventListener("click", (e) => {
          const throttleClick = throttle((type) => {
            for (const key in MarkupObjects.getMarkupObjects()) {
              const object = MarkupObjects.getMarkupObject(key);
              if (type === "select") object.handleClick(e, ctx, addImage);
              else if (type === "focus" && object.handleFocus)
                object.handleFocus(e, ctx, addImage);
            }
            addImage({ redrawSelectedMarkup });
          }, THROTTLE_DELAY);
          if (scope.tool === null && !scope.isReadOnlyMode) {
            throttleClick("select");
          } else if (scope.tool === null && scope.isReadOnlyMode) {
            throttleClick("focus");
          }
        });

        canvas.addEventListener("mousemove", (e) => {
          //console.log(MarkupObjects.getMarkupObjects(), "fck");
          const throttleObjectMove = throttle(() => {
            for (const key in MarkupObjects.getMarkupObjects()) {
              const object = MarkupObjects.getMarkupObject(key);
              object.handleMouseMove(e, ctx, addImage);
            }
          }, THROTTLE_DELAY);
          if (
            (MarkupCanvas.isMarkupsSelected() || scope.isDrawingWithPencil) &&
            !scope.isReadOnlyMode &&
            scope.checkifClickedonCanvasImage(e)
          )
            throttleObjectMove();

          const throttlePaneZoom = throttle(() => {
            const deltaX = e.clientX - lastX;
            const deltaY = e.clientY - lastY;
            const transformedDeltaX = deltaX / PaneZoom.getZoomFactor();
            const transformedDeltaY = deltaY / PaneZoom.getZoomFactor();

            addImage({ redrawSelectedMarkup });
            PaneZoom.setPaneX(PaneZoom.getPaneX() + transformedDeltaX);
            PaneZoom.setPaneY(PaneZoom.getPaneY() + transformedDeltaY);
            lastX = e.clientX;
            lastY = e.clientY;
          }, THROTTLE_DELAY);

          if (
            isDragging &&
            !MarkupCanvas.isMarkupsSelected() &&
            !scope.isDrawingWithPencil
          ) {
            throttlePaneZoom();
          }
        });

        canvas.addEventListener("wheel", (event) => {
          event.preventDefault();

          const throttleZoom = throttle(() => {
            // Calculate new zoom level
            var newZoomLevel = PaneZoom.getZoomFactor() + event.deltaY * -0.001;

            //Restrict zoom level to between 0.1 and 10
            if (newZoomLevel < 0.5) {
              newZoomLevel = 0.5;
            } else if (newZoomLevel > 10) {
              newZoomLevel = 10;
            }

            // Calculate new pan values
            var rect = canvas.getBoundingClientRect();
            var mouseX = event.clientX - rect.left;
            var mouseY = event.clientY - rect.top;
            var newPanX =
              ((mouseX - PaneZoom.getPaneX()) / PaneZoom.getZoomFactor()) *
              newZoomLevel;
            var newPanY =
              ((mouseY - PaneZoom.getPaneY()) / PaneZoom.getZoomFactor()) *
              newZoomLevel;

            PaneZoom.setZoomFactor(newZoomLevel);
            PaneZoom.setPane(mouseX - newPanX, mouseY - newPanY);

            // Redraw image
            addImage({ redrawSelectedMarkup });
          }, THROTTLE_DELAY);

          throttleZoom();
        });

        canvas.addEventListener("mouseup", (e) => {
          e.preventDefault();

          isDragging = false;
          if (!scope.isReadOnlyMode) {
            for (const key in MarkupObjects.getMarkupObjects()) {
              const object = MarkupObjects.getMarkupObject(key);
              object.handleMouseUp();
            }

            if (scope.isDrawingWithPencil) scope.isDrawingWithPencil = false;
          }
        });

        canvas.addEventListener("dblclick", (e) => {
          // e.preventDefault();
          // document.getElementById("fileInput").click();
          const throttleClick = throttle((isReadOnlyMode) => {
            for (const key in MarkupObjects.getMarkupObjects()) {
              const object = MarkupObjects.getMarkupObject(key);
              if (object.type === "ruler" && !isReadOnlyMode) {
                const id = object.handleDoubleClick(e, ctx, addImage);
                if (id) {
                  scope.$emit("showScaleModal");
                  scope.selectedRulerObject = object;
                  break;
                }
              } else if (object.type === "camera") {
                const id = object.handleDoubleClick(e, isReadOnlyMode);
                const objectjson = MarkupJson.getMarkupElement(id);
                if (id) {
                  scope.selectedCameraObject = object;
                  if (get(objectjson, "attachmentId")) {
                    scope.$emit("showAttachmentModal", {
                      objType: scope.objMarkup.objType,
                      objId: scope.objMarkup.objId,
                      id: objectjson.attachmentId,
                    });
                  } else {
                    if (isReadOnlyMode) return;
                    document.getElementById("fileInput").click();
                  }
                  break;
                }
              }
            }
            //addImage({ redrawSelectedMarkup });
          }, THROTTLE_DELAY);

          if (scope.tool === null) {
            throttleClick(scope.isReadOnlyMode);
          }
        });
      }

      function addHyperlinkListeners(canvas) {
        scope.addedHyperlinkEventListners = true;
        canvas.addEventListener("dblclick", (e) => {
          const throttleClick = throttle(() => {
            for (const key in HyperlinkObjects.getHyperlinkObjects()) {
              const object = HyperlinkObjects.getHyperlinkObject(key);
              object.handleDoubleClick(e, (id) => {
                const sheetData = find(
                  scope.hyperlinkData,
                  (data) => data.uniqueId === id
                );
                if (sheetData) {
                  scope.onClickHyperlink(sheetData);
                }
              });
            }
          }, THROTTLE_DELAY);
          throttleClick();
        });
      }
    },

    async caliberateRuler(rulerMeasurements) {
      const measurementScale = {
        ...rulerMeasurements,
        canvasPx: this.selectedRulerObject.getSheetPixelDistance(),
      };
      await MarkupCanvas.setMeasurementScale(measurementScale);
      this.drawCanvasWithSheet({
        redrawSelectedMarkup: true,
      });
      await this.addRulerScale({
        plmId: this.getMarkupId,
        rulerScale: measurementScale,
      });
      await this.updateSheetMeta({
        plf_id: this.sheetId,
        plf_sheet_scale_factor: this.getCaliberatedScale(),
      });
    },

    checkifClickedonCanvasImage(event) {
      const canvasRect = this.$refs.canvas.getBoundingClientRect();
      const mouseX = event.clientX - canvasRect.left;
      const mouseY = event.clientY - canvasRect.top;

      // Check if the mouse click is inside the image
      const imageX = PaneZoom.getPaneX();
      const imageY = PaneZoom.getPaneY();
      const imageWidth = this.imageMeta.width * PaneZoom.getZoomFactor();
      const imageHeight = this.imageMeta.height * PaneZoom.getZoomFactor();

      if (
        mouseX >= imageX &&
        mouseX <= imageX + imageWidth &&
        mouseY >= imageY &&
        mouseY <= imageY + imageHeight
      ) {
        // console.log(
        //   "Mouse click is inside the image",
        //   mouseX,
        //   mouseY,
        //   imageX,
        //   imageY,
        //   imageWidth,
        //   imageHeight
        // );
        return true;
        // Perform actions for click inside the image
      } else {
        // console.log(
        //   "Mouse click is outside the image",
        //   mouseX,
        //   mouseY,
        //   imageX,
        //   imageY,
        //   imageWidth,
        //   imageHeight
        // );
        return false;
        // Perform actions for click outside the image
      }
    },

    getCaliberatedScale() {
      const actualScale =
        MarkupCanvas.getActualToCanvasMeasurementRatio() * PaneZoom.scale;
      return actualScale.toFixed(5);
    },

    optimizeCanvasImage(canvas, ctx) {
      // Get the DPR and size of the canvas
      const dpr = window.devicePixelRatio;
      const rect = canvas.getBoundingClientRect();

      // Set the "actual" size of the canvas
      canvas.height = rect.height * dpr;
      canvas.width = rect.width * dpr;

      // Scale the context to ensure correct drawing operations
      ctx.scale(dpr, dpr);

      // Set the "drawn" size of the canvas
      canvas.style.width = `${rect.width}px`;
      canvas.style.height = `${rect.height}px`;
    },

    setCaliberationScale(sheetId) {
      const scaleFactor = get(
        this.sheetMetaById(sheetId),
        "plf_sheet_scale_factor",
        0
      );
      // console.log("scaleFactor-fck", scaleFactor);
      MarkupCanvas.setMeasurementScale(
        MarkupCanvas.getCanvasToActualMeasurementRatio(
          scaleFactor,
          PaneZoom.scale
        )
      );
    },

    drawHyperlinkReferences() {
      forEach(this.hyperlinkData, (sheetData) => {
        const boundingBox = get(sheetData, "bounding_coordinates", []);
        const points = boundingBox;
        this.setHyperLink(points, get(sheetData, "uniqueId"));
      });
    },
    setHyperLink(points, id) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const hyperlink = new Hyperlink(points, id, "#FF0000");

      HyperlinkObjects.addHyperlinkObject(id, hyperlink);
      console.log("hyperlink", HyperlinkObjects.getHyperlinkObjects());

      hyperlink.draw(ctx);
    },
    redrawHyperlinkReferences() {
      const hyperlinks = HyperlinkObjects.getHyperlinkObjects();
      for (const key in hyperlinks) {
        const hyperlink = hyperlinks[key];
        const ctx = this.$refs.canvas.getContext("2d");
        hyperlink.redraw(hyperlink.rawPoints, ctx);
      }
    },

    setRectangle(x, y) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const id = MarkupJson.addMarkupElement({});

      const rectangle = new Rectangle([{ x, y }], id);

      this.addMarkupObject({
        plmId: this.getMarkupId,
        elementId: id,
        markupJson: {},
        markupObject: rectangle,
      });

      rectangle.draw(ctx);

      MarkupObjects.addMarkupObject(id, rectangle, true);
    },
    setPolygon(x, y) {
      console.log(x, y, "polygon");
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const id = MarkupJson.addMarkupElement({});

      const polygon = new Polygon([{ x, y }], id);
      this.addMarkupObject({
        plmId: this.getMarkupId,
        elementId: id,
        markupJson: {},
        markupObject: polygon,
      });

      polygon.draw(ctx);

      MarkupObjects.addMarkupObject(id, polygon, true);

      this.tempPolygonId = id;
    },
    updatePolygon(newPoint) {
      console.log(newPoint, "newPoint");
      const id = this.tempPolygonId;

      // get polygon object
      const polygon = MarkupObjects.getMarkupObject(id);
      const { polygonClosed } = polygon.updatePolygonVertex(newPoint);

      if (polygonClosed) {
        this.tempPolygonId = null;
        this.tool = null;
      }

      this.drawAllMarkups({ redrawSelectedMarkup: true });
    },
    setEllipse(x, y) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const id = MarkupJson.addMarkupElement({});

      const ellipse = new Ellipse([{ x, y }], id);
      this.addMarkupObject({
        plmId: this.getMarkupId,
        elementId: id,
        markupJson: {},
        markupObject: ellipse,
      });
      ellipse.draw(ctx);

      MarkupObjects.addMarkupObject(id, ellipse, true);
    },
    setTriangle(x, y) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const id = MarkupJson.addMarkupElement({});

      const triangle = new Triangle([{ x, y }], id);
      this.addMarkupObject({
        plmId: this.getMarkupId,
        elementId: id,
        markupJson: {},
        markupObject: triangle,
      });

      triangle.draw(ctx);

      MarkupObjects.addMarkupObject(id, triangle, true);
    },
    setLine(x, y) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const id = MarkupJson.addMarkupElement({});

      const line = new Line([{ x, y }], id);
      this.addMarkupObject({
        plmId: this.getMarkupId,
        elementId: id,
        markupJson: {},
        markupObject: line,
      });

      line.draw(ctx);

      MarkupObjects.addMarkupObject(id, line, true);
    },
    setPencil(x, y) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const id = MarkupJson.addMarkupElement({});

      const pencil = new Pencil([{ x, y }], id, true);
      this.addMarkupObject({
        plmId: this.getMarkupId,
        elementId: id,
        markupJson: {},
        markupObject: pencil,
      });

      pencil.draw(ctx);

      MarkupObjects.addMarkupObject(id, pencil, true);
    },
    setHighlighter(x, y) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const id = MarkupJson.addMarkupElement({});

      const highlighter = new Highlighter([{ x, y }], id, true);
      this.addMarkupObject({
        plmId: this.getMarkupId,
        elementId: id,
        markupJson: {},
        markupObject: highlighter,
      });

      highlighter.draw(ctx);

      MarkupObjects.addMarkupObject(id, highlighter, true);
    },
    setCamera(x, y) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const id = MarkupJson.addMarkupElement({});

      const camera = new Camera([{ x, y }], id);
      this.addMarkupObject({
        plmId: this.getMarkupId,
        elementId: id,
        markupJson: {},
        markupObject: camera,
      });

      camera.draw(ctx);

      MarkupObjects.addMarkupObject(id, camera, true);
    },
    setLocation(x, y) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const id = MarkupJson.addMarkupElement({});

      const location = new Location([{ x, y }], id);
      this.addMarkupObject({
        plmId: this.getMarkupId,
        elementId: id,
        markupJson: {},
        markupObject: location,
      });

      location.draw(ctx);

      MarkupObjects.addMarkupObject(id, location, true);
    },
    setArrow(x, y) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const id = MarkupJson.addMarkupElement({});

      const arrow = new Arrow([{ x, y }], id);
      this.addMarkupObject({
        plmId: this.getMarkupId,
        elementId: id,
        markupJson: {},
        markupObject: arrow,
      });
      arrow.draw(ctx);

      MarkupObjects.addMarkupObject(id, arrow, true);
    },
    setRuler(x, y) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const id = MarkupJson.addMarkupElement({});

      const ruler = new Ruler([{ x, y }], id);

      this.addMarkupObject({
        plmId: this.getMarkupId,
        elementId: id,
        markupJson: {},
        markupObject: ruler,
      });
      ruler.draw(ctx);
      MarkupObjects.addMarkupObject(id, ruler, true);
    },
    setCloud(x, y) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const id = MarkupJson.addMarkupElement({});

      const cloud = new Cloud([{ x, y }], id);
      this.addMarkupObject({
        plmId: this.getMarkupId,
        elementId: id,
        markupJson: {},
        markupObject: cloud,
      });

      cloud.draw(ctx);

      MarkupObjects.addMarkupObject(id, cloud, true);
    },
    setText(x, y, textLabel) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const id = MarkupJson.addMarkupElement({});

      const text = new Text([{ x, y }], id, textLabel);
      this.addMarkupObject({
        plmId: this.getMarkupId,
        elementId: id,
        markupJson: {},
        markupObject: text,
      });

      text.draw(ctx);

      MarkupObjects.addMarkupObject(id, text, true);
    },
    drawAllMarkups({ redrawSelectedMarkup }) {
      const canvas = this.$refs.canvas;
      const ctx = canvas.getContext("2d");
      const markups = MarkupJson.getMarkupElements();

      for (let key in markups) {
        var object = MarkupObjects.getMarkupObject(key);
        if (get(object, "show", true))
          if (object && (!object.isSelected || redrawSelectedMarkup))
            object.redraw(markups[key].points, ctx);
      }
    },
    setTool(tool) {
      this.tool = tool;
    },
    renderCachedImage(ctx, canvas, image, { redrawSelectedMarkup }) {
      // Clear the canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      if (!this.imageLoaded) {
        if (image.width && image.height) {
          PaneZoom.setPaneX(canvas.offsetWidth / 2 - image.width / 2);
          this.optimizeCanvasImage(canvas, ctx);
          this.imageLoaded = true;
        }
      }

      // Draw the cached image
      ctx.drawImage(
        image,
        PaneZoom.getPaneX(),
        PaneZoom.getPaneY(),
        image.width * PaneZoom.getZoomFactor(),
        image.height * PaneZoom.getZoomFactor()
      );

      // Draw all markups
      if (this.planSheetCaliberated)
        this.drawAllMarkups({ redrawSelectedMarkup });
      this.redrawHyperlinkReferences();

      this.isPlansLoading = false;
    },
  },
};
</script>

<style scoped lang="scss">
.canvas-container {
  width: 100%;
  height: calc(100% - 40px);
}
.pulse-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
