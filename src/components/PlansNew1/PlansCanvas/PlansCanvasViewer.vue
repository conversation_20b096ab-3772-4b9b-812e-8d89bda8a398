<template>
  <div>
    <PulseLoader
      :loading="isPlansLoading"
      color="#f57947"
      class="pulse-loader"
    ></PulseLoader>
    <canvas
      v-if="sheetUrl"
      id="canvas"
      ref="canvas"
      class="canvas-element"
      screen-reader-only
    ></canvas>
  </div>
</template>

<script>
import PaneZoom from "../Classes/PaneZoom";
import MarkupCanvas from "../Classes/MarkupCanvas";
import MarkupJson from "../Classes/MarkupJson";

import debounce from "lodash/debounce";

import MarkupObjects from "../Classes/MarkupObjects";
import { throttle } from "../util";

import { mapActions } from "vuex";
import PulseLoader from "vue-spinner/src/PulseLoader";

export default {
  name: "PlansCanvasNew",
  components: {
    PulseLoader,
  },
  props: {
    sheetUrl: {
      type: String,
      // default: require("/assets/overlay_result.png"),
      default: "",
    },
    prjId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      addedEventListners: false,
      //sheetUrl:
      //  "https://plans-linarc-qa.s3.amazonaws.com/Sqr2QiWJNrQbiTFW/QbAhm8BYs46SSmdS/nYJXGiDGk4wrCYKn/636ed54f-b998-48b4-b7bb-30a65427abea.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAYBJJYMOLDEJ6WC3S%2F20230317%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20230317T055959Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=63b83ee4a0f4758ae8ebfd7a48c5c26e2feb5f7a12f66c7693a8b00cc799a1f2",
      tools: {},
      tool: null,
      savedMarkups: {},
      isDrawingWithPencil: false,
      isPlansLoading: true,
      imageLoaded: false,
      selectedRulerObject: null,
      selectedCameraObject: null,
      imageMeta: null,
    };
  },
  computed: {
    isReadOnlyMode() {
      return MarkupCanvas.getReadOnly();
    },

    getPlfId() {
      return this.sheetId;
    },
  },
  async mounted() {
    //reset zoom , pane , markupcanvas objects
    this.resetSheet();

    if (this.sheetUrl) {
      // const parentcontainer = this.$refs["canvas-parent-container"];
      const canvas = document.getElementById("canvas");
      console.log("canvas", canvas);

      // canvas.width = parentcontainer.offsetWidth;
      // canvas.height = parentcontainer.offsetHeight;
      await this.drawCanvasWithSheet({
        redrawSelectedMarkup: true,
      });
      //set ReadOnly Mode
      MarkupCanvas.setReadOnly(true);
    }
  },
  methods: {
    ...mapActions({
      resetDraftMarkup: "MarkupCanvas/resetDraftMarkup",
    }),
    toolSelected(tool) {
      this.tool = tool;
    },

    resetSheet() {
      this.resetDraftMarkup();
      PaneZoom.resetPaneZoom();
      MarkupCanvas.resetMarkupCanvas();
      MarkupObjects.resetMarkupObjects();
      MarkupJson.resetMarkupJson();
    },

    async drawCanvasWithSheet({ redrawSelectedMarkup }) {
      const canvas = this.$refs.canvas;
      if (!canvas.getContext("2d")) return;
      const ctx = canvas.getContext("2d");
      const scope = this;

      function addImage({ redrawSelectedMarkup }) {
        const image = new Image();
        image.crossOrigin = "anonymous";
        image.src = scope.sheetUrl;

        // determine the scaling factor
        const widthScale = canvas.offsetWidth / image.width;
        const heightScale = canvas.offsetHeight / image.height;
        const scale = Math.min(widthScale, heightScale);
        if (
          scale !== Infinity &&
          scale !== PaneZoom.scale &&
          scale !== 0.12345
        ) {
          PaneZoom.setScale({
            scale,
            scaleX: widthScale,
            scaleY: heightScale,
          });
        }

        // scale the image
        image.width *= scale;
        image.height *= scale;

        if (image.width && image.height) {
          scope.imageMeta = {
            width: image.width,
            height: image.height,
          };
        }

        // if (PaneZoom.getPaneInitialized() === false) {
        //   PaneZoom.setPane(
        //     (canvas.width / 2 - image.width / 2),
        //     canvas.height / 2 - image.height / 2
        //   );
        //   PaneZoom.setPaneInitialized(true);
        // }

        const setPositionForImage = (canvas, image) => {
          if (image.width && image.height) {
            PaneZoom.setPaneX(canvas.offsetWidth / 2 - image.width / 2);
            scope.optimizeCanvasImage(canvas, ctx);
            scope.imageLoaded = true;
          }
        };

        image.onload = () => {
          // Clear the canvas

          ctx.clearRect(0, 0, canvas.width, canvas.height);

          if (!scope.imageLoaded) {
            setPositionForImage(canvas, image);
          }
          // Draw the image
          ctx.drawImage(
            image,
            PaneZoom.getPaneX(),
            PaneZoom.getPaneY(),
            image.width * PaneZoom.getZoomFactor(),
            image.height * PaneZoom.getZoomFactor()
          );

          // Add drag listeners

          scope.isPlansLoading = false;
        };

        image.onerror = () => {
          console.log("error");
        };

        if (!image.complete) {
          const throttleAddImage = debounce(() => {
            window.requestAnimationFrame(() => {
              addImage({ redrawSelectedMarkup });
            });
          }, 500);
          throttleAddImage();
          scope.isPlansLoading = true;
        } else {
          scope.isPlansLoading = false;
        }
      }

      await addImage({ redrawSelectedMarkup });

      if (!scope.addedEventListners) addDragListeners(canvas, ctx);

      function addDragListeners(canvas) {
        scope.addedEventListners = true;
        let isDragging = false;
        let lastX = 0;
        let lastY = 0;

        canvas.addEventListener("mousedown", (e) => {
          e.preventDefault();
          isDragging = true;
          lastX = e.clientX;
          lastY = e.clientY;
          //test event delegation
        });

        canvas.addEventListener("mousemove", (e) => {
          const throttlePaneZoom = throttle(() => {
            const deltaX = e.clientX - lastX;
            const deltaY = e.clientY - lastY;
            const transformedDeltaX = deltaX / PaneZoom.getZoomFactor();
            const transformedDeltaY = deltaY / PaneZoom.getZoomFactor();

            addImage({ redrawSelectedMarkup });
            PaneZoom.setPaneX(PaneZoom.getPaneX() + transformedDeltaX);
            PaneZoom.setPaneY(PaneZoom.getPaneY() + transformedDeltaY);
            lastX = e.clientX;
            lastY = e.clientY;
          }, 100);

          if (
            isDragging &&
            !MarkupCanvas.isMarkupsSelected() &&
            !scope.isDrawingWithPencil
          ) {
            throttlePaneZoom();
          }
        });

        canvas.addEventListener("wheel", (event) => {
          event.preventDefault();

          const throttleZoom = throttle(() => {
            // Calculate new zoom level
            var newZoomLevel = PaneZoom.getZoomFactor() + event.deltaY * -0.001;

            //Restrict zoom level to between 0.1 and 10
            if (newZoomLevel < 0.5) {
              newZoomLevel = 0.5;
            } else if (newZoomLevel > 10) {
              newZoomLevel = 10;
            }

            // Calculate new pan values
            var rect = canvas.getBoundingClientRect();
            var mouseX = event.clientX - rect.left;
            var mouseY = event.clientY - rect.top;
            var newPanX =
              ((mouseX - PaneZoom.getPaneX()) / PaneZoom.getZoomFactor()) *
              newZoomLevel;
            var newPanY =
              ((mouseY - PaneZoom.getPaneY()) / PaneZoom.getZoomFactor()) *
              newZoomLevel;

            PaneZoom.setZoomFactor(newZoomLevel);
            PaneZoom.setPane(mouseX - newPanX, mouseY - newPanY);

            // Redraw image
            addImage({ redrawSelectedMarkup });
          }, 100);

          throttleZoom();
        });

        canvas.addEventListener("mouseup", (e) => {
          e.preventDefault();

          isDragging = false;
        });
      }
    },

    optimizeCanvasImage(canvas, ctx) {
      // Get the DPR and size of the canvas
      const dpr = window.devicePixelRatio;
      const rect = canvas.getBoundingClientRect();

      // Set the "actual" size of the canvas
      canvas.height = rect.height * dpr;
      canvas.width = rect.width * dpr;

      // Scale the context to ensure correct drawing operations
      ctx.scale(dpr, dpr);

      // Set the "drawn" size of the canvas
      canvas.style.width = `${rect.width}px`;
      canvas.style.height = `${rect.height}px`;
    },
  },
};
</script>

<style scoped lang="scss">
.canvas-element {
  width: 100%;
  height: 75vh;
}
.pulse-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
