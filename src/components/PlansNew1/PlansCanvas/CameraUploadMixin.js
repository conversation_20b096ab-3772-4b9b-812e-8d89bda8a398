import { Axios, urls } from "/src/utils/Axios";
import forEach from "lodash/forEach";
import keys from "lodash/keys";
import Markup<PERSON>son from "../Classes/MarkupJson";
const CameraUploadMixin = {
  methods: {
    async handleFileChange(event, objMarkup, elementId) {
      var input = event.target;
      var file = input.files[0];

      const attachment = {
        file_name: file.name,
        model_name: objMarkup.objType,
        object_id: objMarkup.objId,
        file_type: "image",
      };
      const response = await Axios.post(urls.preSignedPost(), attachment);

      const attachmentId = response.data.id;

      const markupJson = {
        ...MarkupJson.getMarkupElement(elementId),
        attachmentId: attachmentId,
      };

      MarkupJson.updateMarkupElement(markupJson);

      const formData = new FormData();
      forEach(keys(response.data.fields), (key) => {
        formData.set(key, response.data.fields[key]);
      });

      formData.append("file", file);

      await this.$axios.post(response.data.url, formData);
    },
  },
};

export default CameraUploadMixin;
