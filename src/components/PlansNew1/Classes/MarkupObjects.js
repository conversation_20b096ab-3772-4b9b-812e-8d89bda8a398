// Description: This class is used to store all the initiated markup objects in the plan.
import get from "lodash/get";

class MarkupObjects {
  constructor() {
    this.resetMarkupObjects();
    this.store = null;
  }

  setStore(store) {
    this.store = store;
  }

  resetMarkupObjects() {
    this.markupObjects = {};
  }

  addMarkupObject(id, markupObject, show) {
    // this.markupObjects[id] = {
    //   ...this.markupObjects[id],
    //   ...markupObject,
    // };
    this.markupObjects[id] = markupObject;
    this.markupObjects[id].show = show;
    this.store.dispatch("MarkupCanvas/updateMarkupObject", {
      elementId: id,
      markupObject,
    });
  }

  showMarkupObject(id, show) {
    this.markupObjects[id].show = show;
    this.store.dispatch("MarkupCanvas/updateMarkupObject", {
      elementId: id,
      markupObject: this.markupObjects[id],
    });
  }

  getMarkupObject(id) {
    return this.markupObjects[id];
  }

  getMarkupObjects() {
    //return only if the object.show is true
    let markupObjects = {};
    for (let key in this.markupObjects) {
      if (get(this.markupObjects[key], "show", true)) {
        markupObjects[key] = this.markupObjects[key];
      }
    }
    return markupObjects;
  }

  removeMarkupObject(id) {
    if (this.markupObjects[id]) delete this.markupObjects[id];
  }

  removeAllMarkupObjects() {
    this.markupObjects = {};
  }

  setMarkupObjectMeasurements(id, measurements) {
    if (this.markupObjects[id]) {
      this.markupObjects[id].measurements = measurements;
    }
  }

  getMarkupObjectMeasurements(id) {
    if (this.markupObjects[id]) {
      if (this.markupObjects[id].measurements) {
        return this.markupObjects[id].measurements;
      } else {
        return null;
      }
    }
  }
}

export default new MarkupObjects();
