class PaneZoom {
  constructor(zoomFactor, paneX, paneY) {
    this.zoomFactor = zoomFactor ? zoomFactor : 1;
    this.paneX = paneX ? paneX : 0;
    this.paneY = paneY ? paneY : 0;
    this.scaleX = 1;
    this.scaleY = 1;
    this.scale = 0.12345;
    this.paneInitialized = false;
  }

  resetPaneZoom() {
    this.zoomFactor = 1;
    this.paneX = 0;
    this.paneY = 0;
    this.scaleX = 1;
    this.scaleY = 1;
    this.scale = 0.12345
  }

  setScale({ scaleX, scaleY, scale }) {
    scaleX ? (this.scaleX = scaleX) : null;
    scaleY ? (this.scaleY = scaleY) : null;
    scale ? (this.scale = scale) : null;
  }

  getPaneInitialized() {
    return this.paneInitialized;
  }

  setPaneInitialized(value) {
    this.paneInitialized = value;
  }

  setZoomFactor(zoomFactor) {
    this.zoomFactor = zoomFactor;
  }

  setPaneX(paneX) {
    this.paneX = paneX;
  }

  setPaneY(paneY) {
    this.paneY = paneY;
  }

  setPane(paneX, paneY) {
    this.paneX = paneX;
    this.paneY = paneY;
  }

  getZoomFactor() {
    return this.zoomFactor;
  }

  getPaneX() {
    return this.paneX;
  }

  getPaneY() {
    return this.paneY;
  }

  getPaneZoom() {
    return {
      zoomFactor: this.zoomFactor,
      paneX: this.paneX,
      paneY: this.paneY,
    };
  }

  // New cordinates
  getCalculatedCordinates(x, y) {
    return {
      //consider both pan and zoom
      x: x * this.zoomFactor + this.paneX,
      y: y * this.zoomFactor + this.paneY,
    };
  }

  //Actual Cordinates for Markup Json
  getOriginalCordinates(x, y) {
    return {
      x: (x - this.paneX) / this.zoomFactor,
      y: (y - this.paneY) / this.zoomFactor,
    };
  }

  getCalculatedListCordinates(list) {
    const newList = [];
    for (let i = 0; i < list.length; i++) {
      newList.push(this.getCalculatedCordinates(list[i].x, list[i].y));
    }
    return newList;
  }

  getOriginalListCordinates(list) {
    const newList = [];
    for (let i = 0; i < list.length; i++) {
      newList.push(this.getOriginalCordinates(list[i].x, list[i].y));
    }
    return newList;
  }

  // Actual Image vs Canvas Fit according to Parent div based on this.scale
  getCanvasCordinatesFromSheet(x, y) {
    return {
      x: x * this.scale,
      y: y * this.scale,
    };
  }

  getStrokeWidthFromSheet(strokeWidth) {
    return strokeWidth * this.scale;
  }

  getSheetCorrdinatesFromCanvas(x, y) {
    return {
      x: x / this.scale,
      y: y / this.scale,
    };
  }

  getCanvasListCordinatesFromSheet(list) {
    const newList = [];
    for (let i = 0; i < list.length; i++) {
      newList.push(this.getCanvasCordinatesFromSheet(list[i].x, list[i].y));
    }
    return newList;
  }

  getSheetListCordinatesFromCanvas(list) {
    const newList = [];
    for (let i = 0; i < list.length; i++) {
      newList.push(this.getSheetCorrdinatesFromCanvas(list[i].x, list[i].y));
    }
    return newList;
  }

  getStrokeWidthFromCanvas(strokeWidth) {
    return strokeWidth / this.scale;
  }
}

export default new PaneZoom();
