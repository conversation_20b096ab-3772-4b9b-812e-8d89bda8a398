class HyperlinkObjects {
  constructor() {
    this.resetHyperlinkObjects();
  }

  resetHyperlinkObjects() {
    this.hyperlinkObjects = {};
  }

  addHyperlinkObject(id, hyperlinkObject) {
    this.hyperlinkObjects[id] = hyperlinkObject;
  }

  getHyperlinkObject(id) {
    return this.hyperlinkObjects[id];
  }

  getHyperlinkObjects() {
    //return only if the object.show is true
    return this.hyperlinkObjects;
  }

  removeHyperlinkObject(id) {
    if (this.hyperlinkObjects[id]) delete this.hyperlinkObjects[id];
  }

  removeAllHyperlinkObjects() {
    this.hyperlinkObjects = {};
  }
}

export default new HyperlinkObjects();
