import { generateUUID } from "../util";

class MarkupJson {
  constructor() {
    this.resetMarkupJson();
    this.store = null;
  }

  resetMarkupJson() {
    this.markupJson = {};
    this.selectedMarkUpElements = {};
  }

  setStore(store) {
    this.store = store;
  }

  generateId() {
    return generateUUID();
  }

  addMarkupElement(markupElement) {
    const generateId = this.generateId();
    this.markupJson[generateId] = markupElement;
    return generateId;
  }

  getMarkupElement(id) {
    return this.markupJson[id];
  }

  getMarkupElements() {
    return this.markupJson;
  }

  setSavedMarkupElements(markupJson) {
    this.markupJson = markupJson;
    this.store.dispatch("MarkupCanvas/setMarkupJson", markupJson);
  }

  updateMarkupElement(markupElement) {
    this.markupJson[markupElement.id] = {
      ...this.markupJson[markupElement.id],
      ...markupElement,
    };

    this.store.dispatch("MarkupCanvas/updateMarkupJson", {
      elementId: markupElement.id,
      markupJson: markupElement,
    });
  }

  deleteMarkupElement(id) {
    delete this.markupJson[id];
  }

  deleteAllMarkupElements() {
    this.markupJson = {};
  }
}

export default new MarkupJson();
