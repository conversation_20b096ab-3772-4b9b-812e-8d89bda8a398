import { colorWithOpacity } from "../util";
import indexOf from "lodash/indexOf";
const tools = {
  SELECT: "select",
  HAND: "hand",
  RECTANGLE: "rectangle",
  CIRCLE: "circle",
  LINE: "line",
  ARROW: "arrow",
  TEXT: "text",
  PENCIL: "pencil",
  HIGHLIGHTER: "highlighter",
  RULER: "ruler",
  CAMERA: "camera",
  LOCATION: "location",
  CLOUD: "cloud",
};
class MarkupCanvas {
  constructor() {
    this.cameraImageSrc = null;
    this.locationImageSrc = null;
    this.resetMarkupCanvas();
  }

  resetMarkupCanvas() {
    this.markupCanvas = {};
    this.selectedMarkupElements = [];
    this.toolSelected = null;
    this.gestureSelected = tools.HAND;
    this.fill = "#FFFFFF";
    this.stroke = "#2B76E7";
    this.mesurementFontSize = 10;
    this.mesurementFontColor = "black";
    this.strokeWidth = 2;
    this.opacity = 0;
    this.readOnly = false;
    this.measurementScale = {
      feet: 3,
      inches: 0,
      canvasPx: 1,
    };
    this.store = null;
  }

  //Selection Markups
  selectMarkupElement(id) {
    //this.selectedMarkupElements.push(id);
    if (indexOf(this.selectedMarkupElements, id) === -1) {
      this.selectedMarkupElements.push(id);
    }
  }

  setStore(store) {
    this.store = store;
  }

  setCameraImageSrc(src) {
    this.cameraImageSrc = src;
  }

  setLocationImageSrc(src) {
    this.locationImageSrc = src;
  }

  setMeasurementScale(measurementScale) {
    this.measurementScale = measurementScale;
  }

  getMeasurementScale() {
    return this.measurementScale;
  }

  getActualToCanvasMeasurementRatio() {
    const feet = parseFloat(this.measurementScale.feet);
    const inches = parseFloat(this.measurementScale.inches);
    const canvasPx = parseFloat(this.measurementScale.canvasPx);

    const value = ((feet * 12 + inches) * 96) / canvasPx;
    //round to 2 decimal places
    return Math.round(value * 100) / 100;
  }

  getCanvasToActualMeasurementRatio(scale_factor, sheetScale) {
    //convert pixel to inches
    const value = scale_factor / 96;

    const feet = Math.floor(value / 12);
    const inches = value % 12;
    return {
      feet,
      inches,
      canvasPx: sheetScale,
    };
  }

  getSelectedMarkupElements() {
    return this.selectedMarkupElements;
  }

  unSelectAllMarkupElements() {
    this.selectedMarkupElements = [];
  }

  unSelectMarkupElement(id) {
    const index = this.selectedMarkupElements.indexOf(id);
    if (index > -1) {
      this.selectedMarkupElements.splice(index, 1);
    }
  }

  isMarkupsSelected() {
    return this.selectedMarkupElements.length > 0;
  }

  isMarkupElementSelected(id) {
    return this.selectedMarkupElements.indexOf(id) > -1;
  }

  //Tool Selection
  selectTool(tool) {
    this.toolSelected = tool;
  }

  getSelectedTool() {
    return this.toolSelected;
  }

  getAllTools() {
    return tools;
  }

  //Gesture Selection
  selectGesture(gesture) {
    this.gestureSelected = gesture;
  }

  getSelectedGesture() {
    return this.gestureSelected;
  }

  //Fill
  setFill(fill) {
    this.fill = fill;
  }

  getFill({ nonWhite = false } = {}) {
    // return colorWithOpacity(this.fill, this.opacity);
    if (nonWhite) {
      if (this.fill === "#FFFFFF" || this.opacity === 0) {
        return colorWithOpacity("#2B76E7", 30);
      } else return colorWithOpacity(this.fill, this.opacity);
    }
    return colorWithOpacity(this.fill, this.opacity);
  }

  getCustomColor(color, opacity) {
    return colorWithOpacity(color, opacity);
  }

  //Stroke
  setStroke(stroke) {
    this.stroke = stroke;
  }

  getStroke() {
    return this.stroke;
  }

  //Stroke Width
  setStrokeWidth(strokeWidth) {
    this.strokeWidth = strokeWidth;
  }

  getStrokeWidth() {
    return this.strokeWidth;
  }

  //Opacity

  setOpacity(opacity) {
    this.opacity = opacity;
  }

  getOpacity() {
    return this.opacity;
  }

  //ReadOnly
  setReadOnly(readOnly) {
    this.readOnly = readOnly;
  }

  getReadOnly() {
    return this.readOnly;
  }
}

export default new MarkupCanvas();
