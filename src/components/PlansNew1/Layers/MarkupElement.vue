<template>
  <div>
    <div class="element-list-item">
      <div class="element-check-box">
        <Icon size="tiny" :name="iconName[markupType]" color="secondary" />
      </div>
      <div class="element-name-label">
        <Input v-if="showDeleteMarkupElement" v-model="markup_name"  placeholder="Enter Markup Name" size="medium" :isInline="true" responsive @onInput="onChangeName" />
        <span v-else>{{ markupName }}</span>
      </div>
      <div class="element-actions">
        <div
          v-if="elementChecked"
          class="element-show-hide"
          @click="onChangeMarkupElementValue(elementId, false)"
        >
          <Icon size="tiny" name="eye" color="secondary" />
        </div>
        <div
          v-else
          class="element-show-hide"
          @click="onChangeMarkupElementValue(elementId, true)"
        >
          <Icon size="tiny" name="eyeOff2" color="secondary" />
        </div>
        <div
          v-if="showDeleteMarkupElement"
          class="element-delete"
          @click="onDeleteMarkupElementValue(elementId, false)"
        >
          <Icon size="tiny" name="trash2" color="secondary" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { PLAN_MARKUP_MODES } from "@/constants";

export default {
  name: "MarkupElement",
  props: {
    markupName: {
      type: String,
      default: "",
    },
    markupType: {
      type: String,
      default: "",
    },
    elementId: {
      type: String,
      default: "",
    },
    elementChecked: {
      type: Boolean,
      default: false,
    },
    markupMeasurements:{
      type: Object,
      default: undefined,
    }
  },
  
  data() {
    return {
      iconName: {
        rect: "square",
        camera: "imageIcon",
        locationpin: "pin",
        ruler: "measure",
        pencil: "editNew",
        highlighter: "highlighter",
        text: "textIcon",
        arrow: "diagonalArrowRightUp",
        cloud: "cloudMarkup",
        line: "lineOutline",
        triangle: "triangle",
        ellipse: "radioButtonOff",
        polygon:'newPolygon'
      },
      markup_name: this.markupName,
    };
  },
  computed: {
    showDeleteMarkupElement() {
      return (
        this.getCurrentMarkupMode === PLAN_MARKUP_MODES.EDIT ||
        this.getCurrentMarkupMode === PLAN_MARKUP_MODES.DRAFT
      );
    },
    ...mapGetters({
      getCurrentMarkupMode: "MarkupCanvas/getCurrentMarkupMode",
    }),
  },
  // watch: {
  //   markupName: {
  //     immediate: true,
  //     handler(newValue) {
  //       this.markup_name = newValue;
  //     },
  //   },
  // },
  methods: {
    onChangeMarkupElementValue(elementId, value) {
      this.$emit("onChangeElement", {
        elementId,
        value,
      });
    },
    onChangeName(value) {
      this.markup_name = value;
      this.$emit("onNameChange", {
        elementId: this.elementId,
        value,
      });
    },
    onDeleteMarkupElementValue(elementId, value) {
      this.$emit("onDeleteElement", {
        elementId,
        value,
      });
    },
  },
};
</script>

<style scoped lang="scss">
.element-list-item {
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 10px 0px 10px;
  background: #ffffff;
  //margin-left: 40px;
  .element-check-box {
    width: 18px;
    height: 18px;
  }
  .element-name-label {
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    color: black;
    padding-left: 12px;
    text-transform: capitalize;
    margin-right: 10px;
  }
  .element-measurements{
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    color: black;
    padding-left: 2px;
    padding-top: 5px;
    text-transform: capitalize;
  }
  .element-actions {
    margin-left: auto;
    margin-right: 10px;
    display: flex;
    .element-delete {
      padding-left: 10px;
    }
  }
}
</style>
