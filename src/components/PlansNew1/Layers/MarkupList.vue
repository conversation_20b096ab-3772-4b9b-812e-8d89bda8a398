<template>
  <div class="layers-list">
    <div
      v-for="elementId in getMarkupObjectsByPlmId(getCurrentMarkupId)"
      :key="elementId"
    >
      <MarkupElement
        :markupName="getMarkupName(elementId)"
        :markupType="getMarkupType(elementId)"
        :markupMeasurements="getMarkupMeasurements(elementId)"
        :elementChecked="getElementCheckedStatus(elementId)"
        :elementId="elementId"
        @onChangeElement="
          onChangeMarkupElementValue($event.elementId, $event.value)
        "
        @onDeleteElement="
          onDeleteMarkupElementValue($event.elementId, $event.value)
        "
        @onNameChange="onMarkupNameChange($event.elementId, $event.value)"
      ></MarkupElement>
      <TakeOffCard
        v-if="getMarkupTakeOff(elementId)"
        :takeOffData="getMarkupTakeOff(elementId)"
      />
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import get from "lodash/get";
import MarkupObject from "@/components/PlansNew1/Classes/MarkupObjects.js";
import MarkupElement from "./MarkupElement.vue";
import TakeOffCard from "./TakeOffCard.vue";
import { DRAFT_MARKUP } from "@/constants";
import forEach from "lodash/forEach";
import capitalize from "lodash/capitalize";
export default {
  name: "MarkupListItemNew",
  components: {
    MarkupElement,
    TakeOffCard,
  },
  props: {
    canvasRefs: {
      type: Object,
      default: () => {
        return {};
      },
    },
    filterValue: {
      type: String,
      default: "all",
    },
    sheetId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      elementStatusMap: {
        [DRAFT_MARKUP.id]: true,
      },
      elementExpansionMap: {
        [DRAFT_MARKUP.id]: true,
      },
    };
  },
  computed: {
    ...mapGetters({
      getMarkupObjectsByPlmId: "MarkupCanvas/getMarkupObjectsByPlmId",
      getMarkupObjectById: "MarkupCanvas/getMarkupObjectById",
      getMarkupIdsBySheetId: "MarkupCanvas/getMarkupIdsBySheetId",
      getPlanMarkupById: "MarkupCanvas/getPlanMarkupById",
      getCurrentMarkupId: "MarkupCanvas/getCurrentMarkupId",
    }),
    getElementCheckedStatus() {
      return (elementId) => {
        return get(this.elementStatusMap, elementId, true);
      };
    },
    getElementExpansionStatus() {
      return (markupId) => {
        return get(this.elementExpansionMap, markupId, false);
      };
    },
    getMarkupTakeOff() {
      return (elementId) => {
        const markupObject = this.getMarkupObjectById(elementId);
        console.log(markupObject);
        return get(markupObject, "takeoff");
      };
    },
    getMarkupMeasurements() {
      return (elementId) => {
        const markupObject = this.getMarkupObjectById(elementId);
        return get(markupObject, "measurements");
      };
    },
    getMarkupName() {
      return (elementId) => {
        if(this.getMarkupObjectById(elementId).name){
        return this.getMarkupObjectById(elementId).name;
      }
      return capitalize(this.getMarkupObjectById(elementId).type)
      }
    },
    getMarkupType() {
      return (elementId) => {
        const markupObject = this.getMarkupObjectById(elementId);
        return get(markupObject, "type");
      };
    },
  },
  methods: {
    ...mapActions({
      updateMarkupObject: "MarkupCanvas/updateMarkupObject",
      updateMarkupJson: "MarkupCanvas/updateMarkupJson",
      deleteMarkupElement: "MarkupCanvas/deleteMarkupElement",
    }),
    // getMarkupName(markupId) {
    //   //return get(this.markupIds, `${markupId}.plm_name`, "draft");
    //   const planMarkup = this.getPlanMarkupById(markupId);
    //   return get(planMarkup, "plm_name", DRAFT_MARKUP.title);
    // },
    // getMarkupMeasurements(elementId) {
    //   return get(this.getMarkupObjectById(elementId), "measurements");
    // },
  
    onChangeMarkupElementValue(elementId, value) {
      console.log("onChangeMarkupElementValue", elementId, value);
      this.elementStatusMap = {
        ...this.elementStatusMap,
        [elementId]: value,
      };
      MarkupObject.showMarkupObject(elementId, value);
      this.canvasRefs.drawCanvasWithSheet({
        redrawSelectedMarkup: true,
      });
      this.$emit("onDeleteElement", null);
    },
    onDeleteMarkupElementValue(elementId) {
      this.deleteMarkupElement(elementId);
      // this.canvasRefs.updateMarkupJsonElements(elementId);
      // this.canvasRefs.drawCanvasWithSheet({
      //   redrawSelectedMarkup: true,
      // });
      this.$emit("onDeleteElement", elementId);
    },
    onMarkupNameChange(elementId, value) {
      this.updateMarkupJson({
        elementId,
        markupJson: {
          name: value,
        },
      });
    },
    onChangeMarkup(markupId, value) {
      const allElements = this.getMarkupObjectsByPlmId(markupId);
      // this.elementStatusMap = {
      //   ...this.elementStatusMap,
      //   [markupId]: value,
      // };
      forEach(allElements, (elementId) => {
        this.elementStatusMap = {
          ...this.elementStatusMap,
          [elementId]: value,
        };
        MarkupObject.showMarkupObject(elementId, value);
      });
      this.canvasRefs.drawCanvasWithSheet({
        redrawSelectedMarkup: true,
      });
    },
    moreClicked(markupId) {
      const value = this.elementExpansionMap[markupId] ? false : true;
      this.elementExpansionMap = {
        ...this.elementExpansionMap,
        [markupId]: value,
      };
    },
  },
};
</script>

<style scoped lang="scss">
.layers-list {
  height: 440px;
  overflow-y: scroll;
}
</style>
