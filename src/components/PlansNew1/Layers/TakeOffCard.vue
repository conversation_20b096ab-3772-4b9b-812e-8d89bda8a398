<template>
  <div class="takeoff-card">

    <div class="takeoff-items">
      <!-- <div v-if="takeOffData.length" class="takeoff-item">
        <span class="label">Length:</span>
        <span class="value">{{ formatLength(takeOffData.length) }}</span>
      </div>
      <div v-if="takeOffData.width" class="takeoff-item">
        <span class="label">Width:</span>
        <span class="value">{{ formatLength(takeOffData.width) }}</span>
      </div>
      <div v-if="takeOffData.area" class="takeoff-item">
        <span class="label">Area:</span>
        <span class="value">{{ formatArea(takeOffData.area) }}</span>
      </div>
      <div v-if="takeOffData.perimeter" class="takeoff-item">
        <span class="label">Perimeter:</span>
        <span class="value">{{ formatLength(takeOffData.perimeter) }}</span>
      </div> -->
      <Grid v-if="getMarkupMeasurements && getMarkupType === 'TAKEOFF'" :columns="4">
        <Box v-for="key in filteredKeys" :key="key" :columnSize="2" >
          <template #body>
            <div v-if="takeOffData[key]" class="takeoff-item">
              <span :title="mapTakeOffAttribute[key]" class="label text-baseLight">{{ mapTakeOffAttribute[key][0] }}</span>
              <!-- <span class="value">{{ takeOffData[key] }}</span> -->
               <Input :value="fomatData(takeOffData[key],key )" :disabled="true" responsive />
            </div>
          </template>
        </Box>
      </Grid>
    </div>
  </div>
</template>

<script>

import keys from 'lodash/keys';
import filter from 'lodash/filter';
import get from 'lodash/get';
import { mapGetters } from 'vuex';

export default {
  name: 'TakeOffCard',
  props: {
    takeOffData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      mapTakeOffAttribute: {
        length: 'Length',
        width: 'Width',
        area: 'Area',
        perimeter: 'Perimeter',
      }
    }
  },
  computed:{
    ...mapGetters({
      getMarkupObjectById: "MarkupCanvas/getMarkupObjectById",
      getMarkupType: "MarkupCanvas/getMarkupType",
    }),
    filteredKeys(){
      let data = filter(keys(this.takeOffData), (key) => {
        return key !== 'points';
      });
      return data;
    },
    getMarkupMeasurements() {
      return (elementId) => {
        const markupObject = this.getMarkupObjectById(elementId);
        return get(markupObject, "measurements");
      };
    },
  },
  methods: {
    fomatData(value, key) {
      if (key === 'length' || key === 'width' || key === 'perimeter') {
        return this.formatLength(value);
      } else if (key === 'area') {
        return this.formatArea(value);
      }
      return value;
    },
    formatLength(inches) {
      if (!inches && inches !== 0) return '';
      
      const feet = Math.floor(inches / 12);
      const remainingInches = Math.round((inches % 12))
      
      if (feet === 0) {
        return `${remainingInches}″`;
      } else if (remainingInches === 0) {
        return `${feet}′`;
      } else {
        return `${feet}′ ${remainingInches}″`;
      }
    },
    formatArea(squareInches) {
      if (!squareInches && squareInches !== 0) return '';
      
      const squareFeet = squareInches / 144;
      const roundedSquareFeet = Math.round(squareFeet * 100) / 100;
      
      return `${roundedSquareFeet} ft²`;
    }
  }
};
</script>

<style scoped lang="scss">
.takeoff-card {

  border-radius: 4px;
  padding: 0px 12px;

  font-size: 0.9rem;
  
  .takeoff-title {
    font-weight: 600;

  }
  
  .takeoff-items {
    .takeoff-item {
      display: flex;
      align-items: center;
      // border: 1px solid red;
      border-radius: 4px;
      padding: 0px 8px 0px 8px;
      margin: 4px 0;
      overflow: hidden;
      .label {
        // padding: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 2rem;
        border: 1px solid rgb(173, 193, 209);
        border-right: 0px;
        border-radius: 4px 0px 0px 4px;
        width: 50px;
      }
      
      .value {
        font-weight: 500;
      }
    }
  }
}
::v-deep .no-arrows{
  border-radius: 0px 4px 4px 0px;
  padding: 6px;
}
</style>