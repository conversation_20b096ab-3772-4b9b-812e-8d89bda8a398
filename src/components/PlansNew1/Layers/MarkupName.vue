<template>
  <div>
    <div class="element-list-item">
      <div @click="onClickMarkup">
        <Icon size="small" :name="getIconName" color="baseDark" />
      </div>
      <div class="element-check-box">
        <Checkbox
          :value="elementChecked"
          isDashed
          label=""
          size="small"
          @onChange="onChangeMarkupElementValue(markupId, $event)"
        />
      </div>
      <div class="element-name-label">
        {{ markupName }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "MarkupName",
  props: {
    markupName: {
      type: String,
      default: "",
    },
    markupId: {
      type: String,
      default: "",
    },
    elementChecked: {
      type: Boolean,
      default: false,
    },
    expansionStatus: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    getIconName() {
      if (!this.expansionStatus) {
        return "arrowRight";
      } else {
        return "arrowDown";
      }
    },
  },
  methods: {
    onChangeMarkupElementValue(elementId, value) {
      this.$emit("onChangeMarkup", { elementId, value });
    },
    onClickMarkup() {
      this.$emit("onClickMarkup", this.markupId);
    },
  },
};
</script>

<style scoped lang="scss">
.element-list-item {
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px;
  background: #f6faff;
  .element-check-box {
    width: 20px;
    height: 20px;
    padding-left: 5px;
  }
  .element-name-label {
    font-weight: 300;
    font-size: 12px;
    line-height: 18px;
    /* identical to box height, or 150% */
    padding-left: 17px;
    color: #000000;
    text-transform: capitalize;
  }
}
</style>
