<template>
<div class="dropup">
  <!-- <button class="dropbtn" @click="showSheets">
    Sheets
  </button> -->
  <Treeselect 
    v-model="value" 
    :multiple="false" 
    :options="options" 
    @input="changeImage"
    :searchable="false"
    :disableBranchNodes="false" />
</div>
</template>
<script>
 import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import split from 'lodash/split';
import keys from 'lodash/keys';
import forEach from 'lodash/forEach';

export default {
   name: 'sideBar',
   props: {
     all_data: Object
   },
   data() {
     return {
       value: null,
       options: null,
       selected_sheet: null,
       version: null,
       version_obj: null
     };
   },
   components: {
     Treeselect
   },
   methods: {
     changeImage() {
       const sheet = split(this.value, ' ')[0];
       const version = split(this.value, ' ')[1];;
       console.log(sheet, version);
       this.$emit('changeImage', sheet, version);
     }
   },
   mounted() {
     setTimeout(
       () => {
         const options = [];
         const keys1 = keys(this.all_data.objs);
         forEach(keys1,key => {
           let sheet_name = key;
           const title = this.all_data.objs[key].sheet_title;
           if (title !== null) { sheet_name = `${sheet_name} - ${title}`; }
 
           const inner_obj = {
             id: key,
             label: sheet_name,
             children: []
           };
           const versions = this.all_data.objs[key].versions;
           const version_keys = keys(versions);
           forEach(version_keys,version => {
             const version_id = `${key} ${version}`;
             const version_obj = {
               id: version_id,
               label: version
             };
             inner_obj.children.push(version_obj);
           });
           options.push(inner_obj);
         });
         this.options = options;
       }
       , 3000
     );
   }
};
</script>
<style scoped>
</style>
