<template>
  <div>
    <modal name="detail_tag" draggable="#edit_tag_header" height="auto" id="detail_tag">
      <div class="container">
        <div id="edit_tag_header">
          <h3>Tag Details</h3>
        </div>
        <hr />
        <div class="edit_tag_body">
          <div>
            <div class="edit-heading">Tag Type</div>
            <span>{{getTagType}}</span>
          </div>
          <div>
            <div class="edit-heading">
              Details
              </div>
            <span>{{getDetails}}</span>
          </div>

          <!-- <div>Other Details</div> -->
          <div>
            <div class="edit-heading">
            Hyperlink :
            </div>
            <span @click="gotoSheet(getHyperlink)" class="hyperlink">{{getHyperlink}}</span>
          </div>
          <div>
            <div class="edit-heading">
              Generic Tag :
            </div>
             </div> {{getGenericTag}}
        </div>
        <hr />
        <div id="edit_tag_footer" style="float:right;margin-bottom:10px;">
          <button @click="edit_data" class="btn-default">Edit Details</button>
          <button @click="$modal.hide('detail_tag')" class="btn-default">Close</button>
          <!-- <button @click="edit_data" class="btn btn-primary">Edit Details</button>
          <button @click="$modal.hide('detail_tag')" class="btn btn-danger">Close</button> -->
        </div>
      </div>
    </modal>

    <modal name="edit_tag" draggable="#edit_tag_header" height="auto" id="edit_tag_modal" :width="400">
      <div class="container">
        <div id="edit_tag_header">
          <h3>Edit Tag</h3>
        </div>
        <hr />
        <div id="edit_tag_body">
          <form>
            <table class="table-form">
              <tr class="table-rows-heading">
                <td class="table-label-heading">Tag Type</td>
                <td> <input type="text" v-model="tag_type" class="table-text-box"/></td>
              </tr>
              <tr class="table-rows-heading">
                <td>Generic Tag</td>
                <td><select v-model="generic_tag">
              <option selected value="false">False</option>
              <option value="true">True</option>
            </select></td>
              </tr>
              <tr class="table-rows-heading">
                <td>Detail</td>
                <td> <textarea type="text" v-model="detail"></textarea></td>
              </tr>
              <tr class="table-rows-heading">
                <td>Hyperlink</td>
                <td>
                  <select v-model="hyperlink">
                    <option selected value></option>
                    <option v-for="(sheet,data) in all_keys.objs" :key="data">{{data}}</option>
                  </select>
                </td>
              </tr>
            </table>

          </form>
        </div>
        <hr />
        <div id="edit_tag_footer" style="float:right;margin-bottom:10px;">
          <button @click="save_data" class="btn-default">Save</button>
          <button @click="$modal.hide('edit_tag')" class="btn-default">Cancel</button>
        </div>
      </div>
    </modal>
  </div>
</template>

<script>
export default {
  name: 'editTabComponent',
  props: {
    current_sheet: String,
    all_keys: Object,
    tag_id: String,
    generic_tags: Object
  },
  data() {
    return {
      tag_type: '',
      detail: '',
      hyperlink: '',
      generic_tag: ''
    };
  },
  computed: {
    getTagType() {
      try {
        return this.all_keys.objs[this.current_sheet].tags[this.tag_id].datesa.tag_type;
      } catch (error) {
        return '';
      }
    },
    getGenericTag() {
      try {
        return this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.generic_tag;
      } catch (error) {
        return '';
      }
    },
    getDetails() {
      try {
        return this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.detail;
      } catch (error) {
        return '';
      }
    },
    getHyperlink() {
      try {
        return this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.hyperlink;
      } catch (error) {
        return '';
      }
    }
  },
  methods: {
    save_data() {
      console.log(this.event);
      this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.tag_type = this.tag_type;
      this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.generic_tag = this.generic_tag;
      this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.detail = this.detail;
      this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.hyperlink = this.hyperlink;

      if (this.generic_tag === 'true') {
        this.generic_tags[this.tag_id] = this.all_keys.objs[this.current_sheet].tags[this.tag_id];
      }

      this.$modal.hide('edit_tag');
    },
    edit_data() {
      this.tag_type = this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.tag_type;
      this.generic_tag = this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.generic_tag;
      this.detail = this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.detail;
      this.hyperlink = this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.hyperlink;
      this.$modal.show('edit_tag');
    },
    gotoSheet(sheet) {
      this.$modal.hide('detail_tag');
      this.$emit('goto_sheet', sheet);
    }
  }
};
</script>

<style scoped lang="scss">
.table-form {
  td {
    width: 75px;
    height: 35px;
    vertical-align: middle;

  }
}

textarea{
  resize: none;
  border: none;
  background: #EEE;
  color: #666;
  padding: 2%;
}
.table-text-box{
  border: none;
  background: #EEE;
  color: #666;
  padding: 2%;
}
select{
  color: #646464 !important;
  background: #eee;
  border: none;
  font-size: 12px !important;
  font-family: "Lato", sans-serif;
  padding: 3px !important;
}
.table-label-heading{
  width: 74px;
}
.table-rows-heading{
  margin-bottom: 3px;
}
.container {
  padding: 10px;
}
span.hyperlink:hover{
  cursor:pointer;
  background-color: orange
}
.edit_tag_body{
  padding: 5px;
  font-size: 14px;
}
.edit-heading{
  font-size: 15px;
  font-weight: bold;
}
#edit_tag_header{
  font-size: 15px;
  font-weight: bold;
  color: #316BAE;
}
#edit_tag_footer button{
  margin-left:5px;
}
</style>
