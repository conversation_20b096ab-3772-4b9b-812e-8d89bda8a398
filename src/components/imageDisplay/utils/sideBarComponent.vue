<template>
<div class="dropup">
  <button class="dropbtn" @click="showSheets">
    Sheets
  </button>
  <div class="dropup-content" ref="dropup-content">
      <div id="file_ref" :class="section.class" v-for="(section,key,index) in all_data.objs" tabindex="-1" @click="changeImage(key)" href="#">{{key}} | {{section.sheet_title}}</div>
  </div>
</div>
    <!-- <div id = "all_files">
        <div id="file_ref" :class="section.class" v-for="(section,key,index) in all_data.objs" @click="changeImage(key)">{{key}}<br></div>
    </div> -->
</template>
<script>
export default {
  name: 'sideBar',
  props: {
    all_data: Object
  },
  data() {
    return {

    };
  },
  methods: {
    changeImage(key) {
      this.$emit('changeImage', key);
      this.$refs['dropup-content'].style.display = 'None';
    },
    showSheets() {
      this.$refs['dropup-content'].style.display = 'block';
    }
  }
};
</script>
<style scoped>

.selected{
  background-color: bisque
}

.visited{
  background-color: darkgray
}

.dropup {
  position: relative;
  display: inline-block;
}

.dropup-content {
  bottom:100%;
    width:400px;
    height: auto;
    max-height: 600px;
    overflow-x: hidden;
    display: none;
    position: absolute;
    background-color: #f1f1f1;
}

#file_ref{
  cursor: pointer;
  width:100%;
  height:20px;
  font-size: 15px;
  border-bottom: 1px solid black;
  margin-bottom:5px;
  overflow: hidden;
  color: black;
  /* text-decoration: none; */
  display: block;
}

button{
  width:200px;
  font-size: 0.9em;
  margin: 5px 10px;
  border-radius: 5px;
  background-color: rgba(0,0,0,0.4);
  color:seashell;
}
/*
#all_files{
  height:60vh;
  width:100px;
  overflow:auto;
  z-index: 100;
  position:absolute;
} */
</style>