<template>
    <nav id = "action_bar_zoom">
        <button id="zoomIn" @click="zoomIn()">+</button>
        <button id="zoomOut">-</button>

        <button id="goLeft"><</button>
        <button id="goRight">></button>
        <button id="goUp">Up</button>
        <button id="goDown">Down</button>
    </nav>
</template>
<script>
export default {
  name: 'zoomMoveComponent',
  props: {
    canvas
  },
  data() {
    return {

    };
  },
  methods: {
    zoomIn() {
      canvas.setZoom(canvas.getZoom() * 1.1);
    }
  }
};
</script>
<style scoped>

</style>