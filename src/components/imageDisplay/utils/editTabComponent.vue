<template>
  <div>
    <modal name="detail_tag" draggable="#edit_tag_header" height="auto" id="detail_tag">
      <div class="container">
        <div id="edit_tag_header">
          <h3>Tag Details</h3>
        </div>
        <hr />
        <div id="edit_tag_body">
          <h4>Tag Type</h4>
          <p>{{getTagType}}</p>
          <h4>Details</h4>
          <p>{{getDetails}}</p>
          <h4>Other Details</h4>
          <b>Hyperlink : </b><span @click="gotoSheet(getHyperlink)">{{getHyperlink}}</span></p>
          <b>Generic Tag : </b> {{getGenericTag}}
        </div>
        <hr />
        <div id="edit_tag_footer" style="float:right;margin-bottom:10px;">
          <button @click="edit_data" class="btn btn-primary">Edit Details</button>
          <button @click="$modal.hide('detail_tag')" class="btn btn-danger">Close</button>
        </div>
      </div>
    </modal>

    <modal name="edit_tag" draggable="#edit_tag_header" height="auto" id="edit_tag_modal">
      <div class="container">
        <div id="edit_tag_header">
          <h3>Edit Tag</h3>
        </div>
        <hr />
        <div id="edit_tag_body">
          <form>
            <label>Tag Type</label>
            <input class="form-control" type="text" v-model="tag_type"/>

            <label>Generic Tag</label>
            <select class="form-control" v-model="generic_tag">
              <option selected value="false">False</option>
              <option value="true">True</option>
            </select>

            <label>Detail</label>
            <textarea class="form-control" type="text" v-model="detail"></textarea>

            <label>Hyperlink</label>
            <select class="form-control" v-model="hyperlink">
              <option selected value></option>
              <option v-for="(sheet,data) in all_keys.objs">{{data}}</option>
            </select>
          </form>
        </div>
        <hr />
        <div id="edit_tag_footer" style="float:right;margin-bottom:10px;">
          <button @click="save_data" class="btn btn-primary">Save</button>
          <button @click="$modal.hide('edit_tag')" class="btn btn-danger">Cancel</button>
        </div>
      </div>
    </modal>
  </div>
</template>

<script>
export default {
  name: 'editTabComponent',
  props: {
    current_sheet: String,
    all_keys: Object,
    tag_id: String,
    generic_tags: Object
  },
  data() {
    return {
      tag_type: '',
      detail: '',
      hyperlink: '',
      generic_tag: ''
    };
  },
  computed: {
    getTagType() {
      try {
        return this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.tag_type;
      } catch (error) {
        return '';
      }
    },
    getGenericTag() {
      try {
        return this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.generic_tag;
      } catch (error) {
        return '';
      }
    },
    getDetails() {
      try {
        return this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.detail;
      } catch (error) {
        return '';
      }
    },
    getHyperlink() {
      try {
        return this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.hyperlink;
      } catch (error) {
        return '';
      }
    }
  },
  methods: {
    save_data() {
      console.log(this.event);
      this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.tag_type = this.tag_type;
      this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.generic_tag = this.generic_tag;
      this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.detail = this.detail;
      this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.hyperlink = this.hyperlink;

      if (this.generic_tag == 'true') {
        this.generic_tags[this.tag_id] = this.all_keys.objs[this.current_sheet].tags[this.tag_id];
      }

      this.$modal.hide('edit_tag');
    },
    edit_data() {
      this.tag_type = this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.tag_type;
      this.generic_tag = this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.generic_tag;
      this.detail = this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.detail;
      this.hyperlink = this.all_keys.objs[this.current_sheet].tags[this.tag_id].data.hyperlink;
      this.$modal.show('edit_tag');
    },
    gotoSheet(sheet) {
      this.$modal.hide('detail_tag');
      this.$emit('goto_sheet', sheet);
    }
  }
};
</script>

<style scoped>
.container {
  padding: 10px;
}
span:hover{
  cursor:pointer;
  background-color: orange
}
</style>