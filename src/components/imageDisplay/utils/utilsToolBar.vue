<template>
    <div id="utilsToolBar">
        <!-- <button title="Close" @click="close" style="background-color:'red'"><font-awesome-icon :icon="['fas', 'times']"/></button>
        <button title="Previous Plan" v-if="plans==true" @click="goPrevSheet"><font-awesome-icon :icon="['fas', 'backward']"/></button>
        <button v-if="showNav==true" title="Undo" @click="undo"><font-awesome-icon :icon="['fas', 'undo']"/></button>
        <button v-if="showNav==true" title="Redo" @click="redo"><font-awesome-icon :icon="['fas', 'redo']"/></button> -->
    </div>
</template>
<script>
export default {
  name: 'utilsToolBar',
  props: [
    'bus',
    'plans',

    'saveNormalObject',
    'saveLineRulerObject',
    'saveAreaRulerObject',
    'saveCameraObject',
    'onSheetClicked',
    'onVersionClicked',
    'loadPage',
    'allowSelect',
    'showNav'
  ],
  data() {
    return {
      markupSeq: [],
      redoSeq: [],
      sheetSeq: []
    };
  },
  methods: {
    serializeObject(obj) {
      const type = obj[0];
      var obj2 = obj[1];
      switch (type) {
        case 'normal':
          // Promise.resolve(this.bus.$emit('saveNormalObject', obj))
          // .then(x => {
          //     return x
          // })

          // var promise = Promise.resolve()
          // evt.waitUntil = p => promise = p
          // this.bus.$emit('saveNormalObject', obj)

          return this.saveNormalObject(obj2);
        case 'areaRuler':
          // return this.bus.$emit('saveLineRulerObject', obj)
          return this.saveAreaRulerObject(obj2);
        case 'lineRuler':
          // return this.bus.$emit('saveAreaRulerObject', obj)
          return this.saveLineRulerObject(obj2);
        case 'camera':
          // return this.bus.$emit('saveCameraObject', obj)
          return this.saveCameraObject(obj2);
        default:
          return false;
      }
    },
    markupSeqAdd(obj) {
      // obj = [obj[0], this.serializeObject(obj)]

      this.markupSeq.push(obj);

      // if (this.redoSeq.length > 0){
      //     this.redoSeq = []
      // }
    },
    undo() {
      if (this.markupSeq.length === 0) {
        return;
      }
      const obj = this.markupSeq.pop();
      this.$emit('deleteObject', obj);

      this.redoSeq.push(obj);
    },
    redo() {
      if (this.redoSeq.length === 0) {
        return;
      }

      const obj = this.redoSeq.pop();

      this.markupSeq.push(obj);

      // obj = [obj[0], this.serializeObject(obj)]

      this.$emit('addObject', obj);
    },
    reset() {
      this.markupSeq = [];
      this.redoSeq = [];
    },
    addSheetSeq(currentSheet) {
      console.log('Pushing sheet into stack', currentSheet);
      if (this.sheetSeq[this.sheetSeq.length - 1] === currentSheet) {
        return;
      }
      this.sheetSeq.push(currentSheet);
    },
    goPrevSheet() {
      if (this.sheetSeq.length === 1) { return; }
      this.sheetSeq.pop();
      const prevSheet = this.sheetSeq.pop();
      console.log('data wiz popped', prevSheet);
      const version = prevSheet.version;
      const sheetCode = prevSheet.sheet;
      const markupVersion = prevSheet.markupVersion;
      this.allowSelect();
      if (markupVersion === null) {
        console.log('Going into sheet clicked');
        this.onSheetClicked(sheetCode, version);
      } else {
        console.log('Going into version clicked');
        this.onVersionClicked(sheetCode, markupVersion, version);
      }
      setTimeout(() => {
        this.loadPage();
      }, 200);
      // this.bus.$emit("changeImage", prevSheet.sheet, prevSheet.version)
      // this.sheetSeq.pop()
    },
    close() {
      this.bus.$emit('goBack');
    }
  },
  mounted() {
    this.bus.$on('markupSeqAdd', this.markupSeqAdd);
    this.bus.$on('addSheetSeq', this.addSheetSeq);
  }
};
</script>
<style scoped>
/* #utilsToolBar{
    font-family: "Lato", sans-serif;
    background: rgba(22, 13, 13, 0.6);
    padding: 5px;
    z-index: 1000;
    position: absolute;
    top: 49px;
    margin-right: 20px;
    border-radius: 4px;
    width: 70px;
    right:0;
    display: flex;
    flex-direction: column;
    align-items: center;
} */

#utilsToolBar button{
    font-size: 0.9em;
    margin: 5px 10px;
    /* border-radius: 5px; */
    border: none;
    /* border-bottom: 1px solid white; */
    background-color: rgba(0,0,0,0);
    color: seashell;
    width: 40px;
}

#close {
    width: 20px;
    height: 100%;
    border-radius: 5px;
}
</style>>