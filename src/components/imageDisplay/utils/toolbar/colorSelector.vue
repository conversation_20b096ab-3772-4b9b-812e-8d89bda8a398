<template>
    <div>
        <div id = "colorSelector" ref="colorSelector" style="cursor:pointer"  @click="toggleSelector">
            
        </div>
        <div id = "selector" ref="selector" class="selector">
            <div id = "selectorColors" v-for="(color,i) in colors" :key="i" :style="{backgroundColor:color}" @click="setColor(color)">

            </div>
        </div>
    </div>
</template>
<script>
export default {
  name: 'colorSelector',
  inject: ['data'],
  props: [
    'presentColor',
    'event'
  ],
  data() {
    return {
      colors: [
        'red',
        'blue',
        'green',
        'magenta',
        'orange',
        'brown',
        'black',
        'white'
      ]
    };
  },
  methods: {
    toggleSelector() {
      if (this.$refs.selector.style.display === '') {
        try {
          const elements = document.getElementsByClassName('selector');

                    for(var i=0;i<elements.length;i++){
                        elements[i].style.display=""
                    }
                }
                catch(e){console.log(e)}
                this.$refs.selector.style.display = "block";

                var element = this.$refs.selector 
                
                var box = this.$refs['colorSelector'].getBoundingClientRect()
                var newTop = box.y
                // var newLeft = box.x - 87
                
                element.style.display = "block"
                element.style.top = newTop + "px"
                element.style.right = 114 + "px"
            } else {
                this.$refs.selector.style.display = "";
            }
    },
    setColor(color) {
      // this.presentColor = color
      if (this.data.canvas.isDrawingMode && this.event === 'pen') {
        this.data.canvas.freeDrawingBrush.color = color;
      }
      this.$emit('colorUpdate', color);
      this.$refs.colorSelector.style.backgroundColor = color;
      this.toggleSelector();
    }
  },
  mounted() {
    // this.$ref.colorSelector.style.background-color = this.colorSelector
    this.$refs.colorSelector.style.backgroundColor = this.presentColor;
  }
};
</script>

<style lang="scss" scoped>

@import '../../assets/css/toolbar.scss';

#colorSelector{
    height: 15px;
    width:15px;
    border:1px solid;
    border-radius: 50%;
    cursor: pointer;
    margin: 10px
}
#selectorColors{
    height: 15px;
    width:15px;
    border-radius: 50%;
    cursor: pointer;
    margin: 10px
}
</style>