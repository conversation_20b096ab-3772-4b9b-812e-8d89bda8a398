<template>
    <div>
        <!-- <button @click="getClickCoordinates(addArrow)"><i class="fa fa-long-arrow-left"></i></button> -->
        <!-- <button ref="actionButton" @click="toggleSelector" title="Add Arrow"><font-awesome-icon style="cursor:pointer" :icon="['fas', 'arrow-right']"/></button>
        <div id = "selector" ref="selector" class="selector" style="right: 115px;">
            <center>
                <button v-for="(angle,i) in angles" :key="i" @click="setAngle(angle.angle)">
                    <font-awesome-icon style="cursor:pointer" :icon="['fas', 'arrow-right']" :rotation="angle.angle"/>
                </button>
            </center>
        </div> -->
    </div>
</template>
<script>
export default {
    data(){
        return{
            angles:{
                left:{
                    angle:180,
                },
                right:{
                    angle:0,
                },
                top:{
                    angle:270,
                },
                bottom:{
                    angle:90,
                }
            }
        }
    },
    methods:{
        toggleSelector:function(){
            if (this.$refs.selector.style.display === "") {
                try{
                    var elements = document.getElementsByClassName("selector")

                    for(var i=0;i<elements.length;i++){
                        elements[i].style.display=""
                    }
                }
                catch(e){console.log(e)}
                this.$refs.selector.style.display = "block";

                var element = this.$refs.selector 
                
                var box = this.$refs['actionButton'].getBoundingClientRect()
                var newTop = box.y
                // var newLeft = box.x - 87
                
                element.style.display = "block"
                element.style.top = newTop + "px"
                element.style.right = 114 + "px"
            } else {
                this.$refs.selector.style.display = "";
            }
    },
    setAngle(angle) {
      this.$emit('renderArrow', angle);
      // this.toggleSelector()
    }
  }
};
</script>
<style lang="scss" scoped>

// <style scoped>
@import '../../assets/css/toolbar.scss';
</style>
