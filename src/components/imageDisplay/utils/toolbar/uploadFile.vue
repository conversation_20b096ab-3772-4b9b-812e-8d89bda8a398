<template>
  <div class="col-right">
    <input id="fileInput" type="file" ref="componentRef" style="display:none" @change="uploadAction" multiple :accept="accept"/>
    <!-- <button
      class="upload-button"
      v-bind:class="{ 'disabled': isDisabled }"
      onclick="document.getElementById('fileInput').click()"
      :disabled="isDisabled"
    >Upload File</button> -->
  </div>
</template>

<script>
export default {
  name: 'upload-files',
  props: {
    isDisabled: Boolean,
    uploadAction: Function,
    accept: String
  },
  methods: {
    clear() {
      this.$refs.componentRef.value = '';
    }
  }
};
</script>

<style lang="scss" scoped>
.upload-button {
  background: linear-gradient(137deg, #f25e45, #f79048);
    text-align: center;
    color: white;
    height: 38px;
    font-weight: bold;
    border-radius: 6px;
    padding: 5%;
    font-size: small;
    margin-top: 3%;
    width: 100%;
}
.disabled {
  opacity: 0.5;
  cursor: default;
}
</style>
