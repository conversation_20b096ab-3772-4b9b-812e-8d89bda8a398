<template>
    <div>
        <template v-if="type == 'pen'">
            <!-- <button @click="freeHandDraw()"><i class="fas fa-pencil-alt"></i></button> -->
            <button ref="actionButton" title="Pen" @click="toggleSelector"><font-awesome-icon :icon="['fas', 'pencil-alt']"/></button>
            <div id = "selector" ref="selector" class="selector">
                <center>
                    <button v-for="(size,i) in sizesPen" :key="i" @click="setSize(size.size)">
                        <font-awesome-icon style="cursor:pointer"  :icon="['fas', 'circle']" :size="size.icon"/>
                    </button>
                </center>
            </div>
        </template>
        <template v-if="type == 'highlighter'">
            <!-- <button @click="freeHandDraw()"><i class="fas fa-pencil-alt"></i></button> -->
            <button ref="actionButton" title="Highlighter" @click="toggleSelector"><font-awesome-icon :icon="['fas', 'highlighter']"/></button>
            <div id = "selector" ref="selector" class="selector">
                <center>
                    <button v-for="(size,i) in sizesHighlighter" :key="i" @click="setSize(size.size)">
                        <font-awesome-icon style="cursor:pointer"  :icon="['fas', 'circle']" :size="size.icon"/>
                    </button>
                </center>
            </div>
        </template>
    </div>
</template>
<script>
export default {
  props: ['type'],
  data() {
    return {
      sizesPen: {
        extraSmall:{
          size: 1,
          icon: 'xs',
        },
        small: {
          size: 2,
          icon: 'xxs'
        },
        medium: {
          size: 4,
          icon: 'lg'
        },
        large: {
          size: 8,
          icon: '2x'
        }
      },
      sizesHighlighter: {
        extraSmall:{
          size: 8,
          icon: 'xs',
        },
        small: {
          size: 16,
          icon: 'xxs'
        },
        medium: {
          size: 32,
          icon: 'lg'
        },
        large: {
          size: 64,
          icon: '2x'
        }
      }
    };
  },
  methods: {
    toggleSelector:function(){
            if (this.$refs.selector.style.display === "") {
                try{
                    var elements = document.getElementsByClassName("selector")

                    for(var i=0;i<elements.length;i++){
                        elements[i].style.display=""
                    }
                }
                catch(e){console.log(e)}
                var element = this.$refs.selector 
                
                var box = this.$refs['actionButton'].getBoundingClientRect()
                var newTop = box.y
                // var newLeft = box.x - 87
                
                element.style.display = "block"
                element.style.top = newTop + "px"
                element.style.right = '114' + "px"

            } else {
                this.$refs.selector.style.display = "";
            }
    },
    setSize(size) {
      // console.log(size)
      this.$emit('freeHandSize', size);
      // this.toggleSelector()
    }
  }
};
</script>
<style lang="scss" scoped>
@import '../../assets/css/toolbar.scss';
center{
  display: flex;
  flex-direction: column;
  align-items: center;
  button{
    cursor: pointer;
  }
}
</style>