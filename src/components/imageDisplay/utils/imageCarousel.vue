<template>
  <div>
      <button @click="prev" id="prevButton"><font-awesome-icon :icon="['fas', 'arrow-left']" :size="'6x'"/></button>
      <button @click="next" id="nextButton"><font-awesome-icon :icon="['fas', 'arrow-right']" :size="'6x'"/></button>
  </div>
</template>

<script>
export default {
  name: 'imageCarousel',
  props: [
    'imagesMeta',
    'currentImage',
    'setCurrentData'
  ],
  data() {
    return {
      imageIndex: null
    };
  },
  methods: {
    next() {
      if (this.imageIndex === this.imagesMeta.length - 1) { return; }
      this.imageIndex++;
      this.setCurrentData(
        this.imagesMeta[this.imageIndex],
        -1
      );
    },
    prev() {
      if (this.imageIndex === 0) { return; }
      this.imageIndex--;
      this.setCurrentData(
        this.imagesMeta[this.imageIndex],
        -1
      );
    },
    loadCarousel() {
      this.imageIndex = this.currentImage;
      this.setCurrentData(
        this.imagesMeta[this.imageIndex],
        -1
      );
    }
  }
};
</script>
<style scoped>
#prevButton{
  position: absolute;
  top: 50%;
  margin-left: 20px;
  left:0;
  z-index: 1000;
}
#nextButton{
    top: 50%;
    position: absolute;
    right: 0;
    margin-right: 20px;
    z-index: 1000;
}
</style>