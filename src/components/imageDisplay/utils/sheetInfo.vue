<template>
    <div>
        <div  v-if="false" id = "sheet-info">

                <span>{{ sheetCode }}</span> <br>
                
                
                <!-- | {{sheetVersion}} <br>

                <div v-if="markupVersion!=null">
                    {{markupVersion}}<br>
                </div> -->
                
                <v-select
                    v-if="sheetVersionPresent==false"

                    id="sheetVersion"
                    :searchable="false"
                    :clearable="false"
                    :value="sheetVersion"
                    :options="sheetOptions"
                    @input="switchVersion"
                />

                <v-select
                    v-if="markupVersionPresent==false"

                    id="markupVersion"
                    :placeholder="'No Markups'"
                    :searchable="false"
                    :clearable="false"
                    :value="markupVersion"
                    :options="markupOptions"
                    @input="switchMarkup"
                />

                <!-- <select>
                    <option v-for="markup in markupVersions">{{markup.name}}</option>
                </select> -->

                <div v-if="current_data.sheet_title!=null">
                    <span>{{ current_data.sheet_title }}</span><br>
                </div>


                <div v-if="pxVal!=null">                      
                    <span>100 px : {{ pxVal }} </span>
                </div>
                <div v-else>
                    <span>Scale not set</span>
                </div>

            </div>
            <!-- <div id="version-selector">
                <vSelect
                    id="sheetVersion"
                    class="style-chooser"
                    :searchable="false"
                    :clearable="false"
                    :value="sheetVersion"
                    :options="sheetOptions"
                    @input="switchVersion"
                    :disabled="sheetVersionPresent"
                />

                <vSelect
                    id="markupVersion"
                    class="style-chooser"
                    :placeholder="'No Markups'"
                    :searchable="false"
                    :clearable="false"
                    :value="markupVersion"
                    :options="markupOptions"
                    @input="switchMarkup"
                    :disabled="markupVersionPresent"
                />
            </div> -->
    </div>
</template>

<script>
import vSelect from 'vue-select';

import keys from 'lodash/keys';
import forEach from 'lodash/forEach';

export default {
  name: 'sheetInfo',
  components: {
    vSelect
  },
  computed: {
    markupOptions() {
      // return Object.keys(this.markupVersions).map(version => this.markupVersions[version].name)
      const optionList = [];
      forEach(keys(this.markupVersions), version => {
        if (this.markupVersions[version].sheetVersion[this.sheetVersion]) {
          optionList.push({
            label: this.markupVersions[version].name,
            version
          });
        }
      });
      return optionList.reverse();
    },
    sheetOptions() {
      return keys(this.sheetVersions).reverse();
    },
    sheetVersionPresent() {
      if (this.sheetOptions.length > 1) { return false; }
      return true;
    },
    markupVersionPresent() {
      if (this.markupOptions.length > 0) { return false; }
      return true;
    }
  },
  props: [
    'sheetCode',
    'markupVersion',
    'current_data',
    'pxVal',
    'markupVersions',
    'onVersionClicked',
    'sheetVersion',
    'loadPage',
    'onSheetClicked',
    'sheetVersions',
    'allowSelect'
    // "sheetVersion"
  ],
  methods: {
    switchMarkup(markupVersion) {
      console.log('1 switch markup Triggered, value received => ', markupVersion);
      if (this.markupsVersion === markupVersion) {
        console.log('2 Not switching');
        return;
      }
      console.log('2 Switching passed');
      if ((markupVersion.version !== null) && (markupVersion.version !== '') && (markupVersion.version !== 'null')) {
        console.log('3 Loading the page');
        this.allowSelect();
        this.onVersionClicked(this.sheetCode, markupVersion.version, this.sheetVersion);
        this.loadPage();
      }
    },
    switchVersion(version) {
      console.log('1 switch version Triggered, value received => ', version);
      if (this.sheetVersion === version) {
        console.log('2 Not switching');
        return;
      }

      console.log('2 Switching passed');
      if ((version !== null) && (version !=='') && (version !== 'null')) {
        console.log('3 Loading the page');
        this.allowSelect();
        this.onSheetClicked(this.sheetCode, version);
        this.loadPage();
      }
    }
  }
};
</script>

<style scoped>

#sheet-info{
  position: fixed;
  margin-top: 10px;
  margin-left:10px;
  background: rgba(22, 13, 13, 0.6);
  padding: 5px;
  padding-left:10px;
  z-index: 1000;
  border-radius: 4px;
  color: seashell;
  display: block;
  max-width: 250px;
}
/* #sheet-info span{
    padding-left: 5px;
} */

/* #version-selector{
    position: absolute;
    margin-top: 10px;
    margin-left: 250px;
    padding: 5px;
    z-index: 1000;
    display: inline-flex;
    max-width: fit-content;
} */
/* select{
    background: rgba(22, 13, 13, 0.1);
    width: 100%;
    border: none;
    border-radius: 5%;
    color: white;
}
select::-ms-expand {
    background: black;
} */

/* .style-chooser .vs__search::placeholder,
.style-chooser .vs__dropdown-toggle,
.style-chooser .vs__dropdown-menu {
background: #dfe5fb;
border: none;
color: #394066;
text-transform: lowercase;
font-variant: small-caps;
}

.style-chooser .vs__clear,
.style-chooser .vs__open-indicator {
fill: #394066; */
/* } */
#sheetVersion{
    width:150px;
}
#markupVersion{
    width:150px;
}

/* .v-select{
    background: #dfe5fb;
    border: none;
    color: #394066;
    text-transform: lowercase;
    font-variant: small-caps;
}

.v-select .vs__search::placeholder,
  .v-select .vs__dropdown-toggle,
  .v-select .vs__dropdown-menu {
    background: #dfe5fb;
    border: none;
    color: #394066;
    text-transform: lowercase;
    font-variant: small-caps;
  }

  .v-select .vs__clear,
  .v-select .vs__open-indicator {
    fill: #394066;
  } */
.v-select.single .selected-tag {
    background-color: transparent;
    border-color: transparent;
    /* overflow-x: hidden; */
    color: white;
}

.v-select.unsearchable .dropdown-toggle {
    cursor: pointer;
    margin: 5px 0px 5px 0px;
}
</style>