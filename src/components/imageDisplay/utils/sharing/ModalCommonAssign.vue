<template>
<modal
  name="modal-common-assign"
  class="modal-common-assign"
  :width="270"
  :height="392"
  @before-open="beforeOpen"
  @close="close"
  @before-close="beforeClose"
>
  <form-common-assign
    :assignments="assignments"
    :users="membersToAssign"
    :user="user.uid"
    @save="save"
  ></form-common-assign>
</modal>
</template>
<script>

// import FormCommonAssign from '@/components/common/ProjectPortal/FormCommonAssign';
import FormCommonAssign from './FormCommonAssign';
import { employeesForCompanies } from '@/helpers/employee';
import { mapGetters } from 'vuex';

export default {
  name: 'modal-common-assign',
  components: {
    FormCommonAssign
  },
  data() {
    return {
      assignments: null,
      onSave: null
    };
  },
  props: {
    projectMembers: {
      type: Object
    }
  },
  computed: {
    ...mapGetters(['currentProjectMembersArray', 'user']),
    membersToAssign() {
      console.log(this.currentProjectMembersArray);
      if (this.currentProjectMembersArray.length > 0) {
        return this.currentProjectMembersArray.filter(member => employeesForCompanies[member.accessLevel]);
      }
      const pms = [];
      Object.keys(this.projectMembers).forEach((userId => {
        const tempObj = { ...this.projectMembers[userId], id: userId, projectId: this.$route.params.id };
        pms.push(tempObj);
      }));
      return pms;
    }
  },
  methods: {
    close() { this.$modal.hide('modal-common-assign'); },
    closeByEscape(e) { if (e.keyCode === 27) this.close(); },
    save(assignments) { this.onSave(assignments); },
    beforeOpen(event) {
      const {
        assignments,
        onSave
      } = event.params;
      this.assignments = assignments;
      this.onSave = onSave;
      document.addEventListener('keyup', this.closeByEscape);
    },
    beforeClose() {
      this.assignments = null;
      this.onSave = null;
      document.removeEventListener('keyup', this.closeByEscape);
    }
  }
};
</script>
