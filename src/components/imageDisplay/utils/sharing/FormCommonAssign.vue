<template>
<form class="form form-common-assign" novalidate @submit.prevent @keypress.enter.prevent>
  <h2 class="form-title">Add Participants</h2>
  <div class="form-fields vb--inverted" v-bar>
    <ul class="common-assign__list">
      <li class="common-assign__item"
        v-for="user in data"
        :key="user.id"
        @click="toggleUser(user)"
      >
        <form-checkbox
          :static="true"
          :checked="user.isChecked"
          :indeterminate="user.indeterminate"
        ></form-checkbox>
        <div class="visual">
          <user-avatar
            :photoURL="user.photoURL"
            :id="user.id"
          ></user-avatar>
        </div>
        <div class="info">
          <h3>{{ user.firstName }} {{ user.lastName }}</h3>
          <div class="job-title">
            {{ user.jobTitle }}<template v-if="employeeAccessLevel[user.accessLevel] && employeeAccessLevel[user.accessLevel].length > 0">, {{ employeeAccessLevel[user.accessLevel] }}</template>
            <br/><span>{{getCompanyName(user.companyPIN)}}</span>
          </div>
        </div>
      </li>
    </ul>
  </div>
  <div class="version-holder">
    <input type="text" placeholder="Version Name" required v-model="versionName" @keyup="checkFlag()">
  </div>
  <div class="form-actions">
    <button class="btn-link"
      @click="clear"
      v-show="checkedMembers.length"
    >Clear</button>
    <button class="btn-default"
      @click="save"
      :disabled="!(isMutated && nameFlag)"
    >Ok</button>
  </div>
</form>
</template>
<script>
import { mapGetters } from 'vuex';
import { employeeAccessLevel } from '@/helpers/employee';

export default {
  name: 'common-assign',
  props: {
    users: {
      required: true,
      type: Array
    },
    assignments: {
      required: true,
      type: Array
    },
    user: {
      type: String
    }
  },
  data() {
    return {
      versionName: '',
      nameFlag: false,
      data: this.users.slice(0).map(user => {
        const obj = {
          ...user,
          isChecked: this.getCheckedStatus(user)
        };
        return obj;
      }),
      companyObj: {},
      employeeAccessLevel
    };
  },
  created() {
    this.$apiService.Company.getCompanies(this.setCompanyObj, this.handleError);
    // console.log(this.currentStatusAr);
  },
  computed: {
    ...mapGetters(['projectUsersTasks']),
    assignmentIds() { return this.assignments.map(item => item.id); },
    determinateData() { return this.data.filter(member => !member.indeterminate); },

    checkedMembers() { return this.determinateData.filter(member => member.isChecked); },
    uncheckedMembers() { return this.determinateData.filter(member => !member.isChecked); },

    newAssigns() {
      return this.checkedMembers.filter(member => {
        if (!this.assignmentIds.includes(member.id)) return true;
      });
    },
    removedAssigns() { return this.uncheckedMembers.filter(member => this.assignmentIds.includes(member.id)); },

    isMutated() { return !!this.newAssigns.length || !!this.removedAssigns.length; }
  },

  methods: {
    getCheckedStatus(user) {
      console.log('user:', this.user);
      if (user.id === this.user) {
        return 1;
      }
      const assignedUser = this.assignments.find(assignment => assignment.id === user.id);
      return !!assignedUser;
    },
    toggleUser(user) {
      user.isChecked = !user.isChecked;
      user.indeterminate = false;
    },
    save() {
      const update = {
        users: {},
        versionName: this.versionName
      };
      this.newAssigns.forEach(assign => { update.users[assign.id] = true; });
      this.removedAssigns.forEach(assign => { update.users[assign.id] = null; });
      this.$emit('save', update);
    },
    clear() {
      this.data.forEach(item => {
        item.isChecked = false;
      });
    },
    setCompanyObj(snapshot) {
      this.companyObj = snapshot.val();
    },
    getCompanyName(companyPIN) {
      try {
        if (companyPIN) {
          const company = this.companyObj[companyPIN];
          return company.name;
        }
        return '';
      } catch (e) {}
    },
    checkFlag() {
      if (this.versionName !== '') this.nameFlag = true;
      else this.nameFlag = false;
    }
  }
};
</script>
<style lang="scss">
.common-assign {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  &__dropdown {
    position: absolute;
    z-index: 100;
    top: 100%;
    right: 0;
    box-shadow: 4px 5px 10px 0 rgba(121, 117, 117, 0.39);
    border: solid 1px rgba(221, 223, 229, 0.7);
    border-radius: 2px;
    background: #fff;
    width: 270px;
    .title {
      font-size: 12px;
      line-height: 16px;
      padding: 13px 0 11px;
      margin: 0 20px;
      color: #4D4D4D;
      font-weight: bold;
      opacity: 0.9;
      border-bottom: 1px solid rgba(#dddfe5, 0.3);
    }
  }
  &__opener {
    display: block;
    color: #a3a3a3;
    background: $clr-color12;
    font-size: 0;
    line-height: 0;
    border-radius: 50%;
    padding-right: 2px;
    width: 26px;
    height: 26px;
    &:disabled,
    &.is-disabled {
      cursor: default;
      opacity: 0.5;
    }
  }
  &__item {
    cursor: pointer;
    padding: 0 20px;
    position: relative;
    & + li:before {
      content: '';
      position: absolute;
      top: 0;
      left: 20px + 26px + 17px;
      right: 20px;
      border-top: 1px solid rgba(#dddfe5, 0.3);
    }
    .form-checkbox {
      position: absolute;
      top: 13px;
      right: 30px;
    }
    &:hover {
      background: #f7f7f7;
    }
  }
  &__list {
    li {
      height: 74px;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      .visual {
        margin: 15px 0;
        align-self: flex-start;
      }
      .info {
        font-size: 12px;
        line-height: 16px;
        flex-grow: 1;
        padding: 3px 0 5px;
        margin-left: 17px;
        align-self: center;
        h3 {
          font-weight: normal;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .job-title {
          color: #999;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          display: block;
        }
        .tasks-count {
          color: #666;
          strong {
            color: #4D4D4D;
            font-weight: bold;
          }
        }
      }
    }
  }
  &__actions {
    height: 59px;
    display: flex;
    padding: 12px 0;
    justify-content: space-between;
    margin: 0 20px;
    align-items: center;
    border-top: 1px solid rgba(#dddfe5, 0.3);
  }
}

.form-common-assign {
  .form-title {
    font-size: 12px;
    line-height: 16px;
    padding: 13px 0 11px;
    margin: 0 20px;
    color: #4D4D4D;
    font-weight: bold;
    opacity: 0.9;
    border-bottom: 1px solid rgba(#dddfe5, 0.3);
  }
  .form-fields {
    height: 296px;
  }
  .form-actions {
    height: 59px;
    display: flex;
    padding: 12px 0;
    justify-content: space-between;
    margin: 0 20px;
    align-items: center;
    border-top: 1px solid rgba(#dddfe5, 0.3);
    & > * + * {margin-left: auto;}
  }
}
.version-holder {
  margin-top: 10px;
  margin-left: 20px;
  input {
    border: 1px solid #eee;
    height: 30px;
    padding-left: 10px;
  }
}
</style>
