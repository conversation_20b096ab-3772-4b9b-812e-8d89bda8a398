<template>
  <div id="plan-list">
    <plansList
      :plansData="plansData"
      :onSheetClicked="onSheetClicked"
      :getVersions="getVersions"
      :getMarkups="getMarkups"
      :baseURL="baseURL"
      :focusSheet="focusSheet"
      :onVersionClicked="onVersionClicked"
      :fetchmarkup="fetchmarkup"
      :cameraAttachment="cameraAttachment"
    />
  </div>
</template>
<script>
import plansList from "../plansList/plansList";

export default {
  name: "plan-list",
  components: {
    plansList,
  },
  props: [
    "baseURL",
    "plansData",
    "focusSheet",
    "onSheetClicked",
    "getMarkups",
    "getVersions",
    "fetchmarkup",
    "onVersionClicked",
    "cameraAttachment",
  ],
};
</script>

<style scoped></style>
