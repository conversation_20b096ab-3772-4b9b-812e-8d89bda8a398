<template>
  <div id="plan">
    <imageDisplay
      :plans="true"
      :sheetVersion="sheetVersion"
      :sheetCode="sheetCode"
      :all_data="plansData"
      :sheetDataProp="sheetDataProp"
      :markUpVersionData="markUpVersionData"
      :markupVersion="markupVersion"
      :markupVersions="markupVersions"
      :markupVersionID="markupVersionID"
      :goBack="goBack"
      :onVersionClicked="onVersionClicked"
      :onSheetClicked="onSheetClicked"
      :base_url="baseURL"
      :saveProp="onSave"
      :showNav="showNav"
      :switchProps="switchProps"
      :onUpload="onUpload"
      :cameraAttachment="cameraAttachment"
      :markupTitle="markupTitle"
    />
  </div>
</template>

<script>
import imageDisplay from "../imageDisplay/imageDisplay";

export default {
  name: "plan",
  components: {
    imageDisplay,
  },
  props: [
    "sheetVersion",
    "sheetCode",
    "plansData",
    "sheetDataProp",
    "markUpVersionData",
    "markupVersion",
    "markupVersions",
    "markupVersionID",
    "markupTitle",
    "goBack",
    "onVersionClicked",
    "onSheetClicked",
    "switchProps",
    "baseURL",
    "onSave",
    "onUpload",
    "showNav",
    "cameraAttachment",
  ],
};
</script>

<style scoped></style>
