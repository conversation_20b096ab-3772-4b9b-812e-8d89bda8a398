<template>
    <div id="routes">
        <template v-if="type==='plan'">
            <plan v-bind="$attrs" ></plan>
        </template>
        <template v-else-if="type==='planList'" >
            <planList v-bind="$attrs"></planList>
        </template>
        <template v-else-if="type==='planListPlan'" >
            <planListPlan v-bind="$attrs"></planListPlan>
        </template>
        <template v-else-if="type==='pdfViewer'" >
            <pdfViewer v-bind="$attrs"></pdfViewer>
        </template>
        <template v-else-if="type==='imageViewer'" >
            <imageViewer v-bind="$attrs"></imageViewer>
        </template>
        <template v-else-if="type==='imageCarousel'" >
            <imageCarousel v-bind="$attrs"></imageCarousel>
        </template>
         <template v-else-if="type==='planAttachment'" >
            <planAttachment v-bind="$attrs"></planAttachment>
        </template>
        <template v-else-if="type==='markuptable'" >
            <markupTable v-bind="$attrs"></markupTable>
        </template>
        <template v-else-if="type==='plansAdmin'">
            <plansAdmin
                v-bind="$attrs"
            />
        </template>
        <template v-else-if="type ==='markupLayer'">
            <markupLayer
                v-bind="$attrs"
            />
        </template>
        <!-- <template v-else>
            <div id="error">
                Proper set of props not provided
            </div>
        </template> -->
    </div>
</template>

<script>
import plan from './plan'
import planList from './planList'
import planListPlan from './planListPlan'
import pdfViewer from './pdfViewer'
import imageViewer from './imageViewer'
import imageCarousel from './imageCarousel' 
import planAttachment from './planAttachment'
import markupTable from './markupTable'
import plansAdmin from '@/components/imageDisplay/admin/AdminImageDisplay.vue'
import markupLayer from './markupLayer.vue'

export default {
    name:"component-routing",
    props:[
        "type"
    ],
    components:{
        plan,
        planList,
        planListPlan,
        pdfViewer,
        imageCarousel,
        imageViewer,
        plansAdmin,
        planAttachment,
        markupTable,
        markupLayer,
    }
}
</script>

<style scoped>

</style>