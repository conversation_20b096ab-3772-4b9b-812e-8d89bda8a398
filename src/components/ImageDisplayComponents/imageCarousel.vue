<template>
  <div id="image-carousel">
    <imageDisplay
      :plans="false"
      :showNav="false"
      :type="'image-carousel'"
      :imagesMeta="imagesMeta"
      :currentImage="currentImage"
      :goBack="goBack"
    />
  </div>
</template>

<script>
import imageDisplay from '../imageDisplay/imageDisplay'

export default {
  name:"image-carousel",
  props:[
    "imagesMeta",
    "currentImage",
    "goBack"
  ],
  components:{
    imageDisplay
  }
}
</script>

<style scoped>

</style>