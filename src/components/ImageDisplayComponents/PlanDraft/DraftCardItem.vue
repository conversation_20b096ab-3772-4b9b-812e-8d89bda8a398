<template>
  <Box :columnSize="2">
    <template #body>
      <AlertWrapper :objectId="pbt_id">
        <div class="draft-items">
          <Box :columnSize="2">
            <template #body>
              <div class="draft-top">
                <div class="item-left">
                  <Tag
                    :label="planName"
                    type="baseLight2"
                    size="small"
                    borderRadius="medium"
                    textColor="white"
                  ></Tag>
                </div>
                <div class="item-right">
                  <p>{{ planDate }}</p>
                </div>
              </div>
            </template>
          </Box>
          <Box :columnSize="3">
            <template #body>
              <div class="draft-middle">
                <div class="middle-section-1">
                  <h4 class="draft-title" :title="planTitle">
                    {{ planTitle }}
                  </h4>
                </div>
                <div class="middle-section-2">
                  <div class="middle-items">
                    <!-- <p v-for="(data, index) in 2" :key="index"> -->
                    <Avatar
                      backgroundColor="secondary"
                      :firstNameInitials="uploadedUserInitial"
                    />
                    <!-- </p> -->
                  </div>
                </div>
              </div>
            </template>
          </Box>
          <Box :columnSize="3">
            <template #body>
              <div class="draft-bottom">
                <ProgressBar
                  :status="planStatus"
                  :progress="progress"
                  :color="color"
                  size="small"
                ></ProgressBar>
              </div>
            </template>
          </Box>
        </div>
      </AlertWrapper>
    </template>
  </Box>
</template>

<script>
import toUpper from "lodash/toUpper";
export default {
  data() {
    return {};
  },
  props: {
    planName: String,
    planVersion: String,
    planDate: String,
    planTitle: String,
    planStatus: String,
    planProgress: {
      type: Object,
      default() {
        return {
          isProcessed: false,
          isProcessing: false,
          isFailed: false,
        };
      },
    },
    planUploadedBy: {
      type: String,
      default: "",
    },
    progress: {
      type: Number,
      default: 7,
    },
    color: {
      type: String,
      default: "secondary",
    },
    prj_id: {
      type: String,
      default: "",
    },
    pbt_id: {
      type: String,
      default: "",
    },
  },
  computed: {
    uploadedUserInitial() {
      return toUpper(this.planUploadedBy);
    },
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
@mixin setFont {
  font-family: Lato;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
}
.draft-items {
  cursor: pointer;
  height: 230px;
  box-sizing: border-box;
  margin: 20px 10px;
  border-radius: 16px;
  background: #ffffff;
  padding: 20px 20px 20px 25px;

  .draft-top {
    display: flex;
    .item-left {
      display: flex;

      .plan-name {
        height: 30px;
        border-radius: 5px;
        padding: 5px;
        background: #adc1d1;
        font-family: Lato;
        font-style: normal;
        font-weight: bold;
        font-size: 14px;
        color: #ffffff;
      }
      .plan-version {
        @include setFont();
        padding: 5px;
        color: #2b446c;
      }
    }
    .item-right {
      @include setFont();
      margin-left: auto;
      color: #809fb8;
    }
  }

  .draft-middle {
    .middle-section-1 {
      margin: 15px 0;
      .draft-title {
        @include setFont();
        font-size: 18px;
        color: #2b446c;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .middle-section-2 {
      margin-top: 20px;
      .middle-items {
        display: flex;

        .profile-image {
          margin-right: 10px;
        }
      }
    }
  }

  .draft-bottom {
    margin-top: 30px;
    width: 90%;
    .plan-status {
      @include setFont();
      font-size: 14px;
      color: #2b76e7;
    }

    .plan-progress {
      margin-top: 10px;
      margin-right: 20px;
      border: 1px solid #e7edf5;
      border-radius: 15px;
      .progress-bar {
        border-radius: 15px;
        background: linear-gradient(to right, #2b76e7 100%, #e7edf5 0%);
        height: 5px;
        width: 100%;
      }
    }

    .processed {
      .plan-status {
        color: #2b76e7;
      }
      .progress-bar {
        background: linear-gradient(to right, #2b76e7 100%, #e7edf5 0%);
      }
    }

    .processing {
      .plan-status {
        color: #2b446c;
      }
      .progress-bar {
        background: linear-gradient(to right, #2b446c 70%, #e7edf5 0%);
      }
    }

    .failed {
      .plan-status {
        color: #809fb8;
      }
      .progress-bar {
        background: linear-gradient(to right, #809fb8 70%, #e7edf5 0%);
      }
    }
  }
}
</style>
