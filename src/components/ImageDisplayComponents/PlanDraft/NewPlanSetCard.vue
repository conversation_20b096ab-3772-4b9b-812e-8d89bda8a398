<template>
  <Box :columnSize="3">
    <template #body>
        <div class="draft-items">
          <Box :columnSize="2">
            <template #body>
              <div class="draft-top">
                <div class="draft-title-wrapper">
                  <p class="draft-planName" :title="planName">
                    {{ planName }}
                  </p>
                  <p class="draft-title" :title="planTitle">
                    {{ planTitle }}
                  </p>
                </div>
                <div class="item-right">
                  <StatusTag :planStatus="planStatus" />
                </div>
              </div>
            </template>
          </Box>
          <Box :columnSize="3">
            <template #body>
              <div class="draft-middle">
                <div class="middle-section-1">
                  <hr class="section-divider" />
                </div>
              </div>
            </template>
          </Box>
          <Box :columnSize="3">
            <template #body>
              <div class="draft-bottom">
                <!-- card details  -->
                <div class="card-details">
                  <div class="keys">
                    <p class="detail-keys" >No of sheets :</p>
                    <p class="detail-keys" >Created on :</p>
                    <p class="detail-keys" >Uploaded By :</p>
                    <p class="detail-keys" >Company Name :</p>
                  </div>
                  <div class="values">
                    <p class="detail-values" :title="sheetCount">{{ sheetCount }}</p>
                    <p class="detail-values" :title="planUpdatedOn">{{ planUpdatedOn }}</p>
                    <p class="detail-values" :title="planUploadedBy" >{{ planUploadedBy }}</p>
                    <p class="detail-values" :title="companyName">{{ companyName }}</p>
                  </div>
                </div>
              </div>
            </template>
          </Box>
        </div>
    </template>
  </Box>
</template>

<script>
import StatusTag from './StatusTag.vue';

export default {
  components: {
    StatusTag,
  },
  props: {
    planName: String,
    planUpdatedOn: String,
    sheetCount: Number,
    planTitle: String,
    planStatus: String,
    companyName: {
      type: String,
      default: "",
    },
    planUploadedBy: {
      type: String,
      default: "",
    },
    progress: {
      type: Number,
      default: 7,
    },
    color: {
      type: String,
      default: "secondary",
    },
  },
  data() {
    return {};
  },
  computed: {
    
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
@mixin setFont {
  font-family: Lato;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
}
.detail-keys{
  @include setFont();
  color: #809fb8;
  font-weight: 500;
  font-size: 10px;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  padding: 0.5px 0px;

}
.detail-values{
  @include setFont();
  color: #2b446c;
  font-weight: 700;
  font-size: 10px;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  padding: 0.5px 0px;
}
.card-details {
  display: flex;
  justify-content: flex-start;
  gap: 20px;
  height: 100%;
  width: 100%;
  .keys {
    display: flex;
    flex-direction: column;
    width: 45%;
    h6 {
      @include setFont();
      color: #809fb8;
      margin-bottom: 10px;
      font-weight: 500;
      font-size: 14px;
    }
  }
  .values {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    width: 55%;
    h6 {
      @include setFont();
      color: #2b446c;
      margin-bottom: 10px;
      font-weight: 700;
      font-size: 14px;
    }
  }
}
.draft-title-wrapper {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  height: 50px;
  width: 40%;
  @include setFont();
  font-size: 18px;
  color: #2b446c;
  font-weight: 700;
}
.draft-planName {
  font-size: 12px;
}
.section-divider {
  border: 0.2px solid #e7edf5;
}
.draft-items {
  cursor: pointer;
  height: 184px;
  box-sizing: border-box;
  margin: 20px 10px;
  border-radius: 6px;
  background: #ffffff;
  padding: 20px 20px 20px 25px;

  .draft-top {
    display: flex;
    justify-content: space-between;
    .item-right {
      @include setFont();
      color: #809fb8;
    }
    .draft-title {
        @include setFont();
        font-size: 14px;
        font-weight: 600;
        color: #2b446c;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        height: 40px;
        padding-top: 5px;
      }
  }

  .draft-middle {
    .middle-section-1 {
      margin: 5px 0;
    }
  }

  .draft-bottom {
    width: 100%;
  }
}
</style>
