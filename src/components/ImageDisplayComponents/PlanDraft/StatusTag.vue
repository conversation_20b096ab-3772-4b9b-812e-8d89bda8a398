<template>
    <p :class="['plan-status',statusClass]">
        {{ planStatus }}
    </p>
</template>

<script>

import { BATCH_STATUS_TEXT } from '@/constants';
export default {
    props: {
        planStatus: {
            type: String,
            required: true
        },
    },
    computed: {
        statusClass() {
            console.log(this.planStatus);
            // return this.status_code_class_matcher[this.planStatus];
            switch (this.planStatus) {
    case BATCH_STATUS_TEXT["DON"]:
      return 'done';
    case BATCH_STATUS_TEXT["PEN"]:
      return 'pending';
    case BATCH_STATUS_TEXT["PCS"]:
      return 'processing';
    case BATCH_STATUS_TEXT["FAL"]:
      return 'failed';
    default:
      return ''; // or any default value you prefer
  }
        }
    },
    
}

</script>

<style scoped>
    .plan-status {
    padding: 6px 14px 6px 14px;
    font-size: 12px;
    font-weight: 700;
    border-radius: 14px;
    text-align: center
}
.processing{
    color: #ffffff;
    background: #2b76e7;
}
.pending{
    color: #ffffff;
    background: #809FB8;
}
.done{
    color: #ffffff;
    background: #00B1B7;
}
.failed{
    color: #ffffff;
    background: #F55A08;
}
</style>