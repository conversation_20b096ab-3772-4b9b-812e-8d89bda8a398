<template>
  <MainLayout>
    <template #header> Plan Set</template>
    <template #page-actions>
      <DraftHeader
        :openPlanListComponent="openPlanListComponent"
        :filterData="filterData"
        :allDrafts="allDrafts"
        :openModal="openModal"
      >
      </DraftHeader
    ></template>
    <template #content>
      <NoData
        v-if="showNoDataScreen"
        primaryTitle="No Plan Sets found"
        title="Click 'Upload Set' to start."
      />
      <Grid v-else :columns="12">
        <NewPlanSetCard
          v-for="(item, index) in filteredDrafts"
          :key="index"
          :planName="item.pbt_set_sequence"
          planVersion="2"
          :planDate="formatDate(item.pbt_created_on)"
          :planTitle="item.psm_set_name"
          :planStatus="item.pbt_status"
          :planProgress="item.planProgress"
          :planUploadedBy="item.pbt_uploaded_by_name"
          :planUpdatedOn="formatDate(item.pbt_created_on)"
          :sheetCount="item.plan_sheet_count"
          :companyName="item.pbt_company_name"
          :progress="item.progress"
          :color="item.color"
          :prj_id="item.prj_id"
          :pbt_id="item.pbt_id"
          :psm_id="item.psm_id"
          @click.native="clickDraftCard(item.prj_id, item.pbt_id, item.psm_id)"
        />
      </Grid>
      <SheetUpload
        v-if="isModal"
        :prjId="prjId"
        :openPlansUploadComponent="openPlansUploadComponent"
        @closeModal="closeModal"
      />
    </template>
  </MainLayout>
</template>

<script>
import DraftHeader from "./DraftHeader.vue";
// import DraftCardItem from "./DraftCardItem.vue";
import NewPlanSetCard from "./NewPlanSetCard.vue";
import { mapGetters, mapActions } from "vuex";
import toLower from "lodash/toLower";
import includes from "lodash/includes";
import filter from "lodash/filter";
import forEach from "lodash/forEach";
import MainLayout from "@/components/MainLayout.vue";
import SheetUpload from "@/components/ImageDisplayComponents/PlanList/SheetUpload/index.vue"
import config from "../../../config";


export default {
  name: "PlanDraft",
  components: {
    DraftHeader,
    // DraftCardItem,
    SheetUpload,
    MainLayout,
    NewPlanSetCard,
  },
  props: {
    prjId: {
      type: String,
      required: true,
    },
    openPlansUploadComponent: {
      type: Function,
      required: true,
    },
    openPlanListComponent: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      batchIds: null,
      allDrafts: [],
      filteredDrafts: [],
      isModal: false,
    };
  },
  computed: {
    ...mapGetters({
      getBatchIdsbyPrjId: "PlanUploadSet/getBatchIdsbyPrjId",
      getBatchbyId: "PlanUploadBatch/getBatchbyId",
    }),
    showNoDataScreen() {
      return this.filteredDrafts.length === 0;
    },
  },
  async mounted() {
    await this.setProjectBatches({ prj_id: this.prjId, pcm_id: config.getProjectCompanyId() });
    this.batchIds = await this.getBatchIdsbyPrjId(this.prjId);

    forEach(this.batchIds, (eachId) => {
      let data = this.getBatchbyId(eachId);
      this.allDrafts.push(data);
    });
    this.filteredDrafts = this.allDrafts;
  },
  methods: {
    ...mapActions({
      setProjectBatches: "PlanUploadBatch/setProjectBatches",
      getDrafts: "PlanMarkup/getDrafts",
      setActiveBatch: "PlanUploadSet/setActiveBatch",
      setActiveSet: "PlanUploadSet/setActiveSet",
    }),
    formatDate(dateString) {
      // Parse the date string
      const date = this.$moment(dateString).format("MM/DD/YYYY");

      return date
},
    clickDraftCard(prj_id, pbt_id, psm_id) {
      this.setActiveBatch({ pbt_id: pbt_id });
      this.setActiveSet({ psm_id: psm_id });
      this.openPlansUploadComponent({
        prjId: prj_id,
        pbtId: pbt_id,
        psmId: psm_id,
      });
    },
    filterData(text) {
      let data = this.allDrafts;
      if (text.length)
        data = filter(this.allDrafts, (item) => {
          return (
            includes(toLower(item.psm_set_name), toLower(text)) ||
            includes(toLower(item.pbt_set_sequence), toLower(text))
          );
        });
      this.filteredDrafts = data;
    },
    openModal() {
      this.isModal = true;
    },
    closeModal() {
      this.isModal = false;
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
.drafts-container {
  .drafts-header {
    // height: 80px;
  }
}
</style>
