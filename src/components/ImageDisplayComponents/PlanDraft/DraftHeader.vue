<template>
  <div>
    <div class="plan-draft-view">
      <Grid :columns="12" :gap="2">
        <Box :columnSize="12">
          <template #body>
            <div class="draft-left-section">


              <p v-if="allDrafts.length" class="search-text">
                <Icon name="search" color="baseLight2" class="search-img" />

                <input
                  v-model="searchText"
                  type="text"
                  placeholder="Search by set name"
                  class="plan-search"
                  @input="searchDrafts"
                />
              </p>
              <div


                class="plan-upload-button"
                @click="openModal"
              >
                <Button v-if="!getreadOnly" label="Upload Set" type="primary" />
              </div>
            </div>
          </template>
        </Box>
      </Grid>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: "DraftHeader",
  props: {
    openPlanListComponent: {
      type: Function,
      required: true,
    },
    filterData: {
      type: Function,
      required: true,
    },
    allDrafts: {
      type: Array,
      required: true,
    },
    openModal: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      isList: false,
      isGrid: true,
      searchText: '',
    };
  },
  computed: {
    ...mapGetters({
      getreadOnly: 'PlanMarkup/getreadOnly',
    }),
  },
  methods: {
    toggle(event) {
      if (event === false) {
        this.isList = !this.isList;
        this.isGrid = !this.isGrid;
      }
    },
    searchDrafts() {
      this.filterData(this.searchText);
    },
    goBack() {
      this.openPlanListComponent();
    },
  },
};
</script>

<style lang="scss" scoped>
// .plan-left-section {
//   display: flex;
//   position: relative;
//   margin-top: 5px;
//   .search-img {
//     position: absolute;
//     left: 35px;
//     top: 25px;
//   }

//   .plan-search {
//     width: 300px;
//     height: 48px;
//     background: #ffffff;
//     // color: #adc1d1;
//     border-radius: 16px;
//     padding: 10px 60px;
//     margin: 10px;
//   }
// }
.drafts-header {
  font-weight: 700;
  font-size: 24px;
  line-height: 30px;
  color: #2b446c;
}
.draft-left-section {
  display: flex;
  margin-top: 5px;
  justify-content: space-between;
  align-items: center;
  padding: 0px 20px;

  // .back-button {
  //   cursor: pointer;
  //   width: 48px;
  //   height: 48px;
  //   margin: 10px;
  //   background: #ffffff;
  //   position: relative;
  //   border-radius: 5px;

  //   .back-img {
  //     position: absolute;
  //     left: 15px;
  //     top: 10px;
  //   }
  // }
  .search-text {
    position: relative;
    .search-img {
      position: absolute;
      left: 25px;
      top: 6px;
    }
  }

  .plan-search {
    width: 300px;
    height: 33px;
    background: #ffffff;
    border-radius: 16px;
    padding: 10px 60px;
  }
  .plan-upload-button {
    padding-left: 10px;
  }
}
</style>
