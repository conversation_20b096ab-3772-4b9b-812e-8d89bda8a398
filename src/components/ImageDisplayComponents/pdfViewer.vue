<template>
  <div id="pdf-viewer">
      <imageDisplay
        :plans="false"
        :type="'pdf'"
        :pdfSrc="pdfSrc"
        :goBack="goBack"
        :saveProp="saveProp"
      />
  </div>
</template>

<script>
import imageDisplay from '../imageDisplay/imageDisplay'

export default {
  name:"pdf-viewer",
  props:[
    "pdfSrc",
    "goBack",
    "saveProp"
  ],
  components:{
    imageDisplay
  },
  mounted(){
    // console.log("This is the pdf", this.pdfSrc)
  }
}
</script>

<style scoped>

</style>