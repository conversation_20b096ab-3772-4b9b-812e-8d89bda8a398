<template>
  <div id="markup-list">
    <MarkupListHeader v-if='false' @onFilter="onFilter"/>
    <MarkupListItem
      v-if="false"
      :onClickVersionContainerMarkup="onClickVersionContainerMarkup"
      @clearObjects="$emit('clearObjects')"
      @onMoreOptions="onMoreOptions"
    />
    <MarkupLayersList :canvasRefs="canvasRefs" :filterValue="filterValue" :sheetId='sheetId'
      @onDeleteElement="onDeleteElement" 
      @onChangeMarkup="onChangeMarkup"
    />
  </div>
</template>

<script>
import MarkupListHeader from "./MarkupListHeader.vue";
import MarkupListItem from "./MarkupListItem.vue";
import MarkupLayersList from "@/components/PlansNew1/Layers/MarkupList.vue";
export default {
  name: "MarkupList",
  components: {
    <PERSON>upListHeader,
    MarkupListItem,
    MarkupLayersList,
  },

  props: {
    onClickVersionContainerMarkup: {
      type: Function,
      default: () => {},
    },
    canvasRefs: {
      type: Object,
      default: () => {
        return {};
      },
    },
    sheetId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      filterValue: "all",
    };
  },
  methods: {
    onMoreOptions(data) {
      this.$emit("onMoreOptions", data);
    },
    onFilter(value) {
      this.filterValue = value;
    },
    onDeleteElement(elementId){
      this.$emit("onDeleteElement", elementId);
    },
    onChangeMarkup(){
      this.$emit("onChangeMarkup");
    }
  },
};
</script>

<style scoped>
#markup-list {
  border-top: 0.5px solid #e7edf5;
}
</style>
