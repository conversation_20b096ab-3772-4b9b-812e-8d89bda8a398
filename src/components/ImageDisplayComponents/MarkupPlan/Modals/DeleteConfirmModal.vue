<template>
  <div style="padding: 10px">
    <Modal :isFooter="false" :isHeader="false">
      <template #modal-body>
        <div class="plans-upload__publish__header">
          <div class="plans-upload__publish__header__title">Confirm Delete</div>
          <div @click="$emit('closeModal')">
            <Icon color="baseLight2" name="close" />
          </div>
        </div>
        <div class="plans-upload__publish__body">
          <div class="plans-upload__publish__body__text">
            <div class="plans-upload__publish__body__text__title">
              Are you sure ,You want to Delete ?
            </div>
            <!-- <div class="plans-upload__publish__body__text__subtitle">
              This action cannot be undone.
            </div> -->
          </div>
        </div>

        <div class="plans-upload__publish__footer">
          <div>
            <Button
              :size="'large'"
              label="Cancel"
              type="secondary"
              @onClick="$emit('closeModal')"
            />
          </div>
          <div>
            <Button
              :size="'large'"
              label="Delete"
              type="primary"
              @onClick="clickConfirmModal"
            />
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
export default {
  name: "DeleteConfirmModal",
  methods: {
    clickConfirmModal() {
      this.$emit("delete");
      this.$emit("closeModal");
    },
  },
};
</script>

<style lang="scss" scoped>
.plans-upload__publish__footer {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 100%;
  justify-content: space-evenly;
  padding: 30px 30px 0px 30px;
}
.plans-upload__publish__header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 30px;
  &__title {
    font-family: Lato;
    font-style: normal;
    font-weight: bold;
    font-size: 18px;
    line-height: 24px;
    display: flex;
    align-items: center;
    text-align: center;
    color: #000000;
  }
}
.plans-upload__publish__body {
  padding-top: 2%;
  &__text {
    &__title {
      font-family: Lato;
      font-style: normal;
      font-weight: normal;
      font-size: 14px;
      line-height: 18px;
      display: flex;
      align-items: center;
      text-align: center;
      color: #809fb8;
      padding-left: 10px;
      padding-left: 30px;
    }
    // &__subtitle {
    //   font-family: Lato;
    //   font-style: normal;
    //   font-weight: normal;
    //   font-size: 14px;
    //   line-height: 18px;
    //   display: flex;
    //   align-items: center;
    //   text-align: center;
    //   color: #000000;
    // }
  }
}
</style>
