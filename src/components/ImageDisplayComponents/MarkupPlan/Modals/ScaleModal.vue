<template>
  <Modal :isFooter="false" @closeModal="$emit('closeModal')">
    <template v-slot:modal-left> Enter the Scale in (ft/in) </template>
    <template v-slot:modal-body>
      <div id="scale-wrapper">
        <div class="scale-feet">
          <div class="label">Feet</div>
          <div class="scale-feet-input">
            <Input
              v-model="scaleObj.feet"
              :rules="{ min: 0 }"
              placeholder="Enters Feets here"
              :responsive="true"
              type="number"
            />
          </div>
        </div>
        <div class="scale-feet">
          <div class="label">Inches</div>
          <div class="scale-feet-input">
            <Input
              v-model="scaleObj.inches"
              :rules="{ min: 0, max: 11 }"
              placeholder="Enter Inches Here"
              :responsive="true"
              type="number"
            />
          </div>
        </div>
      </div>
      <div id="button-select" @click="doneScaling">
        <Button label="Submit" type="primary" size="large" />
      </div>
    </template>
  </Modal>
</template>

<script>
// import modalBase from '@/components/plansList/modals/modalBase.vue'
export default {
  components: {
    // modalBase,
  },
  data() {
    return {
      scaleObj: {
        feet: null,
        inches: null,
      },
    };
  },
  methods: {
    doneScaling() {
      const scaleObj = {
        feet: this.scaleObj.feet ? this.scaleObj.feet : 0,
        inches: this.scaleObj.inches ? this.scaleObj.inches : 0,
      };
      this.$emit("doneScaling", scaleObj);
    },
  },
};
</script>

<style lang="scss" scoped>
#scale-wrapper {
  padding: 15px;
}
#button-select {
  display: flex;
  justify-content: center;
  padding: 14px;
}
.scale-feet {
  display: flex;
  padding: 10px 0px;
  .label {
    flex: 1;
    margin-top: 8px;
  }
  .scale-feet-input {
    flex: 4;
  }
}
</style>
