<template>
  <Modal
    :isFooter="false"
    :maxHeightBody="500"
    @closeModal="$emit('closeModal')"
  >
    <template v-slot:modal-left> Create Access </template>
    <template v-slot:modal-body>
      <div id="markup-access">
        <div id="mp-access-label">Markup Access</div>
        <div id="mp-access-drop-down">
          <Dropdown
            placeholder="Select Access"
            :selectedValue="selectedAccess"
            responsive
            :options="['Restricted', 'Private']"
            size="medium"
            @change="OnSelected"
          />
        </div>
      </div>
      <div id="mp-table-body">
        <!-- <div v-if="showCompanyTable === true" id="mp-search-tag">
          <input v-model="search" placeholder="Company Name" responsive />
        </div>
        <div v-else id="mp-search-tag">
          <input v-model="search" placeholder="Person Name" responsive />
        </div> -->
        <div v-if="showCompanyTable === true">
          <div id="mp-table-header-top">
            <div class="mp-table-header" style="margin-left: 10px">
              <!-- <Checkbox label="" :checked="allChecked" :size="'small'" :textColor="'baseDark'" @change="allSelected" /> -->
              Company
            </div>
            <div class="mp-table-header">Company Role</div>
            <div class="mp-table-header">Project Manager</div>
          </div>
          <div id="mp-row-wrapper">
            <div
              v-for="(cmp, index) in filteredList"
              :key="index"
              class="mp-row"
            >
              <div class="mp-row-name" style="margin-left: 10px">
                <!-- eslint-disable-line -->
                <input
                  :id="index"
                  v-model="cmp.checked"
                  type="checkbox"
                  class="check-box"
                  :value="cmp"
                  @change="changed(cmp.pcm_id)"
                />
                {{ cmp.company_name }}
              </div>
              <div class="mp-row-role">{{ cmp.project_role }}</div>
              <div class="mp-row-pm">{{ cmp.contact_name }}</div>
            </div>
          </div>
        </div>
        <div v-else>
          <div id="mp-table-header-top">
            <div class="mp-table-header" style="margin-left: 10px">Name</div>
            <div class="mp-table-header">Role</div>
          </div>
          <div id="mp-row-wrapper">
            <div
              v-for="(emp, index) in secondFilteredList"
              :key="index"
              class="mp-row"
            >
              <div class="mp-row-name" style="margin-left: 10px">
                <!-- eslint-disable-line -->
                <input
                  :id="index"
                  v-model="emp.checked"
                  type="checkbox"
                  class="check-box"
                  :value="emp"
                  :disabled="emp.user_id === getOwner"
                  @change="updateSelected(emp.user_id)"
                />
                <span class="employee-name">{{ emp.full_name }}</span>
              </div>
              <div class="mp-row-role">{{ emp.emp_role_name }}</div>
            </div>
          </div>
        </div>
      </div>
      <div id="mp-access-btn">
        <Button
          :size="'large'"
          label="Select"
          type="secondary"
          @onClick="onClickSubmit"
        />
      </div>
    </template>
  </Modal>
</template>

<script>
// import Notifications from '@/constants/notification';
import map from "lodash/map";
import includes from "lodash/includes";
import filter from "lodash/filter";
import forEach from "lodash/forEach";
import { mapGetters, mapActions } from "vuex";

export default {
  name: "SetCompanyModal",
  components: {
    // Input,
    // Checkbox
  },
  props: {
    calledFrom: {
      type: String,
      default: "plan",
    },
    accessProp: {
      type: String,
      default: "Restricted",
    },
    currAccess: {
      type: String,
      default: "",
    },
    // clickedRow: {},
  },
  data() {
    return {
      search: "",
      allChecked: false,
      checked: true,
      singleselect: false,
      selectedCompanyList: [],
      selectedEmployeeList: [],
      contractorBusinessSubType: "",
      showCompanyTable: true,
      selectedAccess: this.accessProp,
      loadingAccess: false,
      selectedUserIds: null,
      diffArray: [],
      empDiffArray: [],
    };
  },
  computed: {
    ...mapGetters({
      getCompList: "PlanMarkup/getCompList",
      getCurrentAccess: "PlanMarkup/getCurrentAccess",
      getPlm: "PlanMarkup/getPlm",
      getCompanyDetails: "PlanMarkup/getCompanyDetails",
      getPcmIdArray: "PlanMarkup/getPcmIdArray",
      getUserIdArray: "PlanMarkup/getUserIdArray",
      getSelectedCmpIds: "PlanMarkup/getSelectedCmpIds",
      getSelectedUserIds: "PlanMarkup/getSelectedUserIds",
      getEmpList: "PlanMarkup/getEmpList",
      getOwner: "PlanMarkup/getOwner",
    }),
    filteredList() {
      let newArray = map(this.getCompList, (company) => {
        return {
          cmp_id: company.company.cmp_id,
          company_name: company.company.cmp_name,
          pcm_id: company.pcm_id,
          project_role: this.getCompanyDetails[company.company.cst_code],
          contact_name: `${company.employee.emp_first_name} ${company.employee.emp_last_name}`,
          checked: includes(this.getPcmIdArray, company.pcm_id),
          user_id: company.employee.user_id,
        };
      });
      //  company.employee.user_id === this.clickedRow.pfl_createdby,
      // this.updateSelectedArray(newArray);
      // console.log(newArray, 'this is the new array');
      // folderOwnerChecked:company.employee.user_id === this.clickedRow.pfl_createdby
      return newArray;
    },
    secondFilteredList() {
      let newArray = map(this.getEmpList, (item) => {
        return {
          user_id: item.user_id,
          emp_first_name: item.emp_first_name,
          emp_last_name: item.emp_last_name,
          emp_title: item.emp_title,
          emp_role_name: item.user_id__role_id_main__rol_name,
          checked: includes(this.getUserIdArray, item.user_id),
          full_name: item.full_name,
        };
      });
      // this.updateSelectedArray(newArray);
      return newArray;
    },
  },
  async mounted() {
    this.companies = this.getCompList;
    this.diffArray = [...this.getPcmIdArray];
    this.empDiffArray = [...this.getUserIdArray];
    if (this.accessProp === "Restricted") {
      this.showCompanyTable = true;
    } else {
      this.showCompanyTable = false;
    }
    // for(let i=0; i<this.getPcmIdArray; i++){
    //   this.selectedCompanyList.push(...this.getPcmIdArray[0]);
    // }

    // else{
    //do something
    // }
    // this.selectedUserIds = this.getAclData.selectedUserIds;
    // this.selectedCmpIds = this.getAclData.selectedCmpIds;
  },
  methods: {
    ...mapActions({
      getCompanyBasic: "PlanMarkup/getCompanyBasic",
      deleteMarkupAccess: "PlanMarkup/deleteMarkupAccess",
      postCompanyAccess: "PlanMarkup/postCompanyAccess",
      editMarkup: "PlanMarkup/editMarkup",
      getMarkupAcl: "PlanMarkup/getMarkupAcl",
      editMarkupTable: "PlanMarkup/editMarkupTable",
    }),
    onClickSubmit() {
      if (this.showCompanyTable) {
        this.postSelectedCompaniesAccess();
      } else {
        this.postSelectedPeopleAccess();
      }
    },
    updateSelected(val) {
      var index = -1;
      if (includes(this.getUserIdArray, val)) {
        if (includes(this.empDiffArray, val)) {
          index = this.empDiffArray.indexOf(val);
          if (index > -1) {
            this.empDiffArray.splice(index, 1);
          }
        } else {
          this.empDiffArray.push(val);
        }
      } else {
        if (includes(this.selectedEmployeeList, val)) {
          index = this.selectedEmployeeList.indexOf(val);
          if (index > -1) {
            this.selectedEmployeeList.splice(index, 1);
          }
        } else {
          // if(includes(this.getPcmIdArray, val)){
          this.selectedEmployeeList.push(val);
          // }
        }
      }
    },
    changed(val) {
      var index = -1;
      if (includes(this.getPcmIdArray, val)) {
        if (includes(this.diffArray, val)) {
          index = this.diffArray.indexOf(val);
          if (index > -1) {
            this.diffArray.splice(index, 1);
          }
        } else {
          this.diffArray.push(val);
        }
      } else {
        if (includes(this.selectedCompanyList, val)) {
          index = this.selectedCompanyList.indexOf(val);
          if (index > -1) {
            this.selectedCompanyList.splice(index, 1);
          }
        } else {
          // if(includes(this.getPcmIdArray, val)){
          this.selectedCompanyList.push(val);
          // }
        }
      }
    },
    OnSelected(val) {
      this.selectedAccess = val;
      if (val === "Restricted") {
        this.showCompanyTable = true;
      } else {
        this.showCompanyTable = false;
      }
    },
    handleSubmit() {
      let selectedCmpId = [];
      for (let i = 0; i < this.selectedList.length; i++) {
        selectedCmpId.push(this.selectedList[i].pcm_id);
      }
      if (this.selectedList.length) {
        this.$emit("selectedComp", selectedCmpId);
      } else {
        // this.$toast.error(Notifications.toast_MSG.REQUIRED);
      }
    },
    updateSelectedArray(newArray) {
      this.selectedList = filter(
        newArray,
        (company) => company.checked === true
      );
    },
    async fetchProjectCompanies() {
      // get the list of companies in selectCompany modal
      let result = await this.getCompanyList();
      this.companies = result.data.Active_Companies;
    },
    async postSelectedPeopleAccess() {
      let res = -1;
      forEach(this.empDiffArray, (ele) => {
        this.selectedEmployeeList.push(ele);
      });
      let users = this.selectedEmployeeList;
      this.loading = true;
      let payloadArr = [];
      for (let i = 0; i < this.getSelectedUserIds.length; i++) {
        if (!includes(users, this.getSelectedUserIds[i].user_id)) {
          res = await this.deleteMarkupAccess(
            this.getSelectedUserIds[i].macl_id
          );
          if (res.status === 204) {
            this.$toast.success("Markup Access Updated");
          }
        }
      }
      for (let i = 0; i < users.length; i++) {
        const userId = users[i];
        if (!includes(this.userIdArray, userId)) {
          let payload = {
            // acl_permission: this.permission.viewer,
            plm_id: this.getPlm,
            user_id: userId,
            macl_permission: "CONTRIBUTOR",
          };
          payloadArr.push(payload);
        }
      }
      if (payloadArr.length) {
        if (this.getCurrAccess() === "PR") {
          res = await this.postCompanyAccess(payloadArr);
          if (res.status === 201) {
            this.$toast.success("Markup Access Updated");
          }
        } else {
          try {
            if (this.calledFrom === "plan") {
              await this.editMarkup({
                plm_access: "PR",
                macl_permission: "OWNER",
              });
            } else {
              const data = await this.editMarkupTable({
                plm_access: "PR",
                macl_permission: "OWNER",
              });
              this.$emit("accessChangePrivate", data);
            }
            res = await this.postCompanyAccess(payloadArr);
            if (res.status === 201) {
              this.$toast.success("Markup Access Updated");
            }
          } catch (error) {
            this.$toast.error("Some Error Occured");
          }
        }
      }
      // this.closeSetPeopleModal();
      this.$emit("closeModal");
      this.loading = false;
    },
    async postSelectedCompaniesAccess() {
      let res = -1;
      forEach(this.diffArray, (ele) => {
        this.selectedCompanyList.push(ele);
      });
      // console.log(this.selectedCompanyList, this.diffArray)

      let companies = this.selectedCompanyList;
      // console.log(this.pcmIdArray, 'dkjfkjs');
      this.loadingAccess = true;
      let payloadArr = [];

      for (let i = 0; i < this.getSelectedCmpIds.length; i++) {
        if (!includes(companies, this.getSelectedCmpIds[i].pcm_id)) {
          res = await this.deleteMarkupAccess(
            this.getSelectedCmpIds[i].macl_id
          );
          if (res.status === 204) {
            this.$toast.success("Markup Access Updated");
          }
        }
      }
      for (let i = 0; i < companies.length; i++) {
        if (!includes(this.getPcmIdArray, companies[i])) {
          let payload = {
            // acl_permission: this.permission.viewer,
            plm_id: this.getPlm,
            pcm_id: companies[i],
            macl_permission: "CONTRIBUTOR",
          };
          payloadArr.push(payload);
        }
      }
      if (payloadArr.length) {
        if (this.getCurrAccess() === "RE") {
          res = await this.postCompanyAccess(payloadArr);
          // console.log(res, 'res');
          if (res.status === 201) {
            this.$toast.success("Markup Access Updated");
          }
        } else {
          try {
            if (this.calledFrom === "plan") {
              await this.editMarkup({
                plm_access: "RE",
              });
              res = await this.postCompanyAccess(payloadArr);
              if (res.status === 201) {
                this.$toast.success("Markup Access Updated");
              }
            } else {
              const data = await this.editMarkupTable({
                plm_access: "RE",
              });
              this.$emit("accessChangeRestricted", data);
              res = await this.postCompanyAccess(payloadArr);
              if (res.status === 201) {
                this.$toast.success("Markup Access Updated");
              }
            }
          } catch (error) {
            // this.$toast.error(Notifications.toast_MSG.ERROR);
          }
        }

        // await this.getFolderList();
      }
      // await this.getMarkupAcl();
      this.$emit("closeModal");
      this.loadingAccess = false;
    },
    getCurrAccess() {
      if (this.currAccess === "") {
        return this.getCurrentAccess;
      } else this.currAccess;
    },
  },
};
</script>

<style lang="scss" scoped>
#markup-access {
  display: flex;
  align-items: center;
  padding: 10px 0px;
}
#mp-access-label {
  margin-left: 15px;
  margin-right: 10px;
}
#mp-access-drop-down {
  width: 360px;
}
#mp-search-tag {
  padding: 15px 10px;
}
#mp-table-body {
  background-color: white;
  height: 352px;
  padding-top: 10px;
}
#mp-table-header-top {
  display: flex;
  padding-bottom: 10px;
  border-bottom: 0.5px solid #adc1d1;
  font-style: normal;
  font-weight: 400;
  font-size: 10px;
  line-height: 14px;
  color: #06152b;
  div {
    flex: 1;
  }
}
.mp-row {
  display: flex;
  align-items: center;
  height: 36px;
  border-bottom: 0.5px solid #adc1d1;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: #06152b;
  div {
    flex: 1;
  }
}
#mp-row-wrapper {
  overflow: auto;
  height: 254px;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
#mp-row-wrapper::-webkit-scrollbar {
  display: none;
}

#mp-access-btn {
  padding: 14px;
  display: flex;
  justify-content: center;
}
.employee-name {
  padding-left: 5px;
}
</style>
