<template>
  <div>
    <Modal @closeModal="$emit('closeModal')" @onClickButton="onClickButton">
      <template v-slot:modal-left> Publish </template>
      <template v-slot:modal-body>
        <div id="share-body">
          <div id="markup-name-label">Markup Name</div>
          <div id="mp-name">
            <Input
              placeholder="Enter Markup Name"
              :rules="{ required: true }"
              v-model="value"
              responsive
            />
          </div>
          <!-- <div id="mp-access" class="mp-flex">
                        <div id="mp-access-label" class="mp-publish-label">Select who can access</div>
                        <div id="mp-access-input">
                            <Input placeholder="Enter Name" size="large" responsive/>
                        </div>
                        <div id="mp-access-dp">
                            <Dropdown placeholder="Can edit" responsive :options='["Can view","Can edit"]' size="large"/>
                        </div>
                    </div> -->
          <!-- <div id="mp-category" class="mp-flex">
                        <div id="mp-category-label" class="mp-publish-label">Select Markup Category</div>
                        <div id="mp-category-input">
                            <Dropdown placeholder="Select" responsive :options='["MP", "RFI","CHO","PL"]' size="large"/>
                        </div>
                    </div> -->
          <!-- <div id="mp-availability" class="mp-flex">
                        <div id="mp-availability-label" class="mp-publish-label">Select available</div>
                        <div id="mp-availability-private" class="mp-flex">
                            <div id="mp-availability-private-toggle">
                                <Toggle :state="false"/>
                            </div>
                            <div id="mp-availability-private-label" class="toggle-label">Private</div>
                        </div>
                        <div id="mp-availability-public" class="mp-flex">
                            <div id="mp-availability-public-toggle">
                                <Toggle :state="false"/>
                            </div>
                            <div id="mp-availability-public-label" class="toggle-label">public</div>
                        </div>
                    </div>     -->
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
// import { Modal } from "@development/linarc-design-components";
// import InviteUserDiv from '../UtilsComponents/InviteUserDiv.vue';
// import ShareUserCell from '../UtilsComponents/ShareUserCell.vue';
export default {
  name: "ShareModal",
  components: {
    // Modal,
    // Dropdown,
    // Toggle,
  },
  props: {
    mpName: {
      type: String,
      default: "",
    },
    selectCategory: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      canSelectCategory: this.selectCategory,
      value: this.mpName,
    };
  },
  methods: {
    onClickButton() {
      this.$emit("closeModal");
      this.$emit("done", this.value);
      console.log("is this happening");
    },
  },
};
</script>

<style lang="scss" scoped>
#share-body {
  display: flex;
  margin: 32px 10px 10px 16px;
  align-items: center;
}
#markup-name-label {
  width: 134px;
  margin-bottom: 8px;
}
.mp-publish-label {
  font-style: normal;
  font-weight: 500;
  font-size: 10px;
  line-height: 30px;
  width: 106px;
}
#mp-name {
  width: 454px;
}
.mp-flex {
  display: flex;
  align-items: center;
}
#mp-access {
  padding-top: 20px;
}
#mp-access-label {
  margin-right: 20px;
}
#mp-access-input {
  width: 210px;
}
#mp-access-dp {
  width: 110px;
  margin-left: auto;
}
#mp-category {
  padding-top: 20px;
}
#mp-category-label {
  margin-right: 20px;
}
#mp-category-input {
  width: 202px;
}
#mp-availability {
  padding-top: 20px;
}
#mp-availability-label {
  margin-right: 20px;
}
#mp-availability-private {
  margin-right: 44px;
}
.toggle-label {
  margin-bottom: 5px;
  margin-left: 10px;
  font-style: normal;
  font-weight: 500;
  font-size: 10px;
  line-height: 30px;
  color: #809fbb;
}
</style>
