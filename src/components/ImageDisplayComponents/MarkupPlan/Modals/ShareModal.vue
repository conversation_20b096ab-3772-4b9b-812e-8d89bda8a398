<template>
  <Modal :isFooter="false" @closeModal="$emit('closeModal')">
    <template v-slot:modal-left> Select Markup Access </template>
    <template #modal-body>
      <div class="card-item no-background">
        <div id="delete-text">
          <div class="delete-folder-main-div">
            <form @submit.prevent="() => false">
              <div class="delete-form" style="padding: 10px 0px">
                <div class="access-box-wrapper">
                  <div id="access-box">
                    <div>
                      <input
                        id="input-mp"
                        v-model="access"
                        type="radio"
                        value="PB"
                      />
                      <label for="PB">Public</label>
                    </div>
                    <div style="margin-left: 30px">
                      ( Anyone in this project can view this Markup)
                    </div>
                  </div>
                </div>
                <div class="access-box-wrapper">
                  <div id="access-box">
                    <div>
                      <input
                        id="input-mp"
                        v-model="access"
                        type="radio"
                        value="RE"
                      />
                      <label for="RE">Restricted</label>
                    </div>
                    <div style="margin-left: 30px">
                      ( Only selected companies can view this Markup )
                    </div>
                  </div>
                </div>
                <div class="access-box-wrapper" style="border-bottom: 0px">
                  <div id="access-box">
                    <div>
                      <input
                        id="input-mp"
                        v-model="access"
                        type="radio"
                        value="PR"
                      />
                      <label for="PR">Private</label>
                    </div>
                    <div style="margin-left: 30px">
                      ( Only selected employees can view this Markup )
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
        <div class="card-row">
          <div class="button-select">
            <Button
              :size="'large'"
              label="Select"
              type="secondary"
              @onClick="clickSelect"
            />
          </div>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script>
// import modalBase from '@/components/layout/modalBase.vue';
// import AppConstants from '@/constants';

export default {
  name: "ShareModal",
  props: {
    mpAccess: {
      type: String,
      default: "PR",
    },
  },
  data() {
    return {
      access: this.mpAccess,
      modalWidth: 440,
    };
  },
  computed: {},
  methods: {
    clickSelect() {
      this.$emit("accessSelected", this.access);
      this.$emit("closeModal");
    },
  },
};
</script>

<style lang="scss" scoped>
#input-mp {
  margin-right: 15px;
}
.access-box-wrapper {
  border-bottom: 1px solid #e7edf5;
}
#access-box {
  margin: 5px 20px 20px 20px;
  font-family: Lato;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 17px;
}
#delete-text {
  font-size: 18px;
  box-shadow: 0px 0px 40px 20px rgb(0 0 0 / 12%);
  border-radius: 15px;
  margin: 10px 14px 72px 14px;
}
.card-item {
  cursor: default !important;
  &:hover {
    background: none !important;
  }
}
.no-background {
  &:hover {
    background-color: none !important;
  }
  &:focus {
    border: none;
  }
}
.card-row {
  display: flex;
  align-items: center;
  justify-items: center;
  padding: 14px;
  margin-left: 174px;
}
</style>
