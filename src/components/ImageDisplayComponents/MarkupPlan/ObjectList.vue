<template>
    <div id="object-list">
        <ObjectListItem
            v-bind="$attrs"
            @checked="$emit('checked')"
            @unChecked="$emit('unChecked')"
        />
    </div>
</template>

<script>
import ObjectListItem from './ObjectListItem.vue';
export default {
    name: 'ObjectList',
    components: {
        ObjectListItem,
    },
}
</script>

<style scoped>
#object-list{
    padding: 1px;
}
</style>