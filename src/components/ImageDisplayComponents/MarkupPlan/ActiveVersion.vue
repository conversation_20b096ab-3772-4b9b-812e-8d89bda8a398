<template>
  <div id="active-version-wrapper">
    <div id="active-version">
      <div id="av-icon-check">
        <Icon size="tiny" name="checkmark" color="baseLight2" />
      </div>
      <div id="av-text">{{ getActiveVersion }}</div>
      <div v-if="!getreadOnly" id="av-actions">
        <div v-show="false" id="av-icon-lock">
          <Icon size="large" name="unlock" color="secondary" />
        </div>
        <div
          v-if="getAllVersion.length > 1"
          id="av-icon3-delete"
          v-role-access="{
            name: 'plans-plansSheets-markup-version-delete-icon',
            service: 'plans-plansSheets-markup',
            type: 'action',
            access: 'delete',
          }"
          @click="deletePlanSheetVersion(getSelectedVersion.plf_id)"
        >
          <Icon size="huge" name="trash" color="secondary" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import get from "lodash/get";
export default {
  name: "ActiveVersion",
  props: {
    deletePlanSheetVersion: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      // activeVersion: 'v1',
    };
  },
  computed: {
    ...mapGetters({
      getSheetVersion: "PlanMarkup/getSheetVersion",
      getreadOnly: "PlanMarkup/getreadOnly",
      getAllVersion: "PlanMarkup/getAllVersion",
    }),
    getActiveVersion() {
      return get(this.getSheetVersion, "name");
    },
  },
  // mounted() {
  //     this.activeVersion = this.getSelectedVersion;
  // }
};
</script>

<style scoped>
#active-version-wrapper {
  margin: 3px 0px 4px 0px;
  height: 44px;
}
#active-version {
  height: 100%;
  display: flex;
  align-items: center;
  background-color: #f6faff;
}
#av-icon-check {
  margin-left: 12px;
  cursor: pointer;
}
#av-text {
  margin-left: 12px;
  font-style: normal;
  font-weight: 300;
  font-size: 12px;
  line-height: 18px;
  color: #000000;
}
#av-actions {
  margin-left: auto;
  margin-right: 17px;
}
#av-icon-lock {
  display: inline-block;
  margin-left: 11px;
  margin-right: 11px;
  margin-bottom: 5px;
  cursor: pointer;
}
#av-icon3-delete {
  display: inline-block;
  cursor: pointer;
}
</style>
