<template>
  <div id="markup-list-header-wrapper">
    <div id="markup-list-header">
      <div id="header-text">Markups</div>
      <div id="m-list-filter">
        <div id="filter-text">Show :</div>
        <div id="active-filter">
          <Dropdown
            placeholder="All"
            :border="false"
            responsive
            :options="[
              'All',
              'PLAN',
              'RFI',
              'Punch List',
              'Change Order',
              'Submittal',
            ]"
            size="tiny"
            @change="OnSelected"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from "vuex";
export default {
  name: "MarkupListHeader",
  components: {
    // Icon,
  },
  data() {
    return {
      typeMap: {
        PLAN: "plansfile",
        RFI: "rfiquestions",
      },
    };
  },
  methods: {
    ...mapActions({
      setFilter: "PlanMarkup/setFilter",
    }),
    OnSelected(val) {
      this.setFilter(val);
      this.$emit("onFilter", this.typeMap[val] || "all");
    },
  },
};
</script>

<style scoped>
#markup-list-header-wrapper {
  height: 30px;
  border-bottom: 0.5px solid #e7edf5;
  padding-top: 6px;
}
#markup-list-header {
  display: flex;
  align-items: center;
  font-style: normal;
  font-weight: normal;
  font-size: 10px;
  line-height: 18px;
  color: #000000;
}
#header-text {
  margin-left: 12px;
}
#m-list-filter {
  display: flex;
  margin-left: auto;
  margin-right: 5px;
  align-items: center;
  cursor: pointer;
  font-style: normal;
  font-weight: normal;
  font-size: 10px;
  line-height: 18px;
  color: #809fb8;
}
#active-filter {
  margin-left: 5px;
  width: 100px;
}
</style>
