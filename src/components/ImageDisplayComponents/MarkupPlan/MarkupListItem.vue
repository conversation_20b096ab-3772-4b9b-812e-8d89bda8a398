<template>
  <div id="markup-list-item">
    <div v-for="(mp, i) in getFilterMarkupList" :key="i">
      <MarkupObject
        :mName="mp.plm_name"
        :mIndex="i"
        :plmId="mp.plm_id"
        :objType="mp.app_model_name"
        @onMoreOptions="onMoreOptions"
        @toggleLayer="toggleLayer(mp.plm_id)"
        @checked="checked(mp.plm_id)"
        @unChecked="unChecked(mp.plm_id)"
      />
      <div v-if="mp.expanded">
        <div
          v-for="(layer, ind) in markupIdsList(mp.plm_id)"
          :key="ind"
          @click="onSelectMarkup(mp.plm_id, layer)"
        >
          <div :style="getMarkerItemStyle(layer)">
            <ObjectList
              :key="ind"
              :layer="layer"
              @checked="layerChecked(ind, mp.plm_id)"
              @unChecked="layerUnChecked(ind, mp.plm_id)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MarkupObject from "./MarkupObject.vue";
import ObjectList from "./ObjectList.vue";
import { mapActions, mapGetters, mapState } from "vuex";
import get from "lodash/get";

export default {
  name: "MarkupListItem",
  components: {
    MarkupObject,
    ObjectList,
  },
  props: {
    onClickVersionContainerMarkup: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      expandedLayers: [],
    };
  },
  computed: {
    ...mapGetters({
      getFilterMarkupList: "PlanMarkup/getFilterMarkupList",
      getActivePlmIdsLength: "PlanMarkup/getActivePlmIdsLength",
      getMarkupsFromId: "PlanMarkup/getMarkupsFromId",
      isMarkupExpand: "PlanMarkup/isMarkupExpand",
      getSelectedMarkupId: "PlanMarkup/getSelectedMarkupId",
    }),
    ...mapState({
      markupIds: (state) => state.PlanMarkup.markups.byId,
    }),
    markupIdsList() {
      return (plmId) => {
        return get(this.markupIds[plmId], "plm_markup", []);
      };
    },
  },
  methods: {
    ...mapActions({
      setMarkupExpand: "PlanMarkup/setMarkupExpand",
      checkMarkup: "PlanMarkup/checkMarkup",
      unCheckMarkup: "PlanMarkup/unCheckMarkup",
      checkLayer: "PlanMarkup/checkLayer",
      unCheckLayer: "PlanMarkup/unCheckLayer",
      setSelectedMarkup: "PlanMarkup/setSelectedMarkup",
    }),
    onMoreOptions(data) {
      let obj = {};
      obj.yPos = data.yPos;
      // console.log(this.getFilterMarkupList[data.index], "khkjdsfalksn;la", data);
      obj.plmId = this.getFilterMarkupList[data.index].plm_id;
      this.$emit("onMoreOptions", obj);
    },
    toggleLayer(id) {
      this.setMarkupExpand(id);
      // this.$set(this.mList, i, arr);
    },
    checked(id) {
      this.checkMarkup(id);
    },
    unChecked(id) {
      this.unCheckMarkup(id);
      if (!this.getActivePlmIdsLength) {
        this.$emit("clearObjects");
      }
    },
    layerChecked(ind, id) {
      this.checkLayer({ index: ind, plm_id: id });
    },
    layerUnChecked(ind, id) {
      this.unCheckLayer({ index: ind, plm_id: id });
    },
    onSelectMarkup(plm_id, plm_markup) {
      const data = {
        plm_id: plm_id,
        plm_markup: plm_markup,
      };
      this.setSelectedMarkup(data);
      this.onClickVersionContainerMarkup(data);
    },
    getMarkerItemStyle(layer) {
      return {
        cursor: "pointer",
        backgroundColor:
          get(layer, "data.id", undefined) === this.getSelectedMarkupId
            ? "#e7edf5"
            : "white",
      };
    },
  },
};
</script>

<style scoped>
#markup-list-item {
  padding-top: 6px;
  padding-bottom: 6px;
  overflow: auto;
}
#markup-list-item::-webkit-scrollbar {
  display: none;
}

#markup-list-item {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
