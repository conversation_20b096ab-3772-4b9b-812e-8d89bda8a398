<template>
  <div id="toolbox-container-wrapper">
    <div id="toolbox-container">
      <div
        v-for="(t, i) in tools"
        :key="i"
        @click="tooClicked($event, t.popoverName)"
      >
        <div
          id="one-tool"
        >
          <Tool :toolData="t" :selected="t.selected" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Tool from "./Tool.vue";
import forEach from "lodash/forEach";
import includes from "lodash/includes";
export default {
  name: "ToolboxContainer",
  components: {
    Tool,
  },
  data() {
    return {
      tools: [
        // {
        //   name: "Hand",
        //   designName: "hand",
        //   popoverName: "hand",
        //   size: "small",
        //   color: "secondary",
        //   custom: false,
        //   selected: false,
        //   accessManageName: "plans-plansSheets-markup-hand-tool",
        //   accessManageService: "plans-plansSheets-markup",
        //   accessManageType: "action",
        //   accessManageAccess: "write",
        // },
        // {
        //   name: "Select",
        //   designName: "navigation2",
        //   popoverName: "select",
        //   size: "small",
        //   color: "secondary",
        //   custom: false,
        //   selected: false,
        //   accessManageName: "plans-plansSheets-markup-select-tool",
        //   accessManageService: "plans-plansSheets-markup",
        //   accessManageType: "action",
        //   accessManageAccess: "write",
        // },
        {
          name: "Image",
          designName: "imageIcon",
          popoverName: "image",
          size: "small",
          color: "secondary",
          custom: false,
          selected: false,
          accessManageName: "plans-plansSheets-markup-image-tool",
          accessManageService: "plans-plansSheets-markup",
          accessManageType: "action",
          accessManageAccess: "write",
        },
        {
          name: "Location",
          designName: "pin",
          popoverName: "location",
          size: "small",
          color: "secondary",
          custom: false,
          selected: false,
          accessManageName: "plans-plansSheets-markup-image-tool",
          accessManageService: "plans-plansSheets-markup",
          accessManageType: "action",
          accessManageAccess: "write",
        },
        {
          name: "Measurement",
          designName: "measure",
          popoverName: "measure",
          size: "small",
          color: "secondary",
          custom: false,
          selected: false,
          accessManageName: "plans-plansSheets-markup-measurement-tool",
          accessManageService: "plans-plansSheets-markup",
          accessManageType: "action",
          accessManageAccess: "write",
        },
        {
          name: "Polygon",
          designName: "newPolygon",
          popoverName: "polygon",
          size: "small",
          color: "secondary",
          custom: false,
          selected: false,
          accessManageName: "plans-plansSheets-markup-measurement-tool",
          accessManageService: "plans-plansSheets-markup",
          accessManageType: "action",
          accessManageAccess: "write",
        },

        {
          name: "Pen",
          size: "small",
          designName: "editNew",
          popoverName: "pen",
          color: "secondary",
          custom: false,
          selected: false,
          accessManageName: "plans-plansSheets-markup-pen-tool",
          accessManageService: "plans-plansSheets-markup",
          accessManageType: "action",
          accessManageAccess: "write",
        },
        {
          name: "Highlighter",
          size: "small",
          designName: "highlighter",
          popoverName: "highlighter",
          color: "secondary",
          custom: false,
          selected: false,
          accessManageName: "plans-plansSheets-markup-highlighter-tool",
          accessManageService: "plans-plansSheets-markup",
          accessManageType: "action",
          accessManageAccess: "write",
        },
        {
          name: "Text",
          size: "small",
          designName: "textIcon",
          popoverName: "text",
          color: "secondary",
          custom: false,
          selected: false,
          accessManageName: "plans-plansSheets-markup-text-tool",
          accessManageService: "plans-plansSheets-markup",
          accessManageType: "action",
          accessManageAccess: "write",
        },
        {
          name: "Cloud",
          size: "small",
          designName: "cloudMarkup",
          popoverName: "cloud",
          color: "secondary",
          custom: false,
          selected: false,
          accessManageName: "plans-plansSheets-markup-cloud-tool",
          accessManageService: "plans-plansSheets-markup",
          accessManageType: "action",
          accessManageAccess: "write",
        },
        {
          name: "Lines",
          size: "small",
          designName: "diagonalArrowRightUp",
          popoverName: "arrow",
          color: "secondary",
          custom: false,
          selected: false,
          accessManageName: "plans-plansSheets-markup-lines-tool",
          accessManageService: "plans-plansSheets-markup",
          accessManageType: "action",
          accessManageAccess: "write",
        },
        {
          name: "Shape",
          size: "small",
          designName: "square",
          popoverName: "shape",
          color: "secondary",
          custom: false,
          selected: false,
          accessManageName: "plans-plansSheets-markup-shape-tool",
          accessManageService: "plans-plansSheets-markup",
          accessManageType: "action",
          accessManageAccess: "write",
        },      
        {
          name: "Fill",
          custom: true,
          popoverName: "fill",
          color: "secondary",
          selected: false,
          accessManageName: "plans-plansSheets-markup-fill-tool",
          accessManageService: "plans-plansSheets-markup",
          accessManageType: "action",
          accessManageAccess: "write",
        },
        {
          name: "Stroke",
          size: "small",
          color: "secondary",
          popoverName: "stroke",
          custom: true,
          selected: false,
          accessManageName: "plans-plansSheets-markup-stroke-tool",
          accessManageService: "plans-plansSheets-markup",
          accessManageType: "action",
          accessManageAccess: "write",
        },
        // {
        //   name: "Comments",
        //   size: "small",
        //   designName: "messageSquare",
        //   color: "secondary",
        //   custom: false,
        //   selected: false,
        // },
      ],
    };
  },
  methods: {
    tooClicked(event, val) {
      this.$emit("toolClicked", {
        val,
        yPos: event.clientY,
      });
      this.highLightSelectedTool(val);
    },
    highLightSelectedTool(val) {
      //if the selected tol is stroke or fill then we need not to hightlight
      if (this.hightLightExemptTools(val)) {
        return;
      }
      forEach(this.tools, (tool) => {
        if (tool.popoverName === val) {
          tool.selected = true;
        } else {
          tool.selected = false;
        }
      });
    },
    hightLightExemptTools(val) {
      const tools = ["fill", "stroke"];
      return includes(tools, val);
    },
  },
};
</script>

<style scoped>
#toolbox-container-wrapper {
  position: absolute;
  top: 160px;
  max-height: 65%;
  width: 96px;
  background-color: #ffffff;
  right: 25px;
  border-radius: 10px;
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.06);
  overflow: auto;
}
#toolbox-container-wrapper::-webkit-scrollbar {
  display: none;
}

#toolbox-container-wrapper {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
#toolbox-container {
  /* margin-top: 20px; */
}
#one-tool {
  /* height: 55px; */
  /* padding-top: 5px; */
}
</style>
