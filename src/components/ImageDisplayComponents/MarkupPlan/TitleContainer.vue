<template>
  <div id="title-container">
    <div v-show="false" id="icon-back" @click="$emit('backClicked')">
      <Icon size="big" name="arrowBackOutline" color="secondary" />
    </div>
    <div id="sheet-name">
      <p>
        {{ getTitle }}
      </p>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "TitleContainer",
  props: {
    sheetVersion: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      // title: this.sheetCode + ' - ' + this.sheetVersion,
    };
  },
  computed: {
    ...mapGetters({
      getSheetSequenceArray: "PlanMarkup/getSheetSequenceArray",
    }),
    getTitle() {
      let sheetNo =
        this.getSheetSequenceArray[this.getSheetSequenceArray.length - 1];
      return sheetNo + " - " + this.sheetVersion;
    },
  },
  mounted() {},
};
</script>

<style scoped lang="scss">
#title-container {
  // position: absolute;
  // display: flex;
  // align-items: center;
  // top: 25px;
  // margin-left: 10px;
  // width: 250px;
  // height: 64px;
  // background-color: #ffffff;
  // border-radius: 10px;
}
#icon-back {
  margin-left: 23px;
}
// #sheet-name {
//   width: 100%;
//   font-style: normal;
//   font-weight: 700;
//   font-size: 24px;
//   line-height: 30px;
//   color: #2b446c;
//   margin-left: 5px;
//   margin-right: 5px;
//   text-align: center;
//   p {
//     overflow: hidden;
//     white-space: nowrap;
//     text-overflow: ellipsis;
//     max-width: 193px;
//   }
// }
</style>
