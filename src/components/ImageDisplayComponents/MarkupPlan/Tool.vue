<template>
  <div
    v-show="disableTool"
    id="tool"
    :style="backgroundColor"
    class="markup-tool"
    :title="tdata.name"
  >
    <div v-if="!tdata.custom" id="actual-icon" class="markup-tool-single">
      <Icon :size="tdata.size" :name="tdata.designName" color="secondary" />
      <div v-if="showHoverIcon(tdata.name)" class="markup-tool-hover">
        <Icon size="small" name="arrowLeft" color="baseLight" />
      </div>
    </div>
    <div v-else id="actual-icon" class="markup-tool-single">
      <div v-if="tdata.name === 'Fill'" class="fill-icon-border">
        <div id="fill-icon" :style="fillColor">
          <!-- <div class="markup-tool-hover">
          <Icon size="small" name="arrowLeft" color="baseLight" />
        </div> -->
        </div>
      </div>

      <div
        v-else-if="tdata.name === 'Stroke'"
        id="stroke-icon"
        :style="strokeColor"
        class="markup-tool-single"
      >
        <div id="stroke-inner">
          <!-- <div class="markup-tool-hover">
            <Icon size="small" name="arrowLeft" color="baseLight" />
          </div> -->
        </div>
      </div>
    </div>
    <!-- <div id="text-tool">
      {{ tdata.name }}
    </div> -->
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import includes from "lodash/includes";

export default {
  name: "Tool",
  props: {
    toolData: {
      type: Object,
      required: true,
    },
    selected: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tdata: this.toolData,
      subMenuIcons: ["Shape", "Lines"],
      toolsWithFill: ["square", "circle", "triangle"],
    };
  },
  computed: {
    ...mapGetters({
      getStroke: "PlanMarkup/getStroke",
      getFill: "PlanMarkup/getFill",
      getSelectedMarkupTool: "PlanMarkup/getSelectedMarkupTool",
    }),
    strokeColor() {
      return `background-color: ${this.getStroke.color};`;
    },
    fillColor() {
      return `background-color: ${this.getFill.color};opacity:${
        this.getFill.hardness / 100
      };`;
    },
    backgroundColor() {
      return this.selected ? "background-color: #E7EDF5;" : "";
    },
    disableTool() {
      // if (this.tdata.popoverName === "fill")
      //   return includes(this.toolsWithFill, this.getSelectedMarkupTool);
      return true;
    },
  },
  mounted() {},
  methods: {
    showHoverIcon(tool) {
      return includes(this.subMenuIcons, tool);
    },
  },
};
</script>

<style scoped lang="scss">
#tool {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  cursor: pointer;
}
#text-tool {
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  color: #2b446ccc;
  margin-top: 4px;
}
.fill-icon-border {
  border: 3px solid black;
  border-radius: 50%;
}
#fill-icon {
  height: 24px;
  width: 24px;
  background-color: #f9c150;
  border-radius: 15px;
}
#stroke-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 30px;
  background-color: #fe9200;
  border-radius: 15px;
}
#stroke-inner {
  height: 18px;
  width: 18px;
  background-color: #ffffff;
  border-radius: 10px;
}
.markup-tool {
  padding: 10px 0px;
}
.markup-tool-hover {
  position: absolute;
  left: -25px;
  top: 2px;
}
.markup-tool-single {
  position: relative;
}
</style>
