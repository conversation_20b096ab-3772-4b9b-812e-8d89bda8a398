<template>
  <div id="flex-color">
    <!-- <div class="one-color">
            <Icon size="small" name="slash" color="baseDark4" />
        </div> -->
    <div
      v-for="(color, i) in colors"
      id="one-color"
      :key="i"
      :style="`background-color:${color.colorHex};`"
      @click="$emit('colorPicked', color.colorHex)"
    ></div>
    <div id="all-color" @click="$emit('toggleColorPicker')"></div>
  </div>
</template>

<script>
// import { Icon } from '@development/linarc-design-components';
export default {
  name: "ColorPalette",
  components: {
    // Icon,
  },
  data() {
    return {
      colors: [
        {
          colorHex: "#FE9200",
          colorName: "",
        },
        {
          colorHex: "#2B76E7",
          colorName: "",
        },
        {
          colorHex: "#F55A08",
          colorName: "",
        },
        {
          colorHex: "#00B1B7",
          colorName: "",
        },
        {
          colorHex: "#F9C150",
          colorName: "",
        },
        {
          colorHex: "#2B446C",
          colorName: "",
        },
        {
          colorHex: "#000000",
          colorName: "",
        },
        {
          colorHex: "#ffffff",
          colorName: "",
        },
      ],
    };
  },
};
</script>

<style scoped lang="scss">
#flex-color {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
#one-color {
  height: 20px;
  width: 20px;
  border-radius: 10px;
  border: 1px solid #f7f7f7;
  cursor: pointer;
}
.one-color {
  cursor: pointer;
}
#all-color {
  height: 20px;
  width: 20px;
  border-radius: 10px;
  border: 1px solid #f7f7f7;
  cursor: pointer;
  background: conic-gradient(
    from 180deg at 50% 50%,
    #ff3838 0deg,
    rgba(255, 219, 30, 0.804458) 68.52deg,
    rgba(57, 255, 25, 0.580496) 128.52deg,
    rgba(99, 255, 255, 0.94) 194.15deg,
    rgba(27, 64, 255, 0.65) 254.15deg,
    rgba(170, 32, 255, 0.76) 308.52deg,
    #ff3e38 360deg
  );
}
</style>
