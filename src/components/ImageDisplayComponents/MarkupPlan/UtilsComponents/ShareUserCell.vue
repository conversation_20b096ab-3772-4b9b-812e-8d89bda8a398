<template>
  <div id="one-cell">
    <div id="avatar">
      <Avatar size="small" backgroundColor="warning" fullName=" akshay" />
    </div>
    <div id="name">{{ userData.name }}</div>
    <div id="dropdown">
      <Dropdown
        placeholder="Can edit"
        :border="false"
        responsive
        :options="['Can view', 'Can edit']"
        size="small"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "ShareUserCell",
  props: {
    user: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      userData: this.user,
    };
  },
};
</script>

<style lang="scss" scoped>
#one-cell {
  display: flex;
  align-items: center;
  height: 30px;
}
#avatar {
  margin-right: 14px;
}
#dropdown {
  width: 92px;
  margin-left: auto;
  margin-right: -13px;
}
</style>
