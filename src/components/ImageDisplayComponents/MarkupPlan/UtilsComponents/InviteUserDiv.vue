<template>
  <div id="send-invite">
    <div id="send-invite-category">
      <Input placeholder="Enter mail id" size="large" responsive />
    </div>
    <div id="send-invite-category-dp">
      <Dropdown
        placeholder="Can view"
        :options="['Can view', 'Can edit']"
        size="large"
        responsive
      />
    </div>
    <div id="send-invite-button">
      <Button type="success" label="Send Invite" size="large" />
    </div>
  </div>
</template>

<script>
export default {
  name: "InviteUserDiv",
  data() {
    return {
      users: [
        {
          name: "<PERSON><PERSON><PERSON>",
          canView: "all",
        },
        {
          name: "<PERSON><PERSON><PERSON>",
          canView: "all",
        },
        {
          name: "<PERSON><PERSON><PERSON>",
          canView: "all",
        },
        {
          name: "<PERSON><PERSON><PERSON>",
          canView: "all",
        },
        {
          name: "Akshay",
          canView: "all",
        },
        {
          name: "<PERSON><PERSON><PERSON>",
          canView: "all",
        },
        {
          name: "<PERSON><PERSON><PERSON>",
          canView: "all",
        },
        {
          name: "<PERSON><PERSON><PERSON>",
          canView: "all",
        },
        {
          name: "<PERSON><PERSON><PERSON>",
          canView: "all",
        },
        {
          name: "<PERSON><PERSON><PERSON>",
          canView: "all",
        },
        {
          name: "Akshay",
          canView: "all",
        },
        {
          name: "Akshay",
          canView: "all",
        },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
#send-invite {
  display: flex;
  align-items: center;
  padding-top: 15px;
  padding-bottom: 20px;
}
#send-invite-category {
  width: 200px;
  margin-right: 7px;
}
#send-invite-category-dp {
  width: 100px;
  margin-right: 7px;
}
#send-invite-button {
  margin-left: auto;
  margin-right: 0px;
}
</style>
