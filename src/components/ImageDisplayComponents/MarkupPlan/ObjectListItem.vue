<template>
  <div id="object-list-item-wrapper">
    <div id="object-list-item">
      <div id="ol-check-box">
        <input
          id="input-check"
          type="checkbox"
          :checked="showChecked"
          @change="checkClicked()"
        />
        <label>{{ theLayer.type }}</label>
      </div>
      <!-- <div id="ol-icons">
            <div id="icon-eye">
                <Icon size="large" name="unlock" color="secondary" />
            </div>
            <div id="icon-delete">
                <Icon size="huge" name="trash" color="secondary" />
            </div>
        </div> -->
    </div>
  </div>
</template>

<script>
// import { Icon } from '@development/linarc-design-components';
export default {
  name: "ObjectListItem",
  components: {
    // Icon
  },
  props: {
    layer: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      theLayer: this.layer,
      some: true,
      showChecked: this.layer.checked,
    };
  },
  watch: {
    layer: {
      handler(val) {
        this.theLayer = val;
        this.showChecked = val.checked;
      },
      deep: true,
    },
  },
  methods: {
    checkClicked() {
      if (this.showChecked === true) {
        this.$emit("unChecked");
        this.showChecked = false;
      } else {
        this.$emit("checked");
        this.showChecked = true;
      }
    },
  },
};
</script>

<style scoped>
#object-list-item {
  height: 34px;
  display: flex;
  align-items: center;
  margin-left: 65px;
}
#ol-icons {
  margin-left: auto;
  margin-right: 17px;
}
#icon-eye {
  display: inline-block;
  margin-left: 11px;
  margin-right: 11px;
  margin-bottom: 5px;
  cursor: pointer;
}
#icon-delete {
  display: inline-block;
  cursor: pointer;
}
#input-check {
  margin-right: 10px;
}
#ol-check-box {
  display: flex;
  align-items: center;
}
</style>
