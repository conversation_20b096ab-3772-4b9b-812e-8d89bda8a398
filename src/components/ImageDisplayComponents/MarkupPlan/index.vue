<template>
  <MainLayout>
    <template #header>
      <div v-if="loading" class="img-loader">
        <PulseLoader :loading="true" color="#f57947"></PulseLoader>
      </div>
      <TitleContainer
        v-if="type !== 'imageViewer'"
        :sheetVersion="sheetVersion"
        @backClicked="backClicked"
      />
    </template>
    <template #page-actions>
      <ActionContainer
        v-if="showAction"
        :type="type"
        @publish="publish"
        @share="share"
      />
    </template>
    <template #content>
      <div id="mp-container">
        <image-display
          v-if="showPlan === true && type === 'imageViewer'"
          ref="imageDisplay"
          type="image"
          :plans="false"
          :showNav="false"
          :imageMeta="imageMeta.obj"
          :goBack="goBack"
          :saveProp="imageMarkupSave"
        />
        <div v-if="getSheetUrl" class="plan-markup-canvas">
          <Tutorial
            ref="plans-sheet-canvas"
            :sheetUrl="getSheetUrl"
            :objMarkup="objMarkup"
            @showScaleModal="showScale = true"
          />
        </div>
        <imageDisplay
          v-if="showPlan === true && type !== 'imageViewer' && false"
          ref="imageDisplay"
          :plans="true"
          :showNav="false"
          :sheetVersion="sheetVersion"
          :sheetCode="getSheetNo"
          :all_data="plansData"
          :sheetDataProp="sheetDataProp"
          :markUpVersionData="getMarkupPropData"
          :goBack="goBack"
          :onVersionClicked="onVersionClicked"
          :onSheetClicked="onSheetClicked"
          :base_url="baseURL"
          :saveProp="onSavePlan"
          :switchProps="switchProps"
          :onUpload="onUpload"
          :cameraAttachment="cameraAttachment"
          :markupTitle="markupTitle"
          :projectMembers="projectMembers"
          :planState="planState"
        />
        <AddMarkupModal
          v-if="showSaveModal"
          @close="showSaveModal = false"
          @done="addMarkup"
        />

        <ObjectTag v-if="objMarkup.objType !== 'plansfile' && false" />
        <VersionContainer
          v-if="showPlan === true && type !== 'imageViewer'"
          :currentVersion="'v1'"
          :versions="{}"
          :canvasRefs="getPlansCanvasRefs"
          :onClickVersionContainerMarkup="onClickVersionContainerMarkup"
          :markupLists="markupsList"
          :deletePlanSheetVersion="showPlanSheetVersionDeleteModal"
          @clearObjects="clearAllObjects"
          @onMoreOptions="onMoreOptions"
          @toggleListPopover="toggleListPopover"
        />

        <ToolboxContainer
          v-if="showToolBar && !getreadOnly"
          v-role-access="{
            name: 'plans-plansSheets-markup-toolbar',
            service: 'plans-plansSheets-markup',
            type: 'container',
            access: 'write',
          }"
          @toolClicked="toolClicked"
        />
        <PublishModal
          v-if="showPublishModal"
          @closeModal="showPublishModal = false"
          @done="addMarkup"
        />
        <ShareModal
          v-if="showShareModal"
          :mpAccess="getCurrentAccess"
          @closeModal="showShareModal = false"
          @accessSelected="accessSelected"
        />
        <SetCompanyModal
          v-if="showSetCompanyModal === true"
          :accessProp="accessProp"
          :compList="compList"
          @closeModal="showSetCompanyModal = false"
        />
        <ShapePopover
          v-if="showShapePopover"
          :yPos="yPos"
          @shapeClicked="shapeClicked"
          @closeModal="showShapePopover = false"
        />
        <LinePopover
          v-if="showLinePopover"
          :yPos="yPos"
          @shapeClicked="shapeClicked"
          @closeModal="showLinePopover = false"
        />
        <VersionListPopover
          v-if="showVersionListPopover"
          @closeModal="showVersionListPopover = false"
          @versionChanged="versionChanged"
        />
        <TextToolPopover
          v-if="showTextToolPopover"
          :yPos="yPos"
          @closeModal="textPropSelected"
        />
        <StrokeFillPopover
          v-if="showStrokeFillPopover"
          :isStroke="isStroke"
          :yPos="yPos"
          :setFillAndStroke="setFillAndStroke"
          @closeModal="showStrokeFillPopover = false"
          @colorPicked="colorPicked"
          @hardnessChanged="hardnessChanged"
          @versionChanged="versionChanged"
        />

        <UpdateConfirmModal
          v-if="showUpdateConfirmModal === true"
          @closeModal="showUpdateConfirmModal = false"
          @update="addMarkup()"
        />
        <MoreMenu
          v-if="showMorePopover === true"
          :yPos="yPos"
          @delete="deleteMarkup"
          @rename="renameMarkup"
          @closeModal="showMorePopover = false"
        />
        <DeleteConfirmModal
          v-if="showDeleteConfirmModel === true"
          @delete="ConfirmDeleteMarkup"
          @closeModal="showDeleteConfirmModel = false"
        />
        <DeleteConfirmModal
          v-if="showDeletePlanSheetVersion"
          @delete="deletePlanSheetVersion"
          @closeModal="closePlanSheetVersionDeleteModal"
        />
        <ScaleModal
          v-if="showScale"
          @doneScaling="doneScaling"
          @closeModal="showScale = false"
        />
      </div>
    </template>
  </MainLayout>
</template>

<script>
import MainLayout from "@/components/MainLayout";
import { mapActions, mapGetters } from "vuex";
import imageDisplay from "@/components/imageDisplay/imageDisplay";
import TitleContainer from "./TitleContainer.vue";
import VersionContainer from "./VersionContainer.vue";
import ActionContainer from "./ActionContainer.vue";
import ToolboxContainer from "./ToolboxContainer.vue";
import ShareModal from "./Modals/ShareModal.vue";
import PublishModal from "./Modals/PublishModal.vue";
import ShapePopover from "./Popover/ShapePopover.vue";
import LinePopover from "./Popover/LinePopover.vue";
import VersionListPopover from "./Popover/VersionListPopover.vue";
import TextToolPopover from "./Popover/TextToolPopover.vue";
import StrokeFillPopover from "./Popover/StrokeFillPopover.vue";
import AddMarkupModal from "@/components/plansList/modals/addMarkupModal.vue";
import ObjectTag from "./ObjectTag.vue";
import PulseLoader from "vue-spinner/src/PulseLoader";
import config from "/src/config";
import forEach from "lodash/forEach";
import keys from "lodash/keys";
import UpdateConfirmModal from "./Modals/UpdateConfirmModal.vue";
import SetCompanyModal from "./Modals/SetCompanyModal.vue";
import MoreMenu from "./Popover/MoreMenu.vue";
import DeleteConfirmModal from "./Modals/DeleteConfirmModal.vue";
import { Axios, urls } from "/src/utils/Axios";
import get from "lodash/get";
import filter from "lodash/filter";
import find from "lodash/find";
import Tutorial from "@/components/PlansNew1/PlansCanvas";
import ScaleModal from "@/components/ImageDisplayComponents/MarkupPlan/Modals/ScaleModal.vue";

export default {
  name: "MarkupPlan",
  components: {
    imageDisplay,
    TitleContainer,
    VersionContainer,
    ActionContainer,
    MainLayout,
    ToolboxContainer,
    ShareModal,
    PublishModal,
    ShapePopover,
    LinePopover,
    VersionListPopover,
    TextToolPopover,
    StrokeFillPopover,
    AddMarkupModal,
    ObjectTag,
    UpdateConfirmModal,
    SetCompanyModal,
    MoreMenu,
    DeleteConfirmModal,
    PulseLoader,
    Tutorial,
    ScaleModal,
  },
  props: {
    objIdCheck: {
      type: Function,
      default: () => {
        return () => {};
      },
      require: true,
    },
    imageMeta: {
      type: Object,
      default: () => {
        return { obj: null };
      },
    },
    type: {
      type: String,
      default: "",
    },
    baseURL: {
      type: String,
      require: true,
      default: "",
    },
    objMarkup: {
      type: Object,
      default: null,
      require: true,
    },
    sheetDataProp: {
      type: Object,
      default: () => {
        return {};
      },
    },
    onVersionClicked: {
      type: String,
      require: false,
      default: "",
    },
    onUpload: {
      type: Function,
      require: false,
      default: () => {},
    },
    showNav: {
      type: Boolean,
      default: true,
    },
    projectMembers: {
      type: Object,
      default: () => ({}),
    },
    plmId: {
      type: String,
      default: "",
    },
    changeMarkupVersion: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      imageData: {
        imageSrc: null,
        thumbSrc: null,
      },
      loading: false,
      yPos: 100,
      compList: [],
      accessProp: "",
      isStroke: true,
      deletePlm: "",
      showDeleteConfirmModel: false,
      showMorePopover: false,
      showSetCompanyModal: false,
      showUpdateConfirmModal: false,
      showStrokeFillPopover: false,
      showTextToolPopover: false,
      showVersionListPopover: false,
      showShapePopover: false,
      showLinePopover: false,
      showPlan: false,
      plansData: {},
      showSaveModal: false,
      allMarkupData: [],
      markupArray: [],
      markupTitle: null,
      objId: 1,
      objName: "changeorder",
      activeMarkupId: "",
      markupsList: [],
      showVersionList: false,
      activeMarkups: [],
      indexedLayerData: [],
      mIndex: -1,
      planState: "new",
      markupData: this.objMarkup,
      sheetNumber: this.objMarkup.sheetNumber,
      showPublishModal: false,
      showShareModal: false,
      cameraAttachment: null,
      ver: ["v1"],
      showDeletePlanSheetVersion: false,
      deletingPlfId: "",
      showScale: false,
    };
  },
  computed: {
    ...mapGetters({
      getSelectedVersion: "PlanMarkup/getSelectedVersion",
      getMarkupPropData: "PlanMarkup/getMarkupPropData",
      getMode: "PlanMarkup/getMode",
      getPlanMarkupPropData: "PlanMarkup/getPlanMarkupPropData",
      getSheetSequenceArray: "PlanMarkup/getSheetSequenceArray",
      getPlfId: "PlanMarkup/getPlfId",
      getCompList: "PlanMarkup/getCompList",
      getCurrentAccess: "PlanMarkup/getCurrentAccess",
      getAllVersion: "PlanMarkup/getAllVersion",
      getreadOnly: "PlanMarkup/getreadOnly",
      getCurrentSheetData: "PlanMarkup/getCurrentSheetData",
      getModifiedMarkupJson: "MarkupCanvas/getModifiedMarkupJson",
      getCurrentSheetUrl: "PlanMarkup/getSheetUrl",
      getSheetVersion: "PlanMarkup/getSheetVersion",
    }),
    getPlansCanvasRefs() {
      return this.$refs["plans-sheet-canvas"];
    },
    getSheetUrl() {
      // const prjId=this.objMarkup.prj_id;
      // const sheetNumber=this.objMarkup.sheetNumber;

      return this.getCurrentSheetUrl(
        this.objMarkup.prjId,
        this.objMarkup.sheetNumber
      );
    },
    sheetVersion() {
      const version = this.getSheetVersion(
        this.objMarkup.prjId,
        this.objMarkup.sheetNumber
      );
      return get(version, "name", "");
      //return this.getSelectedVersion;
    },
    showToolBar() {
      return this.getMode === "create";
    },
    showAction() {
      return this.getMode === "update" || this.getMode === "create";
    },
    getSheetNo() {
      if (this.getSheetSequenceArray.length) {
        return this.getSheetSequenceArray[
          this.getSheetSequenceArray.length - 1
        ];
      }
      return "";
    },
  },
  async mounted() {
    // reseting he state data so that opening of markup and plan stabalize
    await this.resetState();
    await this.setAllPlanData(this.objMarkup.prjId);
    //adding the sheet no in the sheet sequence  array to keep track of hyper link
    await this.addSheetSequence(this.objMarkup.sheetNumber);

    //setting up the markup prop data
    await this.setPlanMarkupPropData(this.objMarkup);
    this.SelectVersion(this.objMarkup.plfId);
    //setting sheetversion

    // setting the mode if comming from plan mode is create and if markup mode is update
    if (this.objMarkup.plmId) {
      await this.setMarkupMode("update");
    }

    // calling plan api to get the sheet data
    if (this.type !== "imageViewer") {
      await this.getPlanData();
      await this.getMarkupListData();

      (this.cameraAttachment = {
        token: config.accessToken,
        objName: this.markupData.objName,
        prj_id: this.markupData.prjId,
        url: config.baseURL,
      }),
        (this.plansData = await this.getPlansData());
      this.getCompanyBasic();
    }
    this.showPlan = true;
    this.initializeKeyBoardEvents();
  },
  methods: {
    ...mapActions({
      getMarkupListData: "PlanMarkup/getMarkupListData",
      getPlanData: "PlanMarkup/getPlanData",
      getAllPlanData: "PlanMarkup/getAllPlanData",
      setPlanMarkupPropData: "PlanMarkup/setPlanMarkupPropData",
      changeDraft: "PlanMarkup/changeDraft",
      updateMarkup: "PlanMarkup/updateMarkup",
      addSheetSequence: "PlanMarkup/addSheetSequence",
      removeTopSheet: "PlanMarkup/removeTopSheet",
      editMarkup: "PlanMarkup/editMarkup",
      getCompanyList: "PlanMarkup/getCompanyList",
      getCompanyBasic: "PlanMarkup/getCompanyBasic",
      getMarkupAcl: "PlanMarkup/getMarkupAcl",
      getEmplyeeList: "PlanMarkup/getEmplyeeList",
      deletePlmMarkup: "PlanMarkup/deletePlmMarkup",
      undoPlmMarkup: "PlanMarkup/undoPlmMarkup",
      redoPlmMarkup: "PlanMarkup/redoPlmMarkup",
      resetState: "PlanMarkup/resetState",
      setMarkupMode: "PlanMarkup/setMarkupMode",
      setSelectedVersion: "PlanMarkup/setSelectedVersion",
      setAllPlanData: "PlanMarkup/setAllPlanData",
      deletePlansFile: "PlanMarkup/deletePlansFileVersion",
      setSelectedMarkupTool: "PlanMarkup/setSelectedMarkupTool",
    }),
    colorPicked(color) {
      this.$refs["plans-sheet-canvas"].setColor(color);
    },
    hardnessChanged(hardness) {
      this.$refs["plans-sheet-canvas"].setOpacity(hardness);
    },
    async imageMarkupSave(obj) {
      (this.imageData.imageSrc = obj),
        (this.imageData.thumbSrc = obj),
        await this.onSaveImage(this.imageData, this.imageMeta.imgName);
    },
    renameMarkup() {
      return;
    },
    setFillAndStroke() {
      this.$refs.imageDisplay.setFillAndStroke();
    },
    doneScaling(scale) {
      this.$refs["plans-sheet-canvas"].caliberateRuler(scale);
      this.showScale = false;
    },
    async ConfirmDeleteMarkup() {
      const res = await this.deletePlmMarkup(this.deletePlm);
      if (res) {
        this.clearAllObjects();
        this.$toast.success("Markup Deleted Successfully");
      }
    },
    onMoreOptions(val) {
      this.yPos = val.yPos;
      this.deletePlm = val.plmId;
      this.showMorePopover = true;
    },
    deleteMarkup() {
      this.showMorePopover = false;
      this.showDeleteConfirmModel = true;
    },
    share() {
      this.showShareModal = true;
      this.getMarkupAcl();
      this.getCompanyList();
      this.getEmplyeeList();
    },
    async accessSelected(access) {
      // here i will call the api so access could work
      // this.getMarkupAcl();
      this.showShareModal = false;
      // this.getCompanyList();
      // await this.getMarkupAcl(this.markupArray[this.activeIndex].plmId);
      if (access === "PR") {
        this.accessProp = "Private";
        this.showSetCompanyModal = true;
      } else if (access === "RE") {
        this.compList = this.getCompList;
        this.accessProp = "Restricted";
        this.showSetCompanyModal = true;
      } else if (access === "PB") {
        // $axios.put(`${resource}/${prj_id}/markup/edit/${plm_id}/`, body);
        const res = await this.editMarkup({
          plm_access: "PB",
        });
        if (res.status === 200) {
          this.$toast.success("Markup Access Updated");
        }
      }
    },
    clearAllObjects() {
      this.$refs.imageDisplay.cleanObjectsOnEmpty();
    },
    async publish() {
      // console.log("publish");
      // if (!this.objMarkup.objId) await this.objIdCheck();
      // this.$refs.imageDisplay.onSave();
      this.onSavePlan();
    },
    shapeClicked(val) {
      if (val === "square") {
        //this.$refs.imageDisplay.putRectOnCanvas();
        this.$refs["plans-sheet-canvas"].setTool("rect");
      } else if (val === "circle") {
        //this.$refs.imageDisplay.putCircleOnCanvas();
        this.$refs["plans-sheet-canvas"].setTool("ellipse");
      } else if (val === "triangle") {
        //this.$refs.imageDisplay.putTriangleOnCanvas();
        this.$refs["plans-sheet-canvas"].setTool("triangle");
      } else if (val === "arrow") {
        //this.$refs.imageDisplay.putArrowOnCanvas();
        this.$refs["plans-sheet-canvas"].setTool("arrow");
      } else if (val === "line") {
        // this.$refs.imageDisplay.putLineOnCanvas();
        this.$refs["plans-sheet-canvas"].setTool("line");
      } else return "";
      this.showShapePopover = false;
      this.showLinePopover = false;
      this.setSelectedMarkupTool(val);
    },
    textPropSelected() {
      this.showTextToolPopover = false;
      this.$refs.imageDisplay.putTextOnCanvas();
    },
    versionChanged(version) {
      //Call Below two Functions when using component individualy
      // this.setSelectedVersion(version)
      // this.sheetVersion = version.name
      this.changeMarkupVersion(version.plf_id, this.getSheetNo);

      // console.log('version has been changed');
    },
    toggleListPopover() {
      this.showVersionListPopover = !this.showVersionListPopover;
    },
    onClickVersionContainerMarkup(data) {
      this.$refs.imageDisplay.onClickVersionContainerMarkup(data);
    },
    toolClicked(tool) {
      // this.$refs.imageDisplay.disableSelect();
      if (tool.val === "shape") {
        this.yPos = tool.yPos;
        this.showShapePopover = true;
      } else if (tool.val === "line") {
        this.showLinePopover = true;
      } else if (tool.val === "measure") {
        // this.$refs.imageDisplay.createRulerInCanvas();
        // this.setSelectedMarkupTool(tool.val);
        this.$refs["plans-sheet-canvas"].setTool("ruler");
      } else if (tool.val === "text") {
        // this.yPos = tool.yPos;
        // this.showTextToolPopover = true;
        // this.setSelectedMarkupTool(tool.val);
        this.$refs["plans-sheet-canvas"].setTool("text");
      } else if (tool.val === "stroke") {
        this.yPos = tool.yPos;
        this.isStroke = true;
        this.showStrokeFillPopover = true;
      } else if (tool.val === "fill") {
        this.yPos = tool.yPos;
        this.isStroke = false;
        this.showStrokeFillPopover = true;
      } else if (tool.val === "image") {
        // this.$refs.imageDisplay.addImageToCanvas();
        // this.setSelectedMarkupTool(tool.val);
        // this.addImage();
        this.$refs["plans-sheet-canvas"].setTool("camera");
      } else if (tool.val === "pen") {
        // this.$refs.imageDisplay.freeHandDraw(2);
        // this.setSelectedMarkupTool(tool.val);
        this.$refs["plans-sheet-canvas"].setTool("pencil");
      } else if (tool.val === "highlighter") {
        // this.$refs.imageDisplay.freeHandHighlighter(8);
        // this.setSelectedMarkupTool(tool.val);
        this.$refs["plans-sheet-canvas"].setTool("highlighter");
      } else if (tool.val === "arrow") {
        this.yPos = tool.yPos;
        this.showLinePopover = true;
        //this.$refs.imageDisplay.putArrowOnCanvas();
      } else if (tool.val === "select") {
        this.$refs.imageDisplay.offDrawMode();
        this.setSelectedMarkupTool(tool.val);
      } else if (tool.val === "hand") {
        this.$refs.imageDisplay.allowHandMode();
        this.setSelectedMarkupTool(tool.val);
      } else if (tool.val === "cloud") {
        this.$refs["plans-sheet-canvas"].setTool("cloud");
      }
    },
    addImage() {
      return;
    },
    //Adding markup name from input modal
    async addMarkup(markupName) {
      // console.log(markupName, 'name is game');
      this.showUpdateConfirmModal = false;
      if (markupName) this.markupData.plm_name = markupName;
      let plfId = this.getPlfId;
      forEach(keys(this.markupData), (key) => {
        this.formData.set(key, this.markupData[key]);
      });
      if (this.getMode === "create") {
        await Axios.post(urls.addMarkup(plfId), this.markupData);
        // if (res.status === 200) {
          // if (this.objMarkup.objType !== "plansfile")
          //   this.onSaveImage(this.planImageAttachment, markupName);
          // else {
          //   this.$toast.success("Markup Uploaded Successfully");
          // }
        // }
        this.$toast.success("Markup Uploaded Successfully");
        // // console.log(res);
        // this.changeDraft(res.data);
      } else if (this.getMode === "update") {
        this.updateMarkup(this.formData);
      }
    },

    async onSaveImage(obj, name) {
      this.loading = true; //getting object if not present
      const attachment = {
        file_name: name,
        model_name: this.objMarkup.objType,
        object_id: this.objMarkup.objId,
        file_type: "image",
      };
      const response = await Axios.post(urls.preSignedPost(), attachment);

      const storeS3File = {
        key: response.data.fields.key,
        AWSAccessKeyId: response.data.fields.AWSAccessKeyId,
        policy: response.data.fields.policy,
        signature: response.data.fields.signature,
      };
      const formData = new FormData();
      forEach(keys(storeS3File), (key) => {
        formData.set(key, storeS3File[key]);
      });

      const storeThumbFile = {
        key: response.data.thumbnail_fields.key,
        AWSAccessKeyId: response.data.thumbnail_fields.AWSAccessKeyId,
        policy: response.data.thumbnail_fields.policy,
        signature: response.data.thumbnail_fields.signature,
      };
      const formDataThumbnail = new FormData();
      forEach(keys(storeThumbFile), (key) => {
        formDataThumbnail.set(key, storeThumbFile[key]);
      });
      formData.append("file", obj.imageSrc);
      formDataThumbnail.append("file", obj.thumbSrc);
      const p1 = await this.$axios.post(response.data.url, formData);
      const p2 = await this.$axios.post(response.data.url, formDataThumbnail);
      Promise.all([p1, p2]).then(() => {
        this.$toast.success("Image Uploaded Successfully");
        this.$emit("goBack");
      });
    },

    async switchProps(sheetNumber) {
      this.addSheetSequence(sheetNumber);
      await this.getPlanData(sheetNumber);
      await this.getMarkupListData(sheetNumber);
      this.clearAllObjects();
    },
    async backClicked() {
      if (this.getSheetSequenceArray.length === 1) {
        this.removeTopSheet();
        this.$emit("goBack");
      } else {
        this.removeTopSheet();
        this.clearAllObjects();
        let sheetNo =
          this.getSheetSequenceArray[this.getSheetSequenceArray - 2];
        await this.getPlanData(sheetNo);
        await this.getMarkupListData(sheetNo);
      }
    },
    async onSavePlan() {
      this.sheetCode = "A.8.2";
      if (!this.objId)
        //getting object if not present
        await this.objIdCheck();
      const markUp = {
        normal: [],
        compound_elements: [],
        ...this.getModifiedMarkupJson,
      };
      this.markupData = {
        plm_sheet_version: "V1",
        plm_createdby: config.userId,
        obj_id: this.objMarkup.objId,
        obj_type: this.objMarkup.objType,
        pcm_id: this.objMarkup.pcmId,
        plm_markup: markUp,
      };

      const formData = new FormData();

      // formData.append("plm_markup",
      //   JSON.stringify(markUp)
      // );
      //formData.append("plm_thumbnail", obj.thumbSrc);

      this.formData = formData;
      if (this.getMode === "create") this.showPublishModal = true;
      else if (this.getMode === "update") this.showUpdateConfirmModal = true;
      this.planImageAttachment = await this.$refs[
        "plans-sheet-canvas"
      ].canvasToBlob();
    },
    onSheetClicked(sheet, version = null) {
      this.objMarkup.sheetCode = sheet;
      this.objMarkup.sheetVersion =
        version !== null ? version : this.getCurrentVersion(sheet);
      this.focusSheet = sheet;
    },
    async goBack() {
      return;
    },
    async getPlansData() {
      const responseData = await Axios.get(
        urls.getAllFileIds(this.objMarkup.prjId)
      );
      if (responseData.status === 200) {
        return responseData.data;
      }
      // else console.log('Error loading plans data');
    },
    initializeKeyBoardEvents() {
      document.addEventListener("keydown", (e) => {
        if ((e.ctrlKey || e.metaKey) && (e.key === "z" || e.key === "Z")) {
          this.undoPlmMarkup("draft101");
          this.$refs.imageDisplay.undoObjects();
        }
      });
      document.addEventListener("keydown", (e) => {
        if ((e.ctrlKey || e.metaKey) && (e.key === "y" || e.key === "Y")) {
          this.redoPlmMarkup("draft101");
          this.$refs.imageDisplay.redoObjects();
        }
      });
    },
    SelectVersion(plf_id) {
      const version = find(this.getAllVersion, (version) => {
        return version.plf_id === plf_id;
      });
      if (version) {
        this.setSelectedVersion(version);
      }
    },
    async deletePlanSheetVersion() {
      try {
        if (this.getAllVersion.length > 1) {
          const newVersions = filter(
            this.getAllVersion,
            (version) => version.plf_id !== this.deletingPlfId
          );
          const newVersion = newVersions[newVersions.length - 1];

          await this.deletePlansFile(this.deletingPlfId);
          //  this.setSelectedVersion(this.getAllVersion[0]);

          this.changeMarkupVersion(get(newVersion, `plf_id`), this.getSheetNo);
        }
        //   await this.deletePlansFile(this.deletingPlfId);
        // //  this.setSelectedVersion(this.getAllVersion[0]);
        //     this.changeMarkupVersion(this.deletingPlfId, this.getSheetNo);
      } catch (e) {
        return "";
      }
      this.showDeletePlanSheetVersion = false;
    },
    showPlanSheetVersionDeleteModal(plf_id) {
      this.showDeletePlanSheetVersion = true;
      this.deletingPlfId = plf_id;
    },
    closePlanSheetVersionDeleteModal() {
      this.showDeletePlanSheetVersion = false;
    },
  },
};
</script>

<style scoped>
#mp-container {
  position: relative;
  height: 100%;
  width: 100%;
  background-color: #f1f4f9;
}
.img-loader {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 50;
}
.plan-markup-canvas {
  width: 100%;
  height: 100%;
}
</style>
