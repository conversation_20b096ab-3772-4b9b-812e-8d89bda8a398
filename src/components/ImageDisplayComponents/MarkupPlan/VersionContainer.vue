<template>
  <div>
    <div v-show="showVersionContainer">
      <div id="version-container">
        <div class="hide-container">
          <div v-if="getCurrentMarkupMode != 'draft'" class="hide-container-title">{{ getPlmType }}</div>
          <div v-else class="hide-container-title">{{ markupType }}</div>
          <div class="actions">
            <Toggle v-if="getCurrentMarkupMode === 'draft'" :state="getState" size="mini" @change="changeType($event)"/>
            <div class="hide-container-icon" @click="hideVersionContainer">
              <Icon size="small" name="arrowHeadLeft" color="secondary" />
            </div>
          </div>
        </div>
        <div>
          <VersionDropDown
            v-if="false"
            v-bind="$attrs"
            @toggleListPopover="$emit('toggleListPopover')"
          />
          <NewVersionDropdown
            :sheetId="sheetId"
            :onVersionSelection="onVersionSelection"
          />

          <ActiveVersion
            v-if="false"
            :deletePlanSheetVersion="deletePlanSheetVersion"
          />
          <MarkupList
            :onClickVersionContainerMarkup="onClickVersionContainerMarkup"
            :canvasRefs="canvasRefs"
            :sheetId="sheetId"
            @clearObjects="$emit('clearObjects')"
            @onDeleteElement="onDeleteElement"
            @onChangeMarkup="onChangeMarkup"
            @onMoreOptions="onMoreOptions"
          />
        </div>
      </div>
    </div>
    <div
      v-show="!showVersionContainer"
      id="version-container-hide"
      @click="hideVersionContainer"
    >
      <div class="expand-icon">
        <Icon size="small" name="arrowHeadRight" color="secondary" />
      </div>
      <div class="layer-icon">
        <Icon size="medium" name="layers" color="secondary" />
      </div>
      <div class="layer-title">Layer</div>
    </div>
  </div>
</template>

<script>
import VersionDropDown from "./VersionDropDown.vue";
import NewVersionDropdown from "./NewVersionDropdown.vue";
import ActiveVersion from "./ActiveVersion.vue";
import MarkupList from "./MarkupList.vue";
import { mapGetters, mapActions, mapState } from "vuex";
import { MARKUP_TYPES, PLAN_MARKUP_MODES } from "@/constants";
import get from "lodash/get"
import  capitalize  from "lodash/capitalize";
// import { Toggle } from '@development/linarc-design-components';
export default {
  name: "VersionContainer",
  components: {
    VersionDropDown,
    ActiveVersion,
    MarkupList,
    NewVersionDropdown,
    // Toggle,
  },
  props: {
    // currentVersion: {
    //   type: String,
    //   default: "",
    // },
    onClickVersionContainerMarkup: {
      type: Function,
      default: () => {},
    },
    sheetId: {
      type: String,
      default: "",
    },
    // versions: {
    //   type: Object,
    //   default: () => {
    //     return {};
    //   },
    // },
    // markupLists: {
    //   type: Array,
    //   default: () => {
    //     return [];
    //   },
    // },
    deletePlanSheetVersion: {
      type: Function,
      default: () => {},
    },
    onVersionSelection: {
      type: Function,
      required: true,
    },
    canvasRefs: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      showVersionContainer: false,
    };
  },
  computed: {
    ...mapState({
      planMarkUpById: (state) => state.MarkupCanvas.planMarkupById,
    }),
    ...mapGetters({
      getMarkupType: "MarkupCanvas/getMarkupType",
      getCurrentMarkupMode: "MarkupCanvas/getCurrentMarkupMode",
      getCurrentMarkupId: "MarkupCanvas/getCurrentMarkupId",
    }),
    markupType() {
      return capitalize(this.getMarkupType);
    },
    showType() {
      return (
        this.getCurrentMarkupMode === PLAN_MARKUP_MODES.EDIT ||
        this.getCurrentMarkupMode === PLAN_MARKUP_MODES.DRAFT
      )
    },
    getPlmType() {
      return capitalize(get(this.planMarkUpById[this.getCurrentMarkupId], "plm_type", "MARKUP"));
    },
    getState() {
      return this.markupType === MARKUP_TYPES.TAKE_OFF;
    },
  },
  methods: {
    ...mapActions({
      switchMarkupType: "MarkupCanvas/switchMarkupType",
    }),
    changeType(event) {
      // this.switchMarkupType(event);
      const value = event ? MARKUP_TYPES.TAKE_OFF : MARKUP_TYPES.MARKUP;
      console.log("changeType", value);
      this.switchMarkupType(value);
    },
    onMoreOptions(data) {
      this.$emit("onMoreOptions", data);
    },
    hideVersionContainer() {
      this.showVersionContainer = !this.showVersionContainer;
    },
    onDeleteElement(elementId){
      this.$emit("onDeleteElement", elementId);
    },
    onChangeMarkup(){
      this.$emit("onChangeMarkup");
    },
  },
};
</script>

<style scoped lang="scss">
#version-container {
  position: absolute;
  top: 160px;
  margin-left: 10px;
  width: 300px;
  height: 550px;
  background-color: #ffffff;
  box-sizing: border-box;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
}
#version-container-hide {
  position: absolute;
  top: 160px;
  margin-left: 10px;
  width: 60px;
  height: 104px;
  background-color: #ffffff;
  box-sizing: border-box;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  cursor: pointer;
  .expand-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0px;
    cursor: pointer;
  }
  .layer-icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .layer-title {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px 0px;
    font-weight: 300;
    font-size: 13px;
    line-height: 18px;
    color: #809fb8;
  }
}
.hide-container {
  padding: 10px 10px 10px 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 0.5px solid #e7edf5;

  .hide-container-title {
    font-weight: 600;
    font-size: 14px;
    line-height: 18px;
    color: #000000;
  }
  .hide-container-icon {
    cursor: pointer;
  }
}

.actions {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  margin-right: 10px;
}
</style>