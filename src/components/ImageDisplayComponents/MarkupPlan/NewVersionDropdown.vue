<template>
  <div class="version-dropdown-container">
    <div class="version-label">Version</div>
    <div class="version-dropdown">
      <Dropdown
        placeholder="All"
        :border="false"
        responsive
        :options="versionOptions"
        :selectedValue="getSelectedValue"
        label="label"
        value="value"
        size="tiny"
        @change="OnSelected"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import get from "lodash/get";
export default {
  name: "VersionDropdown",
  props: {
    sheetId: {
      type: String,
      default: "",
    },

    onVersionSelection: {
      type: Function,
      default: () => {},
    },
    onPlanChange: {
      type: Function,
      default: () => {},
    },
  },

  computed: {
    ...mapGetters({
      getVersionsBySheetId: "MarkupCanvas/getVersionsBySheetId",
      sheetMetaById: "MarkupCanvas/sheetMetaById",
    }),
    versionOptions() {
      const versions = this.getVersionsBySheetId(this.getSheetCode);
      const options = [];
      console.log("versions", versions, this.getSheetCode);
      for (const version of versions) {
        options.push({
          value: version,
          label:
            "v" + get(this.sheetMetaById(version), "plf_sheet_version", "1"),
        });
      }
      return options;
    },
    getSheetCode() {
      return get(this.sheetMetaById(this.sheetId), "plf_sheet_number");
    },
    getSelectedValue() {
      return (
        "v" + get(this.sheetMetaById(this.sheetId), "plf_sheet_version", "1")
      );
    },
  },
  methods: {
    OnSelected(val) {
      this.onVersionSelection(val);
    },
  },
};
</script>

<style scoped lang="scss">
.version-dropdown-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 0.5px solid #e7edf5;
  padding: 10px;

  .version-dropdown {
    width: 100px;
  }
}
</style>
