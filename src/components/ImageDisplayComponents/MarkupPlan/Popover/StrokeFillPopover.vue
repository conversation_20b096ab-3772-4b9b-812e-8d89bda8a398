<template>
  <div id="popover-wrapper" v-click-outside="clickOutside" :style="style">
    <div id="popover">
      <div v-show="showColorPicker" id="color-picker">
        <ColorPicker @input="onChangeColor" />
      </div>
      <div id="slider-wrapper">
        <div class="slider-text">{{ getTitle }}</div>
        <div id="slider">
          <Slider
            :value="getHardness"
            :max="getMaxValue"
            @change="valueChanged"
          />
        </div>
        <div id="slider-value" class="slider-text">{{ getHardness }}</div>
      </div>
      <div id="color-palette">
        <ColorPalette
          @toggleColorPicker="toggleColorPicker"
          @colorPicked="colorPicked"
        />
      </div>
    </div>
  </div>
</template>

<script>
import ColorPalette from "../UtilsComponents/ColorPalette.vue";
import { mapGetters, mapActions } from "vuex";

export default {
  name: "StrokeFillPopover",
  components: {
    ColorPalette,
  },
  props: {
    isStroke: {
      type: Boolean,
      default: true,
    },
    yPos: {
      type: Number,
      default: 100,
    },
    setFillAndStroke: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      showColorPicker: false,
      sliderValue: 0,
      stroke: {
        color: "#F9C150",
        hardness: 100,
      },
      // isStroke: this.isStroke,
    };
  },
  computed: {
    ...mapGetters({
      getStroke: "PlanMarkup/getStroke",
      getFill: "PlanMarkup/getFill",
    }),
    getMaxValue() {
      return 100;
    },
    style() {
      return `top:${this.yPos - (this.showColorPicker ? 300 : 50)}px;`;
    },
    getHardness() {
      return this.isStroke ? this.getStroke.hardness : this.getFill.hardness;
    },
    getTitle() {
      return this.isStroke ? "Size" : "Opacity";
    },
  },
  mounted() {
    this.stroke = this.isStroke ? this.getStroke : this.getFill;
  },
  methods: {
    ...mapActions({
      setStroke: "PlanMarkup/setStroke",
      setFill: "PlanMarkup/setFill",
    }),
    clickOutside() {
      this.$emit("closeModal");
    },
    valueChanged(val) {
      this.sliderValue = val;
      console.log("val changed", val);
      this.isStroke
        ? this.setStroke({ hardness: val })
        : this.setFill({ hardness: val });
      // this.setFillAndStroke();
      this.isStroke
        ? this.$emit("strokeChanged", val)
        : this.$emit("hardnessChanged", val);
    },
    toggleColorPicker() {
      this.showColorPicker = !this.showColorPicker;
    },
    colorPicked(val) {
      if (this.isStroke) this.setStroke({ color: val });
      else this.setFill({ color: val });
      //this.setFillAndStroke();
      this.$emit("colorPicked", this.getStrokeFillEmit(val));
    },
    onChangeColor(val) {
      if (this.isStroke) this.setStroke({ color: val });
      else this.setFill({ color: val });
      this.$emit("colorPicked", this.getStrokeFillEmit(val));
      //this.setFillAndStroke();
    },
    getStrokeFillEmit(val) {
      if (this.isStroke) return { type: "stroke", color: val };
      else return { type: "fill", color: val };
    },
  },
};
</script>

<style lang="scss" scoped>
#popover-wrapper {
  position: absolute;
  width: 330px;
  background-color: #ffffff;
  box-shadow: 3px 6px 10px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  top: 160px;
  right: 123px;
}
#popover {
  display: flex;
  padding: 25px 10px 25px 10px;
  justify-content: center;
  flex-direction: column;
}
#slider-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}
#slider {
  width: 100%;
  padding: 0px 5px;
}
.slider-text {
  font-size: 15px;
  line-height: 18px;
  font-weight: 300;
  margin-left: 5px;
}
#color-palette {
  margin-top: 30px;
}
#color-picker {
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
}
</style>
