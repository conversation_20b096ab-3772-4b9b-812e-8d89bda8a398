<template>
  <div id="popover-wrapper" v-click-outside="clickOutside" :style="style">
    <div id="popover">
      <div id="slider-wrapper">
        <div class="slider-text">Size</div>
        <div id="slider">
          <Slider :sliderValue="getSize" @valueChanged="valueChanged" />
        </div>
        <div id="slider-value" class="slider-text">{{ getSize + "px" }}</div>
      </div>
      <div id="text-styles">
        <div
          id="text-style-regular"
          :style="[getStyle === 'Regular' ? { color: '#2b446c' } : {}]"
          @click="changeSelected('Regular')"
        >
          Regular
        </div>
        <div
          id="text-style-medium"
          :style="[getStyle === 'Medium' ? { color: '#2b446c' } : {}]"
          @click="changeSelected('Medium')"
        >
          Medium
        </div>
        <div
          id="text-style-bold"
          :style="[getStyle === 'Bold' ? { color: '#2b446c' } : {}]"
          @click="changeSelected('Bold')"
        >
          Bold
        </div>
        <div
          id="text-style-italic"
          :style="[getStyle === 'Italic' ? { color: '#2b446c' } : {}]"
          @click="changeSelected('Italic')"
        >
          Italic
        </div>
      </div>
      <div id="color-palette">
        <ColorPalette
          @toggleColorPicker="toggleColorPicker"
          @colorPicked="colorPicked"
        />
      </div>
      <div v-show="showColorPicker" id="color-picker">
        <ColorPicker @input="onChangeColor" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import ColorPalette from "../UtilsComponents/ColorPalette.vue";
export default {
  name: "TextToolPopover",
  components: {
    ColorPalette,
  },
  props: {
    yPos: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {
      showColorPicker: false,
      sliderValue: 40,
      selectedTextStyle: "Regular",
    };
  },
  computed: {
    ...mapGetters({
      getText: "PlanMarkup/getText",
    }),
    getSize() {
      return this.getText.fontSize;
    },
    getStyle() {
      return this.getText.fontStyle;
    },
    style() {
      return `top:${this.yPos - 200}px;`;
    },
    getfontStyles() {
      if (this.getStyle === "Regular") {
        return "color: #2b446c;";
      } else if (this.getStyle === "Medium") {
        return "color: #2b446c";
      } else if (this.getStyle === "Bold") {
        return "color: #2b446c;";
      } else {
        return "color: #2b446c";
      }
    },
  },
  methods: {
    ...mapActions({
      setText: "PlanMarkup/setText",
    }),
    clickOutside() {
      this.$emit("closeModal");
    },
    valueChanged(val) {
      this.sliderValue = val;
      this.setText({
        fontSize: val,
        fontStyle: this.getText.fontStyle,
        color: this.getText.color,
      });
    },
    changeSelected(val) {
      this.setText({
        fontSize: this.getText.fontSize,
        fontStyle: val,
        color: this.getText.color,
      });
    },
    toggleColorPicker() {
      this.showColorPicker = !this.showColorPicker;
    },
    colorPicked(val) {
      this.setText({
        fontSize: this.getText.fontSize,
        fontStyle: this.getText.fontStyle,
        color: val,
      });
    },
    onChangeColor(val) {
      this.setText({
        fontSize: this.getText.fontSize,
        fontStyle: this.getText.fontStyle,
        color: val,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
#popover-wrapper {
  position: absolute;
  width: 330px;
  background-color: #ffffff;
  box-shadow: 3px 6px 10px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  top: 160px;
  right: 123px;
}
#popover {
  display: flex;
  padding: 25px 10px 25px 10px;
  justify-content: center;
  flex-direction: column;
}
#slider-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}
#slider {
  width: 100%;
  padding: 0px 5px;
}
.slider-text {
  font-style: normal;
  font-size: 15px;
  line-height: 18px;
  font-weight: 300;
}
#text-styles {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 27px;
  margin-bottom: 20px;
  div {
    flex: 1;
    display: flex;
    font-weight: 500;
    font-size: 15px;
    line-height: 18px;
    color: #d7d9dd;
    cursor: pointer;
    &:hover {
      color: #2b446c;
    }
  }
}
#color-picker {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}
#text-style-bold {
  font-weight: bold;
}
#text-style-italic {
  font-style: italic;
}
</style>
