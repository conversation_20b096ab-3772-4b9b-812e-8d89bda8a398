<template>
  <div id="popover-wrapper" v-click-outside="clickOutside" :style="style">
    <div id="popover">
      <div id="one-tool" @click="rulerClicked">
        <div id="icon">
          <Icon size="medium" name="measure" color="secondary" />
        </div>
        <div id="icon-name">Ruler</div>
      </div>
      <div id="one-tool" @click="areaRulerClicked">
        <div id="icon">
          <Icon size="medium" name="measure" color="secondary" />
        </div>
        <div id="icon-name">Area</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "MeasurementPopover",
  props: {
    yPos: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {};
  },
  computed: {
    style() {
      return `top:${this.yPos - 40}px;`;
    },
  },
  methods: {
    rulerClicked() {
      this.$emit("rulerClicked");
    },
    areaRulerClicked() {
      this.$emit("areaRulerClicked");
    },
    clickOutside() {
      this.$emit("closeModal");
    },
  },
};
</script>

<style lang="scss" scoped>
#popover-wrapper {
  position: absolute;
  width: 160px;
  height: 74px;
  background-color: #ffffff;
  box-shadow: 3px 6px 10px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  top: 527px;
  right: 123px;
}
#popover {
  display: flex;
  padding-top: 16px;
  justify-content: center;
}
#one-tool {
  width: 65px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}
#icon-name {
  font-style: normal;
  font-weight: 600;
  font-size: 12px;
  line-height: 18px;
  color: #2b446c;
}
</style>
