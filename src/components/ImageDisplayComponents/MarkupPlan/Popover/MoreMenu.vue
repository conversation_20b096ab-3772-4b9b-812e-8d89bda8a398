<template>
  <div id="popover-wrapper" v-click-outside="clickOutside" :style="style">
    <div id="popover">
      <div>
        <!-- <div class="more-item">
                    <div class="item-icon">
                        <Icon size="medium" name="trash" color="secondary" />
                    </div>
                    <div class="item-text">
                        Copy
                    </div>
                </div>
                <div class="more-item">
                    <div class="item-icon">
                        <Icon size="medium" name="trash" color="secondary" />
                    </div>
                    <div class="item-text">
                        Move to
                    </div>
                </div>
                <div class="more-item" @click="renameClicked">
                    <div class="item-icon">
                        <Icon size="medium" name="trash" color="secondary" />
                    </div>
                    <div class="item-text">
                        Rename
                    </div>
                </div> -->
        <div class="more-item" @click="deleteClicked">
          <div class="item-icon">
            <Icon size="medium" name="trash" color="secondary" />
          </div>
          <div class="item-text">Delete</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "MoreMenu",
  props: {
    yPos: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {};
  },
  computed: {
    style() {
      return `top:${this.yPos - 160}px;margin-left:80px;`;
    },
  },
  methods: {
    clickOutside() {
      this.$emit("closeModal");
    },
    deleteClicked() {
      this.$emit("delete");
      this.$emit("closeModal");
    },
    renameClicked() {
      this.$emit("rename");
      this.$emit("closeModal");
    },
  },
};
</script>

<style lang="scss" scoped>
#popover-wrapper {
  position: absolute;
  width: 90px;
  background-color: #ffffff;
  box-shadow: 3px 6px 10px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  top: 527px;
  left: 155px;
}
#popover {
  display: flex;
  padding: 9px 15px;
  justify-content: center;
}
.more-item {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.item-text {
  font-style: normal;
  font-weight: 300;
  font-size: 10px;
  line-height: 18px;
  margin-top: 2px;
}
.item-icon {
  height: 15px;
  width: 15px;
  display: flex;
  align-items: center;
}
</style>
