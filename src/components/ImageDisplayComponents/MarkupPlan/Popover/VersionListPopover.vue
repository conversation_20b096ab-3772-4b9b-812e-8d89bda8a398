<template>
  <div id="popover-wrapper" v-click-outside="clickOutside">
    <div v-if="versionList.length" id="popover">
      <div
        v-for="(version, index) in versionList"
        :key="index"
        class="version-cell"
        @click="versionChanged(version)"
      >
        <div class="version-text">
          {{ version.name }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import { Icon } from '@development/linarc-design-components';
import { mapGetters } from "vuex";
export default {
  name: "VersionListPopover",
  components: {},
  props: {},
  data() {
    return {
      versionList: ["v1"],
    };
  },
  computed: {
    ...mapGetters({
      getAllVersion: "PlanMarkup/getAllVersion",
      getSelectedVersion: "PlanMarkup/getSelectedVersion",
    }),
  },
  mounted() {
    this.versionList = this.getAllVersion;
  },
  methods: {
    clickOutside() {
      this.$emit("closeModal");
    },
    versionChanged(ver) {
      if (ver !== this.getSelectedVersion.name)
        this.$emit("versionChanged", ver);
      // here we have to write the logic for changing markupList
      this.$emit("closeModal");
    },
  },
};
</script>

<style lang="scss" scoped>
#popover-wrapper {
  position: absolute;
  width: 230px;
  background-color: #ffffff;
  box-shadow: 3px 6px 10px rgb(0 0 0 / 10%);
  border-radius: 15px;
  top: 153px;
  left: 35px;
  max-height: 300px;
  overflow: auto;
}
#popover-wrapper::-webkit-scrollbar {
  display: none;
}

#popover-wrapper {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
#popover {
  display: flex;
  flex-direction: column;
  margin-top: 16px;
  margin-bottom: 16px;
  justify-content: center;
}
.version-cell {
  display: flex;
  align-items: center;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  height: 32px;
  &:hover {
    background-color: #adc1d1;
    cursor: pointer;
  }
}
.version-text {
  margin-left: 10px;
}
</style>
