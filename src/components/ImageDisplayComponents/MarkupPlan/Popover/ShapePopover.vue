<template>
  <div id="popover-wrapper" v-click-outside="clickOutside" :style="style">
    <div id="popover">
      <div id="one-tool" @click="shapeClicked('triangle')">
        <div id="icon">
          <Icon size="medium" name="triangle" color="secondary" />
        </div>
        <div id="icon-name">Triangle</div>
      </div>
      <div id="one-tool" @click="shapeClicked('square')">
        <div id="icon">
          <Icon size="medium" name="square" color="secondary" />
        </div>
        <div id="icon-name">Rectangle</div>
      </div>
      <div id="one-tool" @click="shapeClicked('circle')">
        <div id="icon">
          <Icon size="medium" name="radioButtonOff" color="secondary" />
        </div>
        <div id="icon-name">Ellipse</div>
      </div>
      <!-- <div id="one-tool">
                <div id="icon">
                    <Icon :size="'huge'" :name="'square'" color="secondary" />
                </div>
                <div id="icon-name">Square</div>
            </div> -->
    </div>
  </div>
</template>

<script>
export default {
  name: "ShapePopover",
  props: {
    yPos: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {};
  },
  computed: {
    style() {
      //      alert(this.yPos);
      return `top:${this.yPos - 50}px`;
    },
  },
  methods: {
    clickOutside() {
      this.$emit("closeModal");
    },
    shapeClicked(val) {
      this.$emit("shapeClicked", val);
    },
  },
};
</script>

<style lang="scss" scoped>
#popover-wrapper {
  position: absolute;
  width: 200px;
  height: 74px;
  background-color: #ffffff;
  box-shadow: 3px 6px 10px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  top: 527px;
  right: 123px;
}
#popover {
  display: flex;
  padding-top: 16px;
  justify-content: center;
}
#one-tool {
  width: 65px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}
#icon-name {
  font-style: normal;
  font-weight: 600;
  font-size: 12px;
  line-height: 18px;
  color: #2b446c;
}
</style>
