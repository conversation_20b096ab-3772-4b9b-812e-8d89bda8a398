<template>
  <div id="tag-container-wrapper">
    <div id="tag-icon">
      <Icon size="medium" name="pricetag" color="decline" />
    </div>
    <div id="tag-text">{{ getTagName }}</div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "ActiveVersion",
  data() {
    return {
      activeVersion: "v1",
    };
  },
  computed: {
    ...mapGetters({
      getTagName: "PlanMarkup/getTagName",
    }),
  },
};
</script>

<style scoped lang="scss">
#tag-container-wrapper {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 90px;
  height: 44px;
  top: 35px;
  right: 287px;
  background-color: #ffffff;
  border-radius: 10px;
}
#tag-icon {
  margin-right: 5px;
  margin-top: 8px;
}
#tag-text {
  font-weight: 800;
  font-size: 18px;
  line-height: 30px;
  color: #f55a08;
  margin-right: 5px;
}
</style>
