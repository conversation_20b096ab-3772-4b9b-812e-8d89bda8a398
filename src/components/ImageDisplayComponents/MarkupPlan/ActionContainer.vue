<template>
  <div id="action-container-wrapper">
    <div v-if="!getreadOnly" id="action-container">
      <div
        id="share-button"
        :style="[isDisabledShare ? { 'pointer-events': 'none' } : {}]"
        @click="$emit('share')"
      >
        <Button
          label="SHARE"
          type="secondary"
          size="medium"
          :isDisabled="isDisabledShare"
          borderColor="secondary"
        />
      </div>
      <!-- <div
        id="publish-button"
        :style="[isDisabledPublish ? { 'pointer-events': 'none' } : {}]"
        @click="onClickPublish"
      >
    
      </div> -->

      <div @click="onClickPublish">
        <Button
          label="PUBLISH"
          type="primary"
          size="medium"
          :isDisabled="false"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "ActionContainer",
  props: {
    type: {
      type: String,
      default: "",
    },
  },
  computed: {
    ...mapGetters({
      getMode: "PlanMarkup/getMode",
      getreadOnly: "PlanMarkup/getreadOnly",
      isDraftChecked: "PlanMarkup/isDraftChecked",
    }),
    isDisabledShare() {
      return this.getMode === "create" || this.getMode === "read";
    },
    isDisabledPublish() {
      if (this.type === "imageViewer") return false;
      else if (this.getMode === "update") return true;
      else return !this.isDraftChecked;
    },
  },
  methods: {
    onClickPublish() {
      this.$emit("publish");
    },
  },
};
</script>

<style scoped>
/* #action-container-wrapper{
    position: absolute;
    width: 245px;
    height: 64px;
    top: 25px;
    right: 25px;
    background-color: #ffffff;
    border-radius: 10px;
} */
#action-container {
  display: flex;
  align-items: center;
  /* height: 100%;
    width: 100%;
    padding: 8px; */
}
#share-button {
  margin-left: 13px;
  padding-right: 10px;
  border-right: 1px solid #e7edf5;
  cursor: pointer;
}
#publish-button {
  margin-left: auto;
  margin-right: 13px;
}
</style>
