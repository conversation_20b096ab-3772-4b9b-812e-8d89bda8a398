<template>
  <div id="markup-object-wrapper" v-if="computedProperty">
    <div
      id="markup-object"
      :style="style"
      @mouseover="showKebabIcon = true"
      @mouseout="showKebabIcon = false"
    >
      <div id="check-list-item">
        <Checkbox
          :value="isChecked"
          isDashed
          label=""
          @onChange="onChangeValue"
        />
        <span id="markup-name-label" title="Expand" @click="moreClicked()">{{
          markupName
        }}</span>
      </div>
      <div id="icon-save-publish"></div>
      <div
        v-show="showKebabIcon"
        id="icon-kebab"
        v-role-access="{
          name: 'plans-plansSheets-markup-markupList-more-icon',
          service: 'plans-plansSheets-markup',
          type: 'action',
          access: 'write',
        }"
        @click="onMoreOptions($event)"
      >
        <div id="more-icon-css">
          <Icon size="small" name="more" color="baseDark" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "MarkupObject",
  props: {
    plmId: {
      type: String,
      default: "",
    },
    mName: {
      type: String,
      default: "",
    },
    mIndex: {
      type: Number,
      required: true,
    },
    objType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      showKebabIcon: false,
      markupName: this.mName,
      showChecked: false,
      isChecked: false,
    };
  },
  computed: {
    ...mapGetters({
      getPlanMarkupPropData: "PlanMarkup/getPlanMarkupPropData",
      isMarkupSelected: "PlanMarkup/isMarkupSelected",
    }),
    style() {
      if (this.objType === "plansfile") {
        return `background-color: #f6faff;`;
      } else if (this.objType === "rfiquestions") {
        return `background-color: #F354004D;`;
      } else if (this.objType === "changeorder") {
        return `background-color: #00B1B74D;`;
      } else if (this.objType === "punchlistitem") {
        return `background-color: #2B76E74D;`;
      } else if (this.objType === "submittalregistry") {
        return `background-color: #F9C1504D;`;
      }
      return "";
    },
    computedProperty() {
      let id = this.plmId;
      this.setChecked(this.isMarkupSelected(id));
      return true;
    },
  },
  watch: {
    mName: {
      handler(val) {
        this.markupName = val;
      },
    },
  },
  mounted() {
    if (
      this.getPlanMarkupPropData.plmId &&
      this.getPlanMarkupPropData.plmId === this.plmId
    ) {
      this.unCheckedClicked();
    }
    // else {
    //     this.showChecked = this.isChecked;
    // }
  },
  methods: {
    // checkBoxClicked() {
    //     // console.log('checkbc', val);
    // },
    moreClicked() {
      this.$emit("toggleLayer", this.mIndex);
    },
    checkedClicked() {
      this.$emit("unChecked");
    },
    unCheckedClicked() {
      this.$emit("checked");
    },
    onMoreOptions(event) {
      this.$emit("onMoreOptions", {
        yPos: event.clientY,
        index: this.mIndex,
      });
      // this.$emit('kebabClicked')
    },
    onChangeValue(val) {
      this.setChecked(val);
      if (val === true) this.unCheckedClicked();
      else this.checkedClicked();
    },
    setChecked(val) {
      this.isChecked = val;
    },
  },
};
</script>

<style scoped>
#markup-object-wrapper {
  padding-bottom: 6px;
}
#markup-object {
  height: 36px;
  background-color: #f6faff;
  display: flex;
  align-items: center;
}
#check-list-item {
  margin-left: 12px;
}
#icon-kebab {
  margin-left: auto;
  margin-right: 10px;
  cursor: pointer;
  height: 30px;
  width: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}
#markup-name-label {
  display: inline-block;
  margin-left: 18px;
  cursor: pointer;
}
</style>
