<template>
  <div id="version-drop-down">
    <div id="dp-header">Versions</div>
    <div v-if="!showDropDown" id="dp-icon" @click="toggleListPopover()">
      <Icon size="big" name="arrowDownwardOutline" color="baseDark" />
    </div>
    <div v-else id="dp-icon" @click="toggleListPopover()">
      <Icon size="big" name="arrowUpwardOutline" color="baseDark" />
    </div>
  </div>
</template>

<script>
export default {
  name: "VersionDropDown",
  components: {
    // Dropdown,
  },
  props: {
    currentVersion: {
      type: String,
      default: "",
    },
    versions: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      showDropDown: false,
    };
  },
  mounted() {},
  methods: {
    toggleListPopover() {
      this.showDropDown = !this.showDropDown;
      this.$emit("toggleListPopover");
    },
  },
};
</script>

<style scoped>
#version-drop-down {
  height: 42px;
  display: flex;
  align-items: center;
  border-bottom: 0.5px solid #e7edf5;
}
#dp-header {
  margin-left: 12px;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  line-height: 18px;
  color: #2b446c;
}
#dp-icon {
  margin-left: auto;
  margin-right: 18px;
  cursor: pointer;
}
</style>
