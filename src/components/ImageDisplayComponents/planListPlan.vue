<template>
  <div id="plan-list-plan">
    <imageDisplay
      v-if="showPlan == true"
      ref="image-display"
      :all_data="plansData"
      :plans="true"
      :type="'plan'"
      :sheetCode="sheetCode"
      :sheetVersion="sheetVersion"
      :switchProps="switchProps"
      :onSheetClicked="onSheetClicked"
      :goBack="goBack"
      :base_url="baseURL"
      :saveProp="onSave"
      :markUpVersionData="markUpVersionData"
      :onUpload="onUpload"
      :cameraAttachment="cameraAttachment"
      :markupTitle="markupTitle"
    />
    <planList
      v-else
      :plansData="plansData"
      :baseURL="baseURL"
      :onSheetClicked="onSheetClicked"
      :focusSheet="focusSheet"
      :fetchmarkup="fetchmarkup"
      :onVersionClicked="onVersionClicked"
      :cameraAttachment="cameraAttachment"
    />
  </div>
</template>

<script>
import imageDisplay from "../imageDisplay/imageDisplay";
import planList from "./planList";

export default {
  name: "plan-list-plan",
  components: {
    imageDisplay,
    planList,
  },
  props: [
    "plansData",
    "baseURL",
    "getVersions",
    "getMarkups",
    "onSave",
    "onUpload",
    "fetchmarkup",
    "cameraAttachment",
  ],
  data() {
    return {
      showPlan: false,
      sheetCode: null,
      sheetVersion: null,
      focusSheet: null,
      markUpVersionData: null,
      markupTitle: null,
    };
  },
  methods: {
    getCurrentVersion(sheet) {
      return this.plansData[sheet].current_version;
    },
    onSheetClicked(sheet, version = null) {
      this.sheetCode = sheet;
      this.sheetVersion =
        version !== null ? version : this.getCurrentVersion(sheet);
      this.showPlan = true;
      this.focusSheet = sheet;
    },
    onVersionClicked(sheet, markupObj, version, name) {
      this.sheetCode = sheet;
      this.sheetVersion = version;
      this.markUpVersionData = markupObj;
      this.markupTitle = name;
      this.showPlan = true;
    },
    switchProps(sheet) {
      this.sheetCode = sheet;
      this.sheetVersion = this.getCurrentVersion(sheet);
      this.focusSheet = sheet;
      // this.$refs['image-display'].loadPage()
    },
    goBack() {
      this.showPlan = false;
      this.sheetCode = null;
      this.sheetVersion = null;
      this.markUpVersionData = null;
    },
  },
};
</script>

<style scoped></style>
