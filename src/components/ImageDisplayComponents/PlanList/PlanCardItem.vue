<template>
  <Box :columnSize="2">
    <template #body>
      <AlertWrapper :objectId="plfId">
        <div class="card-items">
          <Box :columnSize="2">
            <template #body>
              <div class="card-top">
                <img
                  :src="thumbnail"
                  alt="plan-thumbnail"
                  @click="$emit('select')"
                />
              </div>
            </template>
          </Box>
          <Box :columnSize="2">
            <template #body>
              <div class="card-middle">
                <div class="middle-item">
                  <p
                    :title="planName"
                    class="plan-name"
                    @click="$emit('select')"
                  >
                    {{ planName }}
                  </p>
                </div>
                <div
                  v-if="!getreadOnly"
                  v-role-access="{
                    name: 'plans-plansSheets-plansCardItem-delete-icon',
                    service: 'plans-plansSheets',
                    type: 'action',
                    access: 'delete',
                  }"
                  class="middle-item remove"
                  @click="$emit('remove', plfId)"
                >
                  <Icon
                    name="trash"
                    size="huge"
                    color="baseLight"
                    class="remove"
                  />
                </div>
              </div>
            </template>
          </Box>
          <Box :columnSize="3">
            <template #body>
              <div class="card-bottom" @click="openVersion">
                <Icon
                  name="eye"
                  size="tiny"
                  color="baseLight"
                  class="eye-icon"
                />
                <small class="bottom-item">Versions</small>
                <p class="update">Updated {{ updatedFromNow }}</p>
              </div>
            </template>
          </Box>
        </div>
      </AlertWrapper>
    </template>
  </Box>
</template>

<script>
import { mapGetters } from "vuex";
export default {

  props: {
    planName: {
      type: String,
      default: "RFI",
    },
    versions: {
      type: Array,
      default: () => {
        [1, 2, 3];
      },
    },
    thumbnail: {
      type: String,
      default:
        "https://linarc-image-resize.s3.ap-south-1.amazonaws.com/test/plan-image_original.jpeg",
    },
    lastUpdated: {
      type: String,
      default: "",
    },
    plfId: {
      type: String,
      default: undefined,
    },
    cardItem: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {};
  },
  computed: {
    ...mapGetters({
      getreadOnly: "PlanMarkup/getreadOnly",
    }),
    updatedFromNow() {
      return this.$moment(this.lastUpdated).fromNow();
    },
  },
  mounted() {},
  methods: {
    openVersion() {
      this.$emit("openVersion", this.planName);
    },
  },
};
</script>

<style lang="scss" scoped>
.card-items {
  // width: 300px;
  //height: 340px;
  box-sizing: border-box;
  margin: 10px;
  border-radius: 16px;
  background: #ffffff;

  .card-top {
    img {
      cursor: pointer;
      // width: 300px;
      width: 100%;
      height: 17vh;
      border-top-right-radius: 16px;
      border-top-left-radius: 16px;
    }
  }

  .card-middle {
    color: #06152b;
    padding: 15px 20px 0px 20px;
    height: 50px;
    display: flex;

    .middle-item {
      cursor: pointer;
    }
    .remove {
      margin-left: auto;
      margin-bottom: 10px;
    }
    .plan-name {
      // width: 200px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-top: 5px;
    }
  }

  .card-bottom {
    height: 50px;
    margin: 0 10px;
    padding: 10px 10px;
    border-top: 1px solid #e7edf5;
    font-family: Lato;
    font-style: normal;
    font-weight: normal;
    color: #adc1d1;

    .eye-icon {
      float: left;
      margin-right: 5px;
      margin-top: 2px;
    }

    .update {
      float: right;
      width: 117px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-left: 10px;
    }
    .bottom-item {
      display: inline-block;
      cursor: pointer;
      margin-bottom: 10px;
    }
  }
}
</style>
