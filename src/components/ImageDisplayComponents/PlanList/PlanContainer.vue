<template>
  <MainLayout>
    <template #header>
      <div>Plan Sheets</div>
    </template>

    <template #page-actions>
      <PlanHeaderActions
        v-show="type !== 'planAttachment'"
        :prjId="prjId"
        :openPlansUploadComponent="openPlansUploadComponent"
        :openDraftsComponent="openDraftsComponent"
        :openModal="openModal"
        :planSheets="list"
        :filterByTag="filterByTag"
        @toggleView="toggleView($event)"
      />
    </template>
    <template #content>
      <NoData
        v-if="showNoDataScreen"
        title="No Plan Sheets are available for this project, please upload plan files to create a markup."
      />
      <Grid v-if="showCards && !showNoDataScreen" :columns="10">
        <PlanCardItem
          v-for="(item, index) in filteredList"
          :key="index"
          :planName="item.planName"
          :versions="item.versions"
          :thumbnail="item.thumbnail"
          :lastUpdated="item.lastUpdated"
          :plfId="item.plfId"
          :cardItem="item"
          @select="planClicked(item)"
          @remove="removePlan($event)"
          @openVersion="openVersion"
        ></PlanCardItem>
      </Grid>

      <div v-if="!showCards" class="table-body">
        <PlanListTable :prjId="prjId" @openSheet="openSheet" />
      </div>

      <SheetUpload
        v-if="isModal"
        :prjId="prjId"
        :openPlansUploadComponent="openPlansUploadComponent"
        @closeModal="closeModal"
      />
      <DeleteConfirmModal
        v-if="showDeletePlanModal"
        @delete="deletePlan"
        @closeModal="closeDeleteModal"
      />
      <VersionModal
        v-if="openVersionFlag"
        :prjId="prjId"
        :sheetName="sheetName"
        @closeModal="closeVersionModal"
      />
    </template>
  </MainLayout>
</template>

<script>
import PlanCardItem from "./PlanCardItem.vue";
import SheetUpload from "./SheetUpload/index.vue";
import PlanListTable from "./PlansListTable/index.vue";
import { mapActions, mapGetters } from "vuex";
import filter from "lodash/filter";
import MainLayout from "@/components/MainLayout.vue";
import PlanHeaderActions from "./PlanHeaderActions.vue";
import VersionModal from "./Modals/VersionModal.vue";
import DeleteConfirmModal from "@/components/ImageDisplayComponents/MarkupPlan/Modals/DeleteConfirmModal.vue";

export default {
  components: {
    PlanCardItem,
    SheetUpload,
    PlanListTable,
    PlanHeaderActions,
    MainLayout,
    DeleteConfirmModal,
    VersionModal,
  },
  props: {
    type: {
      type: String,
      default: "",
      required: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
    prjId: {
      type: String,
      required: true,
    },
    openPlansMarkupComponent: {
      type: Function,
      required: true,
    },
    openPlansUploadComponent: {
      type: Function,
      required: true,
    },
    openDraftsComponent: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      isModal: false,
      showCards: true,
      filteredList: [],
      showDeletePlanModal: false,
      deletingPlfId: "",
      openVersionFlag: false,
      sheetName: "",
    };
  },

  computed: {
    ...mapGetters({ getreadOnly: "PlanMarkup/getreadOnly" }),
    showNoDataScreen() {
      return this.filteredList.length === 0;
    },
  },

  methods: {
    ...mapActions({
      deletePlansFile: "PlanMarkup/deletePlansFile",
    }),
    openSheet(data) {
      this.openPlansMarkupComponent(
        data.prj_id,
        data.plf_id,
        data.plf_sheet_number
      );
    },
    planClicked(item) {
      this.openPlansMarkupComponent(this.prjId, item.plfId, item.planName);
    },
    openPlan() {
      // console.log("Hi");
    },
    removePlan(plf_id) {
      this.showDeletePlanModal = true;
      this.deletingPlfId = plf_id;
    },
    closeDeleteModal() {
      this.showDeletePlanModal = false;
    },
    deletePlan() {
      this.showDeletePlanModal = false;
      this.deletePlansFile(this.deletingPlfId);
    },
    openModal() {
      this.isModal = true;
    },
    closeModal() {
      this.isModal = false;
    },
    toggleView(view) {
      if (view === "List") this.showCards = false;
      else this.showCards = true;
    },
    filterByTag(tag) {
      if (tag && tag !== "All")
        this.filteredList = filter(this.list, (item) => {
          return item.sheetTag === tag;
        });
      else this.filteredList = this.list;
    },
    openVersion(data) {
      this.openVersionFlag = true;
      this.sheetName = data;
    },
    closeVersionModal() {
      this.openVersionFlag = false;
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
.plan-container {
  // .plan-header {
  //   height: 80px;
  // }
  .table-body {
    background: #fff;
    border-radius: 15px;
    margin: 10px;
  }
}
</style>
