<template>
    <PlanContainer
      :list="getSorterdPlans"
      :prjId="prjId"
      :type="type"
      :openPlansMarkupComponent="openPlansMarkupComponent"
      :openPlansUploadComponent="openPlansUploadComponent"
      :openDraftsComponent="openDraftsComponent"
      :readOnly="readOnly"
      @openVersion="openVersion"
    />
    <!-- <div v-if="openVersionFlag">
      <VersionModal
      :prjId="prjId"
      :sheetName="sheetName"
       @closeModal="closeModal"
      />
    </div> -->


</template>

<script>
import PlanContainer from "./PlanContainer.vue";
import { mapActions, mapGetters,mapMutations } from "vuex";
//import VersionModal from "./Modals/VersionModal.vue";
import orderBy from 'lodash/orderBy';
// import config from "/src/config";
// import map from "lodash/map";
// import entries from "lodash/entries";
// import split from "lodash/split";

export default {
  components: {
    PlanContainer,
    //VersionModal
  },
  props: {
    type: {
      type: String,
      default: '',
      required: false,
    },
    prjId: {
      type: String,
      required: true,
    },
    openPlansMarkupComponent: {
      type: Function,
      default: () => {
        return () => {};
      },
      required: false,
    },
    openPlansUploadComponent: {
      type: Function,
      default: () => {
        return () => {};
      },
      required: false,
    },
    openDraftsComponent: {
      type: Function,
      default: () => {
        return () => {};
      },
      required: false,
    },
    readOnly:{
      type:Boolean,
      default:true
    }
  },
  data() {
    return {
      entireData: [],
      openVersionFlag:false,
      sheetName:''
    };
  },

  computed: {
    ...mapGetters({
      getAllPlans: "PlanMarkup/getAllPlans",
      getThumbnail: "PlanMarkup/getThumbnail",
      getLastUpdated: "PlanMarkup/getLastUpdated",
    }),
    getSorterdPlans(){
      const sortedData = orderBy(this.getAllPlans,'lastUpdated','desc')
      return sortedData
    }
  },

  async mounted() {
    this.setreadOnly(this.readOnly);
    this.setProjectId(this.prjId);
    this.setExistingSets({ prj_id: this.prjId });
    await this.setAllPlanData(this.prjId);

    // this.entireData = this.getAllPlans;

    // console.log(entries(this.entireData));

    // this.entireData = map(entries(this.getAllPlans), (element) => {
    //   let sheetName = split(element[0], ":")[1];
    //   let searchKey = element[1].current_version;
    //   console.log("Search Key", searchKey);
    //   console.log("last updated value", element[1].lastUpdated);
    //   return {
    //     planName: sheetName,
    //     versions: element[1].versions,
    //     thumbnail: this.getThumbnail(searchKey),
    //     lastUpdated: this.getLastUpdated(searchKey),
    //     plfId: searchKey,
    //   };
    // });
  },
  methods: {
    ...mapActions({
      setProjectId: "PlanMarkup/setProjectId",
      setAllPlanData: "PlanMarkup/setAllPlanData",
      setExistingSets: "PlanUploadSet/setExistingSets",
    }),
    ...mapMutations({ setreadOnly : "PlanMarkup/SET_READ_ACCESS" }),
    openVersion(sheetName){
      this.openVersionFlag = true
      this.sheetName = sheetName
    },
    closeModal(){
      this.openVersionFlag = false
    }
  },
};
</script>

<style lang="scss" scoped></style>
