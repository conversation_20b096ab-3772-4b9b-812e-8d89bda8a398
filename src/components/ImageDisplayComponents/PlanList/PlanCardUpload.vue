<template>
  <Box :columnSize="3">
    <template #body>
      <div class="upload-card" @click="openModal">
        <div class="upload-img">
          <Icon name="upload" size="massive" color="baseLine" />
        </div>
        <div class="upload-text">
          <p style="text-align: center">Upload Plan</p>
        </div>
      </div>
    </template>
  </Box>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
    openModal() {
      // console.log("Before Emit");
      this.$emit("openModal");
    },
  },
};
</script>

<style lang="scss" scoped>
.upload-card {
  cursor: pointer;
  display: flex;
  height: 340px;
  box-sizing: border-box;
  background: #ffffff;
  margin: 10px;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border-radius: 16px;
  color: #adc1d1;

  .upload-text {
    font-family: Lato;
    font-style: normal;
    font-size: 18px;
    font-weight: 600;
    line-height: 22px;
  }
  .upload-img {
    padding: 20px 30px;
  }
}
</style>
