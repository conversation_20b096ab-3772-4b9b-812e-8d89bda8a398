<template>
  <div>
    <div class="plan-right-section">
      <div
        v-role-access="{
          name: 'plans-plansSheets-upload-button',
          service: 'plans-plansSheets',
          type: 'action',
          access: 'write',
        }"
        class="plan-upload-button"
        @click="openModal"
      >
        <Button
          v-if="!getreadOnly"
          label="Upload Plans"
          type="primary"
          size="large"
        />
      </div>
      <div
        v-if="isGrid && planSheets.length"
        v-role-access="{
          name: 'plans-plansSheets-select-tag',
          service: 'plans-plansSheets',
          type: 'action',
          access: 'read',
        }"
        class="plan-sheets-dropdown"
      >
        <Dropdown
          placeholder="Select Tag"
          :selectedValue="selectedTag"
          :options="planSheetTags"
          label="option"
          size="medium"
          @change="selectTag($event)"
        />
      </div>
      <div
        v-if="planSheets.length"
        v-role-access="{
          name: 'plans-plansSheets-view-toggle',
          service: 'plans-plansSheets',
          type: 'container',
          access: 'read',
        }"
        class="plan-toggle"
      >
        <div class="toggle-item" @click="toggle(isList)">
          <p v-if="isList">
            <Icon name="list" color="secondary" class="item1" />
          </p>
          <p v-if="!isList">
            <Icon name="list" color="baseLight2" class="item1" />
          </p>
        </div>
        <div class="toggle-item" @click="toggle(isGrid)">
          <p v-if="isGrid">
            <Icon name="grid" color="secondary" class="item1" />
          </p>
          <p v-if="!isGrid">
            <Icon name="grid" color="baseLight2" class="item1" />
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

import { mapGetters, mapActions } from "vuex";
import forEach from "lodash/forEach";
import filter from "lodash/filter";
import includes from "lodash/includes";

export default {
  props: {
    prjId: {
      type: String,
      required: true,
    },
    openPlansUploadComponent: {
      type: Function,
      required: true,
    },
    openDraftsComponent: {
      type: Function,
      required: true,
    },
    openModal: {
      type: Function,
      required: true,
    },
    planSheets: {
      type: Array,
      default: () => [],
    },
    filterByTag: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      isList: false,
      isGrid: true,
      selectedTag: "",
    };
  },
  computed: {
    ...mapGetters({
      getreadOnly: "PlanMarkup/getreadOnly",
      getPlanTags: "PlanUploadSet/getPlanTags",
    }),
    planSheetTags() {
      const options = ["All"];
      //adding all the tags which are available in the available plan sheets
      forEach(this.planSheets, (sheet) => {
        options.push(sheet.sheetTag);
      });
      this.filterByTag("");
      const set = new Set(options);
      //getting tag option names from the system
      const tagOptions = filter(this.getPlanTags, (tag) =>
        includes([...set], tag.value)
      );
      return [{ value: "All", option: "All" }, ...tagOptions];
    },
  },
  mounted() {
    this.setPlanTags();
  },
  methods: {
    ...mapActions({
      setPlanTags: "PlanUploadSet/setPlanTags",
    }),
    toggle(event) {
      if (event === false) {
        if (this.isList === false) {
          this.$emit("toggleView", "List");
          this.isList = !this.isList;
          this.isGrid = !this.isGrid;
        } else {
          this.$emit("toggleView", "Grid");
          this.isList = !this.isList;
          this.isGrid = !this.isGrid;
        }
      }
    },
    selectTag(event) {
      this.selectedTag = event;
      this.filterByTag(event.value);
    },
    openDrafts() {
      this.openDraftsComponent();
    },
  },
};
</script>

<style lang="scss" scoped>
.plan-right-section {
  display: flex;
  justify-content: flex-end;
  margin-top: 5px;
  align-items: center;
  .plan-upload-button {
    margin-right: 20px;
  }
  .plan-sheets-dropdown {
    margin-right: 0px;
  }
  // .plan-drafts {
  //   width: 156px;
  //   height: 40px;
  //   border-radius: 15px;
  //   background: #ffffff;
  //   border: 1px solid #2b446c;
  //   margin: 15px 0;
  // }
  .plan-toggle {
    display: flex;
    justify-content: space-between;
    width: 120px;
    height: 36px;
    border-radius: 15px;
    background: #ffffff;
    border: 1px solid #2b446c;
    margin: 10px 20px;
    padding: 5px 15px;

    .toggle-item {
      cursor: pointer;
    }
  }
}
</style>
