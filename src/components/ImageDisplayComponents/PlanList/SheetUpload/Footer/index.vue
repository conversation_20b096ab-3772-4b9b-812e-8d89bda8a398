<template>
  <div class="navigation-buttons">
    <div>
      <Button
        type="baseLight4"
        textColor="baseLight"
        displayBorder="true"
        borderColor="baseLight"
        label="Cancel"
        size="large"
        @onClick="closeModal"
      />
    </div>
    <div>
      <Button
        type="secondary"
        label="Next"
        size="large"
        @onClick="createData"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    prjId: {
      type: String,
      required: true,
    },
  },
  methods: {
    closeModal() {
      this.$emit("closeModal");
    },
    createData() {
      this.$emit("createData");
    },
  },
};
</script>

<style lang="scss" scoped>
.navigation-buttons {
  margin-top: 30px;
  display: flex;
  justify-content: space-between;
}
</style>
