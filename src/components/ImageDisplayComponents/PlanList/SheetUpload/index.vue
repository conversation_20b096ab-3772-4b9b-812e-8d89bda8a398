<template>
  <div>
    <Grid :columns="12" :gap="1" class="plan-container">
      <Box :columnSize="12" class="plan-body">
        <template #body>
          <Modal
            :isFooter="false"
            :isHeader="false"
            isCloseButton
            width="800"
            minHeight="600"
            maxHeightBody="800"
          >
            <template #modal-body>
              <div class="full-modal-view">
                <Header @closeModal="closeModal" />
                <div class="plan-modal-container">
                  <Box :columnSize="6" class="left-box">
                    <template #body>
                      <div
                        :class="{
                          currentSelection: leftBox,
                          noSelection: !leftBox,
                        }"
                        @click="invertToggle('left')"
                      >
                        <NewSet
                          :setName="setName"
                          :setPrefix="setPrefix"
                          @displayVal="displayVal"
                          @updateName="updateName($event)"
                          @updatePrefix="updatePrefix($event)"
                        />
                      </div>
                    </template>
                  </Box>

                  <Box :columnSize="6" class="right-box">
                    <template #body>
                      <div
                        :class="{
                          currentSelection: rightBox,
                          noSelection: !rightBox,
                        }"
                        @click="invertToggle('right')"
                      >
                        <SetsList
                          :prjId="prjId"
                          @updateSearchSet="updateSearchSet($event)"
                          @updateRadio="updateRadio($event)"
                        />
                      </div>
                    </template>
                  </Box>
                </div>

                <Footer
                  :prjId="prjId"
                  @closeModal="closeModal"
                  @createData="createData"
                />
              </div>
            </template>
          </Modal>
        </template>
      </Box>
    </Grid>
  </div>
</template>

<script>
import Header from "./Header/index.vue";
import NewSet from "./SheetUploadBody/NewSet/index.vue";
import SetsList from "./SheetUploadBody/SetsList/index.vue";
import Footer from "./Footer/index.vue";
import { mapActions, mapGetters } from "vuex";
import get from "lodash/get";

export default {
  components: {
    Header,
    NewSet,
    SetsList,
    Footer,
  },
  data() {
    return {
      searchSet: "",
      setName: "",
      setPrefix: "",
      selectedSet: null,
      leftBox: true,
      rightBox: false,
    };
  },
  props: {
    list: Array,
    prjId: {
      type: String,
      required: true,
    },
    openPlansUploadComponent: {
      type: Function,
      required: true,
    },
  },
  computed: {
    ...mapGetters({
      activeBatchId: "PlanUploadSet/getActiveBatchId",
      activeSetId: "PlanUploadSet/getActiveSetId",
    }),
  },
  methods: {
    ...mapActions({
      createNewSet: "PlanUploadSet/createNewSet",
      setPrivateItem: "PlanUploadSet/setPrivateItem",
      setActiveSet: "PlanUploadSet/setActiveSet",
      createBatch: "PlanUploadBatch/createBatch",
      setActiveBatch: "PlanUploadSet/setActiveBatch",
    }),
    invertToggle(data) {
      if (data === "left" && this.leftBox === false) {
        this.leftBox = !this.leftBox;
        this.rightBox = !this.rightBox;
      }
      if (data === "right" && this.rightBox === false) {
        this.leftBox = !this.leftBox;
        this.rightBox = !this.rightBox;
      }
    },
    closeModal() {
      this.$emit("closeModal");
    },
    displayVal() {
      //    console.log("Main File values", this.setName, this.setPrefix);
    },
    updateName(e) {
      this.setName = e;
    },
    updatePrefix(e) {
      this.setPrefix = e;
    },
    updateRadio(e) {
      this.selectedSet = e;
      //  console.log("Radio Value", e);
    },
    updateSearchSet(e) {
      this.searchSet = e;
      // console.log("Search Set Value", e);
    },
    async createData() {
      //  console.log("Next Button Clicked");
      if (this.leftBox === true) {
        if (!this.setName || !this.setPrefix) {
          this.$toast.error("Please fill Name and Prefix to proceed");
          return;
        }
        let obj = {
          setName: this.setName,
          setPrefix: this.setPrefix,
          prjId: this.prjId,
        };
        try {
          let responseData = await this.createNewSet(obj);
          let psmId = responseData.psm_id;

          await this.setPrivateItem({ obj_id: psmId, prjId: this.prjId });
          await this.setActiveSet(responseData);
          let batchData = await this.createBatch(responseData);
          await this.setActiveBatch(batchData);
          this.openPlansUploadComponent({
            prjId: this.prjId,
            psmId: responseData.psm_id,
            pbtId: batchData.pbt_id,
          });
        } catch (e) {
          console.log("Error in creating new set", e);
          this.$toast.error(get(e, "response.data.message.error", ""));
        }
      } else {
        if (!this.selectedSet) {
          this.$toast.error("Please select a set to proceed");
          return;
        }
        await this.setActiveSet({ psm_id: this.selectedSet });
        let batchData = await this.createBatch({ psm_id: this.selectedSet });
        await this.setActiveBatch(batchData);
        this.openPlansUploadComponent({
          prjId: this.prjId,
          psmId: this.selectedSet,
          pbtId: batchData.pbt_id,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  scrollbar-color: #2b76e7;
  // background: #f1f4f9;
}
.plan-container {
  // margin-left: 100px;
  // margin-top: 90px;
  // margin-right: 50px;
  // padding: 0 30px;
  // border: 2px solid black;

  .plan-header {
    // height: 200px;
  }
  .plan-body {
    // height: 100vh;

    .full-modal-view {
      margin: 30px;

      .plan-modal-container {
        display: flex;
        justify-content: space-between;

        .right-box {
          margin-left: 30px;
        }

        .noSelection {
          width: 350px;
          height: 400px;
          background: #f4f9ff;
          border: 1px solid #e7edf5;
          box-shadow: none;

          ::-webkit-scrollbar {
            width: 5px;
          }

          /* Track */
          ::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px #ffffff;
            border-radius: 10px;
          }

          /* Handle */
          ::-webkit-scrollbar-thumb {
            background: #adc1d1;
            border-radius: 10px;
          }
        }

        .currentSelection {
          width: 350px;
          height: 400px;
          border: 1.5px solid #2b76e7;
          box-shadow: 10px 10px 15px rgba(0, 0, 0, 0.06);

          // scrollbar-color: #2b76e7;
          ::-webkit-scrollbar {
            width: 5px;
          }

          /* Track */
          ::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px #e7edf5;
            border-radius: 10px;
          }

          /* Handle */
          ::-webkit-scrollbar-thumb {
            background: #2b76e7;
            border-radius: 10px;
          }
        }
      }
    }
  }
}
</style>
