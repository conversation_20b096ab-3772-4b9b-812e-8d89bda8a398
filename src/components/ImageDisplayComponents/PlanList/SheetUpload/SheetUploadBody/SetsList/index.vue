<template>
  <div class="upload-right-container">
    <p class="right-title">Upload from existing set</p>
    <div class="search-set">
      <Input
        v-model="searchSet"
        placeholder="Search Set"
        iconPosition="right"
        @input="$emit('updateSearchSet', searchSet)"
      />
      <Icon name="search" color="baseLight" class="search-icon" />
    </div>
    <div class="select-set-container">
      <div class="select-set">
        <div
          v-for="(item, index) in filtredRadioData"
          :key="index"
          class="radio-items"
        >
          <div class="radio-name">
            <Radio
              v-model="selectedSets"
              size="small"
              backgroundColor="baseRoyalBlue"
              :value="item.psm_id"
              :label="item.psm_set_prefix"
              @change="$emit('updateRadio', $event)"
            />
          </div>
          <div class="radio-date">
            <span>
              {{ item.psm_created_on }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
// import split from "lodash/split";
import map from "lodash/map";
import filter from "lodash/filter";
import upperCase from "lodash/upperCase";
import includes from "lodash/includes";

export default {
  props: {
    prjId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      searchSet: "",
      selectedSets: null,
      radioData: null,
    };
  },
  computed: {
    ...mapGetters({
      getExistingSetList: "PlanUploadSet/getExistingSetList",
    }),
    filtredRadioData() {
      let newArray = filter(this.radioData, (item) =>
        includes(item.psm_set_prefix, upperCase(this.searchSet))
      );
      return newArray;
    },
  },

  async mounted() {
    this.tempData = await this.getExistingSetList(this.prjId);
    this.radioData = map(this.tempData, (item) => {
      let newDate = this.$moment(item.psm_created_on).format("MM/DD/YYYY");
      return { ...item, psm_created_on: newDate };
    });
  },
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.upload-right-container {
  width: 350px;
  height: 415px;
  padding: 20px 35px;

  .right-title {
    font-family: Lato;
    font-style: normal;
    font-weight: bold;
    font-size: 14px;
    color: #2b446c;
    margin-bottom: 15px;
  }

  .search-set {
    position: relative;
    margin-bottom: 20px;

    .search-icon {
      position: absolute;
      right: 35px;
      bottom: 5px;
    }
  }

  .select-set-container {
    overflow-y: auto;
    width: 293px;
    height: 282px;
    // padding-right: 30px;
    .select-set {
      border: 1px solid #e7edf5;
      padding: 15px;
      .radio-items {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20px;

        .radio-name {
          cursor: pointer;
          margin-top: 1px;
          margin-right: 10px;
          width: 60px;
        }

        .radio-date {
          font-family: Lato;
          font-style: normal;
          font-weight: normal;
          font-size: 10px;
          color: #809fb8;
        }
      }
    }
  }
}
</style>
