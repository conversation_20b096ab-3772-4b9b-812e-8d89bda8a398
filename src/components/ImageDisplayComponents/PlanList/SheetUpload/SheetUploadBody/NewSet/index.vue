<template>
  <div class="upload-left-container">
    <h4 class="left-title">New Set Upload</h4>
    <p>Enter Set Name</p>
    <div>
      <Input
        v-model="setName"
        placeholder="Text"
        size="medium"
        :rules="requiredTrue"
        @input="$emit('updateName', setName)"
      />
    </div>
    <p>Enter Set prefix</p>
    <div>
      <Input
        v-model="setPrefix"
        placeholder="Enter 3 Character Prefix"
        size="medium"
        :rules="requiredMin"
        @input="$emit('updatePrefix', setPrefix)"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    setName: {
      type: String,
      default: "",
    },
    setPrefix: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      requiredTrue: {
        required: true,
      },
      requiredMin: {
        required: true,
        exact: 3,
        alpha_numeric: true,
      },
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.upload-left-container {
  cursor: pointer;
  width: 350px;
  height: 415px;
  padding: 20px 30px;

  .left-title {
    font-family: Lato;
    font-style: normal;
    font-weight: bold;
    font-size: 14px;
    color: #2b446c;
    margin-bottom: 40px;
  }

  p {
    margin-bottom: 10px;
    padding: 0px 5px;
  }

  div {
    margin-bottom: 20px;
  }
}
</style>
