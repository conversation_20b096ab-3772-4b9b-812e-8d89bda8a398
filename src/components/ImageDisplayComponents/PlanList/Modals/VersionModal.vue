<template>
  <!-- <div> -->
  <Modal
    :isFooter="false"
    :width="880"
    :maxHeightBody="600"
    @closeModal="$emit('closeModal')"
  >
    <template #modal-left> Plans List </template>
    <template #modal-body>
      <div class="wrapper-class">
        <SpreadSheetDataWrapper
          ref="plans_version_wrapper"
          tblId="plans_version"
          :params="tableParams"
          :columns="headerConfig"
          :prjId="prjId"
        >
          <template #spreadsheet="{ spreadsheetData, columns }">
            <div class="v2Wrapper">
              <SpreadSheetV2
                id="plans_version"
                ref="plans_version"
                :key="columns.length"
                :headerActionsConfig="{
                  displayHeaderActions: false,
                  topBar: false,
                  filter: true,
                }"
                :data="spreadsheetData"
                :header="columns"
                tbleId="plans_version"
                :dataOptions="dataOptions"
                :userId="userId"
              >
                <template slot="plf_sheet_name" slot-scope="{ rowData }">
                  <TableRedirection
                    :text="rowData.plf_sheet_name"
                    @click.native="onVersionSelection(rowData)"
                  />
                </template>
                <template slot="plf_sheet_version" slot-scope="{ rowData }">
                  <div class="sheet-number">
                    <TableRedirection
                      :text="JSON.stringify(rowData.plf_sheet_version)"
                      @click.native="onVersionSelection(rowData)"
                    />
                  </div>
                </template>
                <template slot="thumbnail_url" slot-scope="{ rowData }">
                  <div
                    class="plans-image"
                    @click="openPlan(rowData.plf_sheet_signed_url)"
                  >
                    <img
                      class="table-cell-image"
                      :src="rowData.thumbnail_url"
                      alt="plans"
                    />
                  </div>
                </template>
                <template slot="plf_id" slot-scope="{ rowData }">
                  <div class="sheet-delete">
                    <Icon
                      name="trash2"
                      color="decline"
                      @click.native="removePlan(rowData.plf_id)"
                    />
                  </div>
                </template>
              </SpreadSheetV2>
            </div>
          </template>
        </SpreadSheetDataWrapper>
      </div>
    </template>
  </Modal>
  <!-- </div> -->
</template>

<script>
import plansConfig from "/src/config";
// import InviteUserDiv from '../UtilsComponents/InviteUserDiv.vue';
// import ShareUserCell from '../UtilsComponents/ShareUserCell.vue';
import { GRID_V2_COLUMN_TYPE } from "@/constants";
import TableRedirection from "@/components/tableRedirection/index.vue";

import debounce from "lodash/debounce";
export default {
  name: "VersionModal",
  components: {
    TableRedirection,
  },
  props: {
    mpName: {
      type: String,
      default: "",
    },
    selectCategory: {
      type: Boolean,
      default: true,
    },
    prjId: {
      type: String,
      default: "",
    },
    sheetName: {
      type: String,
      default: "",
    },
    onVersionSelection: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      canSelectCategory: this.selectCategory,
      value: this.mpName,
      headerConfig: [
        {
          label: "S.No",
          field: "",
          type: GRID_V2_COLUMN_TYPE.SEQUENCE,
        },
        {
          label: "Sheet name",
          field: "plf_sheet_name",
          type: GRID_V2_COLUMN_TYPE.SLOT,
          width: "200px",
        },
        {
          label: "Created on",
          field: "plf_created_on",
          type: GRID_V2_COLUMN_TYPE.DATE,
          customProps: { inputFormat: "YYYY-MM-DD" },
          width: "200px",
        },
        {
          label: "Version",
          field: "plf_sheet_version",
          type: GRID_V2_COLUMN_TYPE.SLOT,
          width: "100px",
        },
        {
          label: "Thumbnail",
          field: "thumbnail_url",
          type: GRID_V2_COLUMN_TYPE.SLOT,
          width: "100px",
        },
        {
          label: "Delete",
          field: "plf_id",
          type: GRID_V2_COLUMN_TYPE.SLOT,
          width: "100px",
        },
      ],
    };
  },
  computed: {
    dataOptions() {
      return {
        // pbt_id: this.activeBatchId,
        user_id: this.userId,
        prj_id: this.prjId, /// make it dynamic
        plf_sheet_number: this.sheetName,
      };
    },
    tableParams() {
      return {
        user_id: this.userId,
        prj_id: this.prjId,
        // pcm_id: plansConfig.getProjectCompanyId(),
        plf_sheet_number: this.sheetName,
      };
    },
  },
  created() {
    this.userId = plansConfig.getUserId();
    window.addEventListener(
      "resize",
      debounce(() => {
        this.tableHeight = window.innerHeight;
      }, 1000)
    );
  },
  beforeDestroy() {
    window.removeEventListener(
      "resize",
      () => (this.tableHeight = window.innerHeight)
    );
  },
  beforeCreate() {
    const baseUrl = plansConfig.getBaseUrl();
    this.$designComponentsConfig.init(
      `${baseUrl}/api/v1`,
      plansConfig.getAccessToken(),
      plansConfig.getUserId(),
      plansConfig.getProjectId(),
      plansConfig.getProjectCompanyId()
    );
  },
  methods: {
    getDateFormatted(date) {
      return this.$moment(date).format("MM/DD/YYYY");
    },
    openPlan(plf_sheet_signed_url) {
      window.open(plf_sheet_signed_url);
    },
    openSheet(raw) {
      this.$emit("OpenSheetMarkup", raw);
    },
    removePlan(plf_id) {
      this.$emit("onDeletePlanSheet", plf_id);
    },
  },
};
</script>

<style lang="scss" scoped>
.plans-image {
  width: 7rem;
  height: 2.25 rem;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3px 20px;
  cursor: pointer;
}
.wrapper-class {
  min-height: 250px;
  height: 300px;
}
.sheet-delete {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.v2Wrapper {
  height: 300px;
  overflow: hidden;
}

.table-cell-image {
  height: 25px;
}
</style>
