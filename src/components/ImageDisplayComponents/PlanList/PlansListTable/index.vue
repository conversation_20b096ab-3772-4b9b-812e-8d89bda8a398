<template>
  <Grid>
    <Box
      bgcolor="baseLight4"
      borderRadius="big"
      :columnSize="12"
      class="plan-list-table"
    >
      <template #body>
        <SmartTable
          tblId="plans_sheet_list_view"
          :userId="userId"
          :headerConfig="headerConfig"
          :uniqueKeys="['plf_id']"
          :height="getHeight"
          :dataOptions="dataOptions"
          :showAlerts="true"
        >
          <template slot="plf_updated_on" slot-scope="{ columnData }">
            <div class="created-date">
              {{ getDateFormatted(columnData.data) }}
            </div>
          </template>
          <template slot="plf_sheet_number" slot-scope="{ rawData }">
            <div class="sheet-number" @click="openSheet(rawData)">
              {{ rawData.plf_sheet_number }}
            </div>
          </template>
          <template slot="thumbnail_url" slot-scope="{ columnData }">
            <div class="plans-image" @click="openPlan(columnData.data)">
              <img
                class="table-cell-image"
                :src="columnData.data"
                alt="plans"
              />
            </div>
          </template>
        </SmartTable>
      </template>
    </Box>
  </Grid>
</template>

<script>

import { mapGetters } from "vuex";
import plansConfig from "/src/config";
import debounce from "lodash/debounce";
export default {
  components: {},
  props: {
    prjId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      tableHeight: window.innerHeight,
      headerConfig: {
        plf_updated_on: {
          component: "slot",
        },
        plf_sheet_number: {
          component: "slot",
        },
        thumbnail_url: {
          component: "slot",
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      activeBatchId: "PlanUploadSet/getActiveBatchId",
    }),
    dataOptions() {
      return {
        // pbt_id: this.activeBatchId,
        user_id: this.userId,
        prj_id: this.prjId, /// make it dynamic
        pcm_id: plansConfig.getProjectCompanyId(),
      };
    },
    getHeight() {
      return this.tableHeight - 190 + "px";
    },
  },

  created() {
    this.userId = plansConfig.getUserId();
    window.addEventListener(
      "resize",
      debounce(() => {
        this.tableHeight = window.innerHeight;
      }, 150)
    );
  },
  beforeDestroy() {
    window.removeEventListener(
      "resize",
      () => (this.tableHeight = window.innerHeight)
    );
  },
  beforeCreate() {
    const baseUrl = plansConfig.getBaseUrl();
    this.$designComponentsConfig.init(`${baseUrl}/api/v1`, plansConfig.getAccessToken(),plansConfig.getUserId() ,plansConfig.getProjectId(), plansConfig.getProjectCompanyId());
  },
  methods: {
    openSheet(raw) {
      this.$emit("openSheet", raw);
    },
    getDateFormatted(date) {
      return this.$moment(date).format("MM/DD/YYYY");
    },
    openPlan(plf_sheet_signed_url) {
      window.open(plf_sheet_signed_url);
    },
  },
};
</script>

<style lang="scss" scoped>
.created-date {
  padding-left: 40px;
}
.sheet-number {
  cursor: pointer;
  padding-left: 20px;
  //text-align: center;
}
.plan-list-table {
  height: 100%;
  overflow: auto;
}
.plans-image {
  width: 7rem;
  height: 2.25 rem;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3px 20px;
  cursor: pointer;
}
.table-cell-image {
  height: 36px;
}
</style>
