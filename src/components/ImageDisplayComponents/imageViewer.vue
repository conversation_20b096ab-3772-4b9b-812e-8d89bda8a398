<template>
  <div id="image-viewer">
      <!-- this is image viewer -->
      <imageDisplay
        :plans="false"
        :type="'image'"
        :imageMeta="imageMeta"
        :goBack="goBack"
        :saveProp="saveProp"
      />
  </div>
</template>

<script>
import imageDisplay from '../imageDisplay/imageDisplay'
export default {
    name:"image-viewer",
    props:[
        "imageMeta",
        "goBack",
        "saveProp"
    ],
    components:{
        imageDisplay
    },
    mounted(){
        console.log("this is the meta information of the images", this.imageMeta)
    }
}
</script>

<style scoped>

</style>