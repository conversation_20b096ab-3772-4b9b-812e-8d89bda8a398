<template>
  <div>
    <!-- <button @click="getPlansData"></button> -->
    <div class="img-loader" v-show="this.loading">
      <PulseLoader :loading="true" color="#f57947"></PulseLoader>
    </div>
    <image-display
      v-if="imageMeta"
      :plans="false"
      :type="'imageViewer'"
      :imageMeta="imageMeta.obj"
      :goBack="goBack"
      :saveProp="imageMarkupSave"
    />
    <plan-list-plan
      v-else
      :onSave="onSavePlan"
      :baseURL="baseURL"
      :plansData="plansData"
      :cameraAttachment="cameraAttachment"
    >
    </plan-list-plan>
    <addMarkupModal
      v-if="showSaveModal"
      @close="showSaveModal = false"
      @done="addMarkup"
    />
  </div>
</template>

<script>
import planListPlan from "./planListPlan";
import imageDisplay from "./imageViewer.vue";
import addMarkupModal from "@/components/plansList/modals/addMarkupModal.vue";
import PulseLoader from "vue-spinner/src/PulseLoader";
import keys from 'lodash/keys';
import forEach from 'lodash/forEach';

export default {
  name: "plan-attachment",
  components: {
    planListPlan,
    addMarkupModal,
    imageDisplay,
    PulseLoader,
  },
  props: {
    baseURL: {
      require: true,
    },
    objMarkup: {
      require: true,
    },
    imageMeta: {
      require: false,
    },
    closed: {
      require: true,
    },
    objIdCheck: {
      require: false,
    },
    iosWebView: {
      require: false,
      default: false,
    },
    goBack: {},
    cameraAttachment: {
      type: Object,
      default: null,
      require: false,
    },
  },
  data() {
    return {
      imageData: {
        imageSrc: null,
        thumbSrc: null,
      },
      loading: false,
      planImageAttachment: null,
      imageSrc: null,
      showSaveModal: false,
      sheetCode: null,
      plansData: {},
      markupData: {
        plm_markup: {},
      },
      formData: null,
    };
  },
  mounted() {
    this.getPlansData();
  },
  methods: {
    //Get project plan files
    async getPlansData() {
      const responseData = await this.$axios.get(
        `${this.objMarkup.url}/api/v1/plan/${this.objMarkup.prjId}/computed`,
        {
          headers: {
            Authorization: this.objMarkup.token,
          },
        }
      );
      this.plansData = responseData.data[0].pld_computed;
    },

    //Adding markup name from input modal
    async addMarkup(markupName) {
      this.showSaveModal = false;
      this.markupData.plm_name = markupName;
      forEach(keys(this.markupData),(key) => {
        this.formData.set(key, this.markupData[key]);
      });
      const url = `${this.objMarkup.url}/api/v1/plan/${this.objMarkup.prjId}/markup/${this.sheetCode}`;

      await this.$axios
        .post(url, this.formData, {
          headers: {
            Authorization: this.objMarkup.token,
          },
        })
        .then(() => {
          this.onSaveImage(this.planImageAttachment, markupName);
        })
        .catch(() => {
          // console.log(err);
        });
    },

    // saving Image Markup
    async imageMarkupSave(obj) {
      (this.imageData.imageSrc = obj),
        (this.imageData.thumbSrc = obj),
        await this.onSaveImage(this.imageData, this.imageMeta.imgName);
    },
    async onSavePlan(obj) {
      this.sheetCode = obj.sheetCode;
      if (!this.objMarkup.objId)
        //getting object if not present
        await this.objIdCheck();

      this.markupData = {
        plm_sheet_version: obj.sheetVersion,
        plm_createdby: this.objMarkup.userId,
        obj_id: this.objMarkup.objId,
        obj_type: this.objMarkup.objName,
      };

      const formData = new FormData();

      formData.append("plm_markup", JSON.stringify(obj.markupObj));
      formData.append("plm_thumbnail", obj.thumbSrc);

      this.formData = formData;
      if (!this.iosWebView) this.showSaveModal = true;
      else this.addMarkup("sample image");
      this.planImageAttachment = obj;
    },
    //Adding Attachment
    async onSaveImage(obj, name) {
      this.loading = true;

      const url = `${this.objMarkup.url}/api/v1/attachment/pre_signed_post/`;
      if (!this.objMarkup.objId) await this.objIdCheck(); //getting object if not present
      const attachment = {
        file_name: name,
        model_name: this.objMarkup.objName,
        object_id: this.objMarkup.objId,
      };
      const response = await this.$axios.post(url, attachment, {
        headers: {
          Authorization: `${this.objMarkup.token}`,
        },
      });

      const storeS3File = {
        key: response.data.fields.key,
        AWSAccessKeyId: response.data.fields.AWSAccessKeyId,
        policy: response.data.fields.policy,
        signature: response.data.fields.signature,
      };
      const formData = new FormData();
      forEach(keys(storeS3File),(key) => {
        formData.set(key, storeS3File[key]);
      });

      const storeThumbFile = {
        key: response.data.thumbnail_fields.key,
        AWSAccessKeyId: response.data.thumbnail_fields.AWSAccessKeyId,
        policy: response.data.thumbnail_fields.policy,
        signature: response.data.thumbnail_fields.signature,
      };
      const formDataThumbnail = new FormData();
      forEach(keys(storeThumbFile),(key) => {
        formDataThumbnail.set(key, storeThumbFile[key]);
      });

      formData.append("file", obj.imageSrc);
      formDataThumbnail.append("file", obj.thumbSrc);
      const p1 = await this.$axios.post(response.data.url, formData);
      const p2 = await this.$axios.post(response.data.url, formDataThumbnail);
      Promise.all([p1, p2]).then(() => {
        this.$toast.success('Image Uploaded Successfully');
        this.goBack();
      })
    },
  },
};
</script>

<style scoped lang="scss">
.img-loader {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 50;
}
</style>
