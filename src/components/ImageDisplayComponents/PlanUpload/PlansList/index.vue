<template>
  <div class="plans-upload__plans-list">
      <!-- <PulseLoader
        :loading="dataLoader"
        color="#f57947"
        class="plans_batch_loader"
      ></PulseLoader> -->
    <PlansListTable
      v-if="showPlansListTable"
      @handleUploadedFiles="handleUploadedFiles"
    />
    <PlansListEmpty
      v-if="showEmptyList"
      :batchStatus="getBatchStatus(activeBatchId)"
    />
  </div>
</template>

<script>
import PlansListEmpty from "./PlansListEmpty";
import PlansListTable from "./PlansListTable";
import { mapGetters, mapActions } from "vuex";
// import PulseLoader from "vue-spinner/src/PulseLoader";

export default {
  components: {
    PlansListEmpty,
    PlansListTable,
    // PulseLoader
  },
  props: {
    clickUpload: {
      type: Function,
      required: true,
    },
    handleUploadedFiles: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      dataLoader: true,
    };
  },
  computed: {
    ...mapGetters({
      getBatchFiles: "PlanUploadFile/getBatchFiles",
      activeBatchId: "PlanUploadSet/getActiveBatchId",
      getBatchFileIds: "PlanUploadBatch/getBatchFileIds",
      getBatchStatus: "PlanUploadBatch/getBatchStatus",
    }),
    showPlansListTable() {
      return this.getBatchFileIds(this.activeBatchId).length > 0 ;
    },
    showEmptyList() {
      return this.getBatchFileIds(this.activeBatchId).length === 0;
    },
  },
  // async mounted() {
  //   this.dataLoader = true;
  // },
  methods: {
    ...mapActions({
      setAllBatchFiles: "PlanUploadFile/setAllBatchFiles",
    }),
    setLoadertoFalse() {
      this.dataLoader = false;
    },
  },
  // mounted() {
  //   this.setAllBatchFiles({pbt_id:this.activeBatchId})
  // },
  // methods: {
  //   ...mapActions({
  //     setAllBatchFiles: "PlanUploadFile/setAllBatchFiles",
  //   }),
  // },
};
</script>

<style lang="scss" scoped>
.plans-upload__plans-list {
  padding: 5px;
  height: 100%;
}
.plans_batch_loader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70vh;
}
</style>
