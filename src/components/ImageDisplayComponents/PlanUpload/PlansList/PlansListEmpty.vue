<template>
  <div class="plan-upload__empty-list">
    <!-- <FileUploadCard
        iconColor="baseLight2"
        buttonColor="baseLight2"
        plansUploadText="Upload Plan Set"
        plansUploadSubText="click here to upload"
      /> -->
    <!-- <NoData
      :fullHeight="false"
        :primaryTitle=getNoDataTitle.primary
        :title=getNoDataTitle.secondary
      /> -->
    <div class="flex flex-col items-center justify-center h-full">
      <div class="text-xl font-bold mb-2">
        {{ getNoDataTitle.primary }}
      </div>
      <div class="text-lg text-gray-600">
        {{ getNoDataTitle.secondary }}
      </div>
    </div>
  </div>
</template>

<script>
// import FileUploadCard from "../UploadModal/FileUploadCard";
import {BATCH_STATUS_TEXT} from "@/constants";
export default {
  name: "PlansModal",
  props: {
    batchStatus: {
      type: String,
      required: true,
    },
  },
  // components: {
  //   // FileUploadCard,
  // },
  computed: {
    getNoDataTitle() {
      if (this.batchStatus === BATCH_STATUS_TEXT["PCS"]) {
        return {
          primary: "Plan Sheets are Processing",
          secondary: "Please Visit back in sometime.",
        };
      } else {
        return {
          primary: "No Plan Files found",
          secondary: "Click Upload to start.",
        };
      }
    },
  },
};
</script>

<style scoped lang="scss">
.plan-upload__empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 70%;
  justify-content: center;
  padding-top: 10%;
}
</style>
