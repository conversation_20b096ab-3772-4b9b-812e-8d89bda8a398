<template>
  <div class="spreadsheet-wrapper">
    <AttachmentModal
      v-if="selectedAttachment"
      :src="selectedAttachment"
      @closeShowModal="onCloseAttachmentModal"
    />
    <!-- <div
      v-if="!showDeleteButton"
      title="Extract Sheet Code with AI"
      class="sheet-code-extract-bulk"
    >
      <Button
        type="primary"
        icon="shuffle"
        label="Sheet Code with AI"
        :isDisabled="selectedIds.length > 5 || selectedIds.length === 0"
        @onClick="extractBulkSheetCodes"
      />
    </div> -->
    <PulseLoader
      v-if="showLoader"
      :loading="true"
      color="#f57947"
      class="pulse-loader"
    ></PulseLoader>

    <!-- 
    <SpreadSheet

      ref="spread-sheet"
      v-model="jsonData"
      :header="getColumns"
      :noFooter="true"
      :rowSelection="rowSelection"
      height="auto"
      @update="onRowUpdate"
      @select="onSelect"
    >
      <template slot="plf_sheet_signed_url" slot-scope="{ rowData }">
        <div
          class="plans-image"
          @click="openPlan(rowData.plf_sheet_signed_url)"
        >
          <img
            class="table-cell-image"
            :src="rowData.thumbnail_url"
            alt="plans"
          />
        </div>
      </template>
      <template slot="plf_icon" slot-scope="{ rowData }">
        <div class="flex">
          <div
            v-if="!showDeleteButton"
            title="Delete Sheet"
            class="plf_access_button"
            @click="remove(rowData.plf_id)"
          >
            <Icon name="trash2" color="decline" />
          </div>
          <div
            v-if="false"
            title="Extract Sheet Code with AI"
            class="plf_access_button"
            @click="
              extractSheetCode(rowData.plf_id, rowData.plf_sheet_signed_url)
            "
          >
            <Icon name="shuffle" color="success" />
          </div>
        </div>
      </template>
    </SpreadSheet> -->
    <DeleteConfirmModal
        v-if="showDeletePlanModal"
        @delete="deletePlan"
        @closeModal="closeDeleteModal"
      />

    <SpreadSheetV2
    id="plans_set_batches_sheet_list"
    :key="tableKey"
      tbleId="plans_set_batches_sheet_list"
      :data="jsonData"
      :header="getColumns"
      :headerActionsConfig="{ topBar: false, filter: true }"
      :userId="userId"
      :dataOptions="dataOptions"
      @update="onRowUpdate"
    >
      <template slot="plf_sheet_signed_url" slot-scope="{ rowData }">
        <div
          class="plans-image"
          @click="openPlan(rowData.plf_sheet_signed_url)"
        >
          <img
            class="table-cell-image"
            :src="rowData.thumbnail_url"
            alt="plans"
          />
        </div>
      </template>
      <template slot="plf_icon" slot-scope="{ rowData }">
        <div class="flex">
          <div
            v-if="!showDeleteButton"
            title="Delete Sheet"
            class="plf_access_button"
            @click="setDeletePlanSheetId(rowData.plf_id)"
          >
            <Icon name="trash2" color="decline" />
          </div>
          <div
            v-if="false"
            title="Extract Sheet Code with AI"
            class="plf_access_button"
            @click="
              extractSheetCode(rowData.plf_id, rowData.plf_sheet_signed_url)
            "
          >
            <Icon name="shuffle" color="success" />
          </div>
        </div>
      </template>
    </SpreadSheetV2>
  </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import debounce from "lodash/debounce";
import plansConfig from "/src/config";
import get from "lodash/get";
import forEach from "lodash/forEach";
import cloneDeep from "lodash/cloneDeep";
import { GRID_V2_COLUMN_TYPE } from "@/constants";
import PulseLoader from "vue-spinner/src/PulseLoader";
import DeleteConfirmModal from "@/components/ImageDisplayComponents/MarkupPlan/Modals/DeleteConfirmModal.vue";
import trim from "lodash/trim";

export default {
  name: "PlansListTable",
  components: { PulseLoader, DeleteConfirmModal },
  data() {
    return {
      tableKey:0,
      selectedAttachment: null,
      showLoader: false,
      selectedIds: [],
      rowSelection: {
        rowNumber: true,
        multiSelect: false,
      },
      showDeletePlanModal: false,
      toDeleteSheetId: null,
      tableHeight: window.innerHeight,
      columns: [
        {
          field: "plf_sno",
          label: "S.No",
          type: GRID_V2_COLUMN_TYPE.SEQUENCE,
        },
        {
          field: "plf_sheet_number",
          label: "Sheet Number",
          type: GRID_V2_COLUMN_TYPE.SHORT_TEXT,
          customValidation: { isMandatory: true,
            validate: (value) => this.validateRequiredField (value, "Sheet Number"),
           },
          readonly: this.isReadOnly,
          width: "120px",
        },
        {
          field: "plf_sheet_name",
          label: "Sheet Name",
          type: GRID_V2_COLUMN_TYPE.SHORT_TEXT,
          readonly: this.isReadOnly,
          customValidation: { isMandatory: true ,
            validate: (value) => this.validateRequiredField (value, "Sheet Name"),
          },
          width: "700px",
        },
        {
          field: "plf_sheet_tag",
          label: "Discipline",
          type: GRID_V2_COLUMN_TYPE.SELECT,
          readonly: this.isReadOnly,
          customProps: {
            label: "name",
            optionsKeyName: "options",
            options: [],
          },
          customValidation: { isMandatory: true,
            validate: (value) => this.validateRequiredField (value, "Discipline"),
           },
          width: "100px",
        },
        {
          field: "plf_sheet_signed_url",
          label: "Sheet",
          type: GRID_V2_COLUMN_TYPE.SLOT,
          readonly: true,
          width: "100px",
        },
        {
          field: "plf_icon",
          label: "Actions",
          type: GRID_V2_COLUMN_TYPE.SLOT,
          readonly: true,
          width: "300px",
        },
      ],
      headerConfig: {
        plf_sheet_tag: {
          component: "select",
          access: "write",
          selectOptions: [],
          headerClass: "w-72",
        },
        plf_sheet_number: {
          component: "input",
          access: "write",
        },
        plf_sheet_name: {
          component: "input",
          access: "write",
        },
        thumbnail_url: {
          component: "slot",
        },
        plf_icon: {
          component: "slot",
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      activeBatchId: "PlanUploadSet/getActiveBatchId",
      getPlanTags: "PlanUploadSet/getPlanTags",
      getreadOnly: "PlanMarkup/getreadOnly",
      getBatchPublishedStatus: "PlanUploadBatch/getBatchPublishedStatus",
      getUploadedBatchFiles: "PlanUploadFile/getUploadedBatchFiles",
      getBatchFiles: "PlanUploadFile/getBatchFiles",
    }),
    getHeight() {
      return this.tableHeight - 240 + "px";
    },
    showDeleteButton() {
      return this.getBatchPublishedStatus(this.activeBatchId);
    },
    dataOptions() {
      return {
        pbt_id: this.activeBatchId,
        user_id: this.userId,
        prj_id: plansConfig.getProjectId(),
        pcm_id: plansConfig.getProjectCompanyId(),
      };
    },
    jsonData() {
      const dataItems = this.getUploadedBatchFiles(this.activeBatchId);
      console.log("jsonData", dataItems);
      return dataItems;
    },
    getColumns() {
      const columnsCopy = cloneDeep(this.columns);
      forEach(columnsCopy, (item) => {
        if (item.field === "plf_sheet_tag") {
          let finalTagOptions = [];
          for (const item of this.getPlanTags) {
            finalTagOptions.push({
              value: item.value,
              name: item.option,
            });
          }
          item.customProps.options = finalTagOptions;
        }
      });
      return columnsCopy;
    },
  },
  watch: {
    getColumns: {
      handler() {
        this.refreshTable();
      },
      deep: true // Watch for nested changes in column objects
    }
  },
  async mounted() {
    await this.setPlanTags();
  },
  created() {
    this.userId = plansConfig.getUserId();
    window.addEventListener(
      "resize",
      debounce(() => {
        this.tableHeight = window.innerHeight;
      }, 150)
    );
  },
  beforeCreate() {
    const baseUrl = plansConfig.getBaseUrl();
    this.$designComponentsConfig.init(
      `${baseUrl}/api/v1`,
      plansConfig.getAccessToken(),plansConfig.getUserId() ,plansConfig.getProjectId(), plansConfig.getProjectCompanyId()
    );
  },
  methods: {
    ...mapActions({
      patchFileDetails: "PlanUploadFile/patchFileDetails",
      setPlanTags: "PlanUploadSet/setPlanTags",
      removeBatchFileId: "PlanUploadBatch/removeBatchFileId",
      deletePlansFileVersion: "PlanMarkup/deletePlansFileVersion",
      extractSheetCodeGemini: "PlanMarkup/extractSheetCodeGemini",
      extractSheetCodeGeminiBatch: "PlanMarkup/extractSheetCodeGeminiBatch",
    }),
    onCloseAttachmentModal() {
      this.selectedAttachment = null;
    },
    validateRequiredField(value, fieldName) {
        if (value && value !== ' ') {
          value = trim(value);
          if (!value) {
            return `${fieldName} required`;
          }
        } else {
          return `${fieldName} required`;
        }
      },
    refreshTable() {
      this.tableKey += 1;
    },
    onSelect(selectedRows) {
      this.selectedIds = selectedRows;
    },
    openPlan(plf_sheet_signed_url) {
      // window.open(plf_sheet_signed_url);
      this.selectedAttachment = plf_sheet_signed_url;
    },
    setDeletePlanSheetId(plfId) {
      this.toDeleteSheetId = plfId;
      this.showDeletePlanModal = true;
    },
    closeDeleteModal() {
      this.toDeleteSheetId = null;
      this.showDeletePlanModal = false;
    },
    deletePlan() {
      this.remove(this.toDeleteSheetId);
      this.toDeleteSheetId = null;
      this.showDeletePlanModal = false;
    },
    isReadOnly() {
      if (
        !this.getBatchPublishedStatus(this.activeBatchId) &&
        this.getBatchFiles(this.activeBatchId).length > 0
      ) {
        return false;
      } else {
        return true;
      }
    },
    getUserId() {
      return plansConfig.getUserId();
    },
    async onRowUpdate(newData) {
      let data = get(newData, "currentRecord");
      let newVal = get(newData, "newVal");
      let fieldName = get(newData, "name");
      console.log(data, newVal, fieldName);
      await this.patchFileDetails({
        plf_id: data.plf_id,
        [fieldName]: newVal,
      });
    },
    async remove(plfId) {
      // await this.deletePlansFileVersion(plfId);
      // this.$emit("handleUploadedFiles");
      this.removeBatchFileId({
        plf_id: plfId,
        pbt_id: this.activeBatchId,
      });
    },
    async extractSheetCode(plf_id, plf_sheet_signed_url) {
      this.showLoader = true;
      try {
        await this.extractSheetCodeGemini({
          plf_id,
          image_url: plf_sheet_signed_url,
        });
        this.$emit("handleUploadedFiles");
      } finally {
        this.showLoader = false;
      }
    },
    async extractBulkSheetCodes() {
      this.showLoader = true;
      try {
        const sheets = [];
        forEach(this.selectedIds, (id) => {
          const sheet = this.jsonData.find((sheet) => sheet.$id === id);
          sheets.push({
            image: sheet.plf_sheet_signed_url,
            plfId: sheet.plf_id,
          });
        });

        await this.extractSheetCodeGeminiBatch({
          plf_data: sheets,
        });
        this.$emit("handleUploadedFiles");
      } finally {
        this.showLoader = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.plans-image {
  width: 90px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3px 20px;
  cursor: pointer;
}
.table-cell-image {
  height: 25px;
}
.plf_access_button {
  padding: 3px 20px;
  cursor: pointer;
}
.spreadsheet-wrapper {
  height: calc(100vh - 280px);
}
.pulse-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.sheet-code-extract-bulk {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
}
</style>
