<template>
  <div class="plans-upload__footer">
    <div>
      <Button
        :size="'large'"
        label="Cancel"
        type="baseLight4"
        textColor="baseLight2"
        borderColor="baseLight2"
        :displayBorder="true"
        @onClick="clickCloseModal"
      />
    </div>
    <div>
      <Button
        id="plansFileUploadContinue"
        ref="plansFileUploadContinue"
        :size="'large'"
        label="Continue"
        type="secondary"
        :isDisabled="isContinueDisabled"
        @onClick="continueClick"
      />
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
export default {
  name: "",
  props: {
    isContinueDisabled: {
      type: Boolean,
      default: false,
    },
    clickCloseModal: {
      type: Function,
      default: () => {},
    },
    setUploadedFiles: {
      type: Function,
      default: () => {},
    },
  },
  computed: {
    ...mapGetters({
      activeBatchId: "PlanUploadSet/getActiveBatchId",
    }),
  },
  methods: {
    continueClick() {
      this.clickCloseModal();
      this.processBatchFiles({ pbt_id: this.activeBatchId });
      this.setUploadedFiles();
    },
    ...mapActions({
      processBatchFiles: "PlanUploadBatch/processBatchFiles",
    }),
  },
};
</script>

<style lang="scss" scoped>
.plans-upload__footer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
}
</style>
