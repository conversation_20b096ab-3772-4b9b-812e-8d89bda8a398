<template>
  <div class="plans-upload__modal__header">
    <div class="plans-upload__modal__header__title">
      {{ title }}
    </div>
    <div class="plans-upload__modal__header__icon" @click="clickCloseModal">
      <Icon size="medium" name="close" color="baseDark" />
    </div>
  </div>
</template>

<script>
export default {
  name: "PlansUploadModalHeader",
  props: {
    title: {
      type: String,
      default: "Sheet Upload",
    },
    clickCloseModal: {
      type: Function,
      default: () => {},
    },
  },
};
</script>

<style scoped lang="scss">
.plans-upload__modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 30px 0px 1px;
  border-bottom: 1px solid #e6e6e6;
}
.plans-upload__modal__header__title {
  width: 100%;
  height: 100%;
  border-radius: 15px;
  background: white;
  font-weight: bold;
  font-size: 24px;
  line-height: 10px;
  color: #000000;
  padding: 30px;
}
.plans-upload__modal__header__icon {
  cursor: pointer;
}
</style>
