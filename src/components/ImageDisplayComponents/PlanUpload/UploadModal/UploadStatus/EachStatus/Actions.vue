<template>
  <div class="plans-upload__upload-status__progress-bar__actions">
    <div
      class="plans-upload__upload-status__progress-bar__actions__icon__close"
      @click="deleteUploadingFile"
    >
      <Icon color="baseLight2" name="close" />
    </div>

    <div
      v-if="plfStatus === 'FAILED'"
      style="cursor: pointer"
      class="plans-upload__upload-status__progress-bar__actions__icon__success"
    >
      <label for="file-upload">
        <Icon color="secondary" name="refresh" />
        <div class="button-input">
          <input
            id="file-upload"
            ref="file"
            type="file"
            enctype="multipart/form-data"
            multiple="multiple"
            accept="image/*,.pdf,.zip"
            style="width: 150px; cursor: pointer"
            @change="handleFileUpload"
          />
        </div>
      </label>
    </div>

    <div
      v-else
      class="plans-upload__upload-status__progress-bar__actions__icon__success"
    >
      <Icon color="baseRoyalBlue" name="checkmark" />
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
export default {
  name: "Actions",
  props: {
    plfStatus: {
      type: String,
      default: "",
    },
    plfId: {
      type: String,
      default: "",
    },
  },
  computed: {
    ...mapGetters({
      getActiveBatchId: "PlanUploadSet/getActiveBatchId",
    }),
  },
  methods: {
    ...mapActions({
      deletePlansFile: "PlanMarkup/deletePlansFile",
      uploadFilesToBatch: "PlanUploadFile/uploadFilesToBatch",
    }),
    deleteUploadingFile() {
      this.deletePlansFile(this.plfId);
    },
    handleFileUpload(e) {
      // this.deletePlansFile(this.plfId);
      let file = e.target.files;
      this.uploadFilesToBatch({
        pbt_id: this.getActiveBatchId,
        file: file,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.plans-upload__upload-status__progress-bar__actions {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  background: #ffffff;

  &__icon__success {
    background: #e7edf5;
    border-radius: 5px;
    padding: 4px;
    margin: 0px 5px 0px 5px;
    cursor: pointer;
    .button-input {
      position: absolute;
      bottom: 5px;
      left: 10px;
      opacity: 0;
      cursor: pointer;
    }
  }
  &__icon__close {
    background: #e7edf5;
    border-radius: 5px;
    padding: 6px;
    margin: 0px 5px 0px 5px;
    cursor: pointer;
  }
}
</style>
