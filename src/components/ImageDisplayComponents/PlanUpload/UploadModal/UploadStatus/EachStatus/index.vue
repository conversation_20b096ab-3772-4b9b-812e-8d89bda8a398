<template>
  <div class="plans-upload__progressbar">
    <div style="width: 10%">
      <FileName :fileName="fileUploadStatus.plf_file_name" />
    </div>
    <div style="width: 79%">
      <ProgressBar :percentage="fileUploadStatus.plf_uploaded_percentage" />
    </div>
    <div style="width: 11%">
      <Actions
        :plfStatus="fileUploadStatus.plf_status"
        :plfId="fileUploadStatus.plf_id"
      />
    </div>
  </div>
</template>

<script>
import ProgressBar from "./ProgressBar";
import Actions from "./Actions";
import FileName from "./FileName";

export default {
  components: {
    ProgressBar,
    Actions,
    FileName,
  },
  props: {
    fileUploadStatus: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style scoped lang="scss">
.plans-upload__progressbar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  background: #ffffff;
  border: 1px solid #e7edf5;
  box-sizing: border-box;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.06);
  border-radius: 10px;
  margin: 5px;
}
</style>
