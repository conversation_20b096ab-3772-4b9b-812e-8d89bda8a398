<template>
  <div class="file-upload-progress">
    <div class="file-info">
      <div class="file-name">{{ fileName }}</div>
      <div class="progress-text">{{ progress }}%</div>
    </div>
    <div class="progress-bar">
      <div class="progress-bar-fill" :style="{ width: `${progress}%` }"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FileUploadProgress',
  props: {
    fileName: {
      type: String,
      required: true
    },
    progress: {
      type: Number,
      required: true,
      validator: (value) => value >= 0 && value <= 100
    }
  }
}
</script>

<style scoped lang="scss">
.file-upload-progress {
  margin-bottom: 10px;
  font-family: Arial, sans-serif;
  font-size: 14px;
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.file-name {
  font-weight: bold;
  color: #333;
}

.progress-text {
  color: #555;
  font-size: 0.9em;
}

.progress-bar {
  width: 100%;
  background-color: #e6e6e6;
  padding: 2px;
  border-radius: 4px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar-fill {
  display: block;
  height: 8px;
  background-color: #007bff;
  border-radius: 2px;
  transition: width 300ms ease;
}
</style>
