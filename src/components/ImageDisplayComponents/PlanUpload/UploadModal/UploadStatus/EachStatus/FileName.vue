<template>
  <div class="plans-upload__progress__file-name">
    <div>
      <Icon color="baseRoyalBlue" name="fileText" size="tiny" />
    </div>
    <div class="plans-upload__progress__file-name__text">{{ fileName }}</div>
  </div>
</template>

<script>
export default {
  name: "FileName",
  props: {
    fileName: {
      type: String,
      default: "",
    },
  },
};
</script>

<style lang="scss" scoped>
.plans-upload__progress__file-name {
  display: flex;
  flex-direction: row;
  align-items: center;
  background: #ffffff;
  &__text {
    margin-left: 5px;
    overflow: hidden;
    margin-right: 5px;
    height: 20px;
  }
}
</style>
