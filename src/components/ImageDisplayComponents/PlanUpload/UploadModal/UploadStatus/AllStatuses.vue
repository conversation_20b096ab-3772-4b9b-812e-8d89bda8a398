<template>
  <div>
    <div v-for="item in currentlyUploadingFiles" :key="item.plf_id">
      <EachStatus :fileUploadStatus="item" />
    </div>
  </div>
</template>

<script>
import EachStatus from "./EachStatus";
import { mapGetters } from "vuex";
export default {
  name: "AllStatuses",
  components: {
    EachStatus,
  },
  computed: {
    ...mapGetters({
      getActiveBatchId: "PlanUploadSet/getActiveBatchId",
      getUploadingFiles: "PlanUploadFile/getUploadingFiles",
    }),
    currentlyUploadingFiles: {
      get() {
        return this.getUploadingFiles(this.getActiveBatchId);
      },
    },
  },
};
</script>
