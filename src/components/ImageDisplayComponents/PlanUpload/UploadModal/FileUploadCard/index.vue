<template>
  <label for="file-upload" class="custom-file-upload">
    <input
      id="file-upload"
      ref="file"
      type="file"
      enctype="multipart/form-data"
      multiple="multiple"
      accept="image/*,.pdf,.zip"
      :style="InputStyle"
      @change="handleFileUpload"
    />
    <div id="file-upload-area" class="file-input-area">
      <div class="plans-upload__modal__content">
        <div class="plans-upload__modal__content__icon">
          <Icon name="cloudUpload" :color="iconColor" size="huge" />
        </div>
        <div class="plans-upload__modal__content__text">
          <div>{{ plansUploadText }}</div>
        </div>
        <div
          class="plans-upload__modal__content__button"
          @click="onBrowseClick"
        >
          <Button :type="iconColor" :size="'large'" label="Browse Files" />
          <div class="button-input"></div>
        </div>
        <div v-if="plansUploadSubText !== 'or'" class="plans-upload__modal__content__conjunction">
          <div>{{ plansUploadSubText }}</div>
        </div>
        <div v-if="false" class="plans-upload__modal__content__button">
          <Button
            :type="iconColor"
            :size="'large'"
            label="Upload Files from your Google Drive"
            @onClick="handleAuthClick"
          />
          <div class="button-input"></div>
        </div>
      </div>
    </div>
  </label>
</template>

<script>
// eslint-disable no-undef
import { mapActions, mapGetters } from "vuex";
import { Axios, urls } from "/src/utils/Axios";
//import config from "../../../../../config";
import { getCurrentURL } from "../../../../../utils/CommonMethods";
import get from "lodash/get";
import map from "lodash/map";
// import axios from "axios";
export default {
  components: {
    // getCurrentURL
  },
  props: {
    plansUploadText: {
      type: String,
      default: "Drag PDF files here or select option below",
    },
    plansUploadSubText: {
      type: String,
      default: "or",
    },
    buttonColor: {
      type: String,
      default: "baseRoyalBlue",
    },
    iconColor: {
      type: String,
      default: "secondary",
    },
    filesUploadingStatus: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      pickerApiLoaded: false,
      developerKey: process.env.VUE_APP_GDRIVE_DEVELOPER_KEY,
      // clientId:
      //   "649809564194-4n98ouoj7vvjmsidqh2i9gaasupvh6bf.apps.googleusercontent.com",
      // scope: "https://www.googleapis.com/auth/drive.readonly",
      oauthToken: null,
    };
  },

  computed: {
    ...mapGetters({
      getActiveBatchId: "PlanUploadSet/getActiveBatchId",
    }),
    InputStyle() {
      const style = {
        cursor: "pointer",
        width: "100%",
        position: "fixed",
        opacity: 0,
      };
      if (this.filesUploadingStatus) style.height = "220px";
      else style.height = "490px";
      return style;
    },
  },
  async mounted() {
    let dropArea = document.getElementById("file-upload-area");
    function handlerFunction(e) {
      e.stopPropagation();
      e.preventDefault();
    }
    dropArea.addEventListener("dragenter", handlerFunction, false);
    dropArea.addEventListener("dragleave", handlerFunction, false);
    dropArea.addEventListener("dragover", handlerFunction, false);
    dropArea.addEventListener("drop", handlerFunction, false);

    //script for Google api
    let gDrive = document.createElement("script");
    gDrive.setAttribute("type", "text/javascript");
    gDrive.setAttribute("src", "https://apis.google.com/js/api.js");
    document.head.appendChild(gDrive);
  },
  methods: {
    handleFileUpload(e) {
      let file = e.target.files;
      this.uploadFilesToBatch({
        pbt_id: this.getActiveBatchId,
        file: file,
      });
    },
    ...mapActions({
      uploadFilesToBatch: "PlanUploadFile/uploadFilesToBatch",
      syncUploadingFiles: "PlanUploadFile/syncUploadingFiles",
      setUploadingBatchFiles: "PlanUploadFile/setUploadingBatchFiles",
    }),
    onBrowseClick() {
      this.$refs.file.click();
    },

    //function on click gdrive button
    async handleAuthClick() {
      //API to get token
      const preSignedResponse = await this.getPreSignedPost();

      const apiStatus = get(preSignedResponse, "status");
      if (apiStatus !== 200) return;

      const token = get(preSignedResponse, "data.token");
      // eslint-disable-next-line no-undef
      gapi.load("picker", () => {
        this.pickerApiLoaded = true;
        this.createPicker(token);
      });
    },

    async getPreSignedPost() {
      // console.log("currenturl", getCurrentURL());
      const redirectURL = getCurrentURL();

      const preSignedResponse = Axios.get(
        urls.preSignedPostGdrive(redirectURL)
      );

      // this.accessToken = preSignedResponse.access_token;
      return preSignedResponse;
    },

    createPicker(token) {
      // eslint-disable-next-line no-undef
      const view = new google.picker.View(google.picker.ViewId.DOCS);
      // view.setMimeTypes("image/png,image/jpeg,image/jpg");
      // eslint-disable-next-line no-undef
      const picker = new google.picker.PickerBuilder()
        // eslint-disable-next-line no-undef
        // .enableFeature(google.picker.Feature.NAV_HIDDEN)
        // eslint-disable-next-line no-undef
        .enableFeature(google.picker.Feature.MULTISELECT_ENABLED)
        .setDeveloperKey(this.developerKey)
        .setAppId(this.clientId)
        .setOAuthToken(token)
        .addView(view)
        // eslint-disable-next-line no-undef
        .addView(new google.picker.DocsUploadView())
        .setCallback(this.pickerCallback)
        .build();
      picker.setVisible(true);
    },

    async pickerCallback(data) {
      // eslint-disable-next-line no-undef
      const docs = get(data, "docs", []);
      const payload = map(docs, (doc) => {
        return {
          file_id: doc.id,
          file_name: doc.name,
          mime_type: doc.mimeType,
        };
      });
      // console.log("payload", payload);
      if (payload.length) {
        await Axios.post(
          urls.uploadPlanDataGdrive(this.getActiveBatchId),
          payload
        );
        //set Progress bar and sync to show UploadingFiles status
        await this.setUploadingBatchFiles({ pbt_id: this.getActiveBatchId });
        await this.syncUploadingFiles({ pbt_id: this.getActiveBatchId });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.file-input-area {
  height: 100%;
  display: flex;
  width: 100%;
}
.custom-file-upload {
  display: inline-block;
  cursor: pointer;
  width: 100%;
  height: 100%;
}
.plans-upload__modal__content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;

  &__icon {
    padding: 5px;
  }
  &__text {
    padding: 5px;
    font-size: 18px;
    line-height: 22px;
    font-weight: 600;
    display: flex;
    align-items: center;
    text-align: center;
    color: #2b446c;
  }
  &__conjunction {
    padding: 10px;
    font-weight: normal;
    font-size: 14px;
    line-height: 18px;
    display: flex;
    align-items: center;
    text-align: center;
    color: #809fb8;
  }
  &__button {
    padding: 5px;
    position: relative;

    // .button-input {
    //   position: absolute;
    //   bottom: 5px;
    //   left: 10px;
    //   opacity: 0;
    // }
  }
}
</style>
