<template>
  <div>
    <Modal
      :width="900"
      :minHeight="680"
      :maxHeight="680"
      :isFooter="false"
      :isHeader="false"
      :maxHeightBody="670"
      @closeModal="clickCloseModal"
    >
      <template #modal-body>
        <Header :clickCloseModal="clickCloseModal" />
        <div class="plan-upload__modal__layout">
          <div class="plan-upload__modal-card">
            <FileUploadCard
              :filesUploadingStatus="getUploadingFiles(getActiveBatchId).length"
            />
          </div>
          <!-- <div
            v-if="getUploadingFiles(getActiveBatchId).length"
            style="
              display: flex;
              justify-content: end;
              padding: 0px 19px 9px 10px;
              cursor: pointer;
            "
            @click="setUploadingFiles"
          >
            <Icon :color="'baseRoyalBlue'" :name="'refresh'" />
          </div> -->
          <div
            v-if="getUploadingFiles(getActiveBatchId).length"
            class="plan-upload__status-card"
          >
            <UploadStatus />
          </div>
          <div>
            <Footer
              :clickCloseModal="clickCloseModal"
              :setUploadedFiles="setUploadedFiles"
            />
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
import FileUploadCard from "./FileUploadCard";
import UploadStatus from "./UploadStatus";
import Footer from "./Footer";
import Header from "./Header";
import { mapActions, mapGetters } from "vuex";

export default {
  name: "PlansModal",
  components: {
    Header,
    FileUploadCard,
    Footer,
    UploadStatus,
    // Icon,
  },
  props: {
    clickCloseModal: {
      type: Function,
      default: () => {},
    },
    state: {
      type: String,
      default: "empy",
    },
    setUploadedFiles: {
      type: Function,
      default: () => {},
    },
  },
  computed: {
    ...mapGetters({
      getActiveBatchId: "PlanUploadSet/getActiveBatchId",
      getUploadingFiles: "PlanUploadFile/getUploadingFiles",
    }),
  },
  mounted() {
    this.setUploadingBatchFiles({pbt_id: this.getActiveBatchId});
  },
  methods: {
    ...mapActions({
      setUploadingBatchFiles: "PlanUploadFile/setUploadingBatchFiles",
    }),
    // setUploadingFiles() {
    //   // need to call get api multiple times to get all file progress with time interval 2 sec
    //   //need to call process api after all files are uploaded

    //   // const throttled = throttle(() => {
    //   //   if (this.getUploadingFiles(this.getActiveBatchId).length) {
    //   //     this.setUploadingBatchFiles({ pbt_id: this.getActiveBatchId });
    //   //     debounced();
    //   //   }
    //   // }, 500);
    //   // throttled();
    //   const processFiles = debounce(() => {
    //     Axios.get(urls.processBatchFiles(this.getActiveBatchId));
    //   }, 1000);
    //   setInterval(() => {
    //     if (this.getUploadingFiles(this.getActiveBatchId).length) {
    //       this.setUploadingBatchFiles({ pbt_id: this.getActiveBatchId });
    //       processFiles();
    //     }
    //   }, 1000);
    // },
  },
};
</script>

<style scoped lang="css">
.plan-upload__modal__layout {
  padding: 20px 30px 39px 30px;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 610px;
  justify-content: space-between;
}
.plan-upload__modal-card {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  border: 1px solid #e7edf5;
  box-sizing: border-box;
  border-radius: 15px;
  margin-bottom: 30px;
}
.plan-upload__status-card {
  width: 100%;
  min-height: 230px;
  overflow: auto;
  margin-bottom: 30px;
}
</style>
