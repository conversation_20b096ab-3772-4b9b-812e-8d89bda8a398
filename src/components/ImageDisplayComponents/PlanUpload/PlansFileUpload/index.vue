<template>
  <div>
    <Modal
      :isCloseButton="uploadingFiles.length === 0"
      :isFooter="showFooter"
      title="Upload Plans"
      noSecondaryBtn
      primaryLabel="Proceed"
      width="500"
      @closeModal="clickCloseModal"
      @onClickButton="onClickProceed"
    >
      <template #modal-body>
        <div class="plans-progress-list">
          <div v-if="uploadingFiles.length">
            <div
              v-for="file in uploadingFiles"
              :key="file.name"
              class="plan-upload__status-card"
            >
              <div v-show="false">{{ fileProgress }}</div>

              <ProgressBar
                :progress="getFileProgress(file)"
                :fileName="file.name"
              />
            </div>
          </div>
          <div v-else class="plan-upload__card">
            <FileUpload
              sheetName="Plan Sheets"
              :allowMultipleFiles="true"
              :acceptedFileFormats="acceptedFileFormats"
              height="300"
              @onUpload="onLocalEvent"
            />
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import forEach from "lodash/forEach";
import { urls, Axios } from "/src/utils/Axios";
import axios from "axios";
import keys from "lodash/keys";
import map from "lodash/map";
import ProgressBar from "../UploadModal/UploadStatus/EachStatus/ProgressBar.vue";


export default {
  name: "PlansModal",
  components: {
    // Icon,
    ProgressBar,
  },
  props: {},
  data() {
    return {
      acceptedFileFormats: ".png,.jpg,.jpeg,.pdf",
      uploadingFiles: [],
      fileProgress: {},
      showFooter: false,
    };
  },
  computed: {
    ...mapGetters({
      getActiveBatchId: "PlanUploadSet/getActiveBatchId",
    }),
    getFileProgress() {
      return (file) => {
        console.log(file);
        return file.uploadingProgress;
      };
    },
  },
  methods: {
    ...mapActions({
      uploadPlanFilesToBatch: "PlanUploadFile/uploadPlanFilesToBatch",
      triggerPlansProcessing: "PlanUploadFile/triggerPlansProcessing",
      setAllBatchFiles: "PlanUploadFile/setAllBatchFiles",
      updateBatchDetails: "PlanUploadBatch/updateBatchDetails",
    }),

    async onLocalEvent(e) {
      const input = e.target;
      const files = input.files;
      this.uploadingFiles = files;

      forEach(this.uploadingFiles, (file) => {
        file.uploadingProgress = 0;
      });

      const data = await this.uploadPlanFilesToBatch({
        pbt_id: this.getActiveBatchId,
        file: this.uploadingFiles,
      });

      const key_file_mapper = {};
      //making a key file mapper to map the s3 key with the file
      map(data.data, (item,index) => {
        key_file_mapper[item.plf_s3_key] = this.uploadingFiles[index];
      });
      const preSignedResponse = await Axios.post(urls.getBulkS3KeyUpload(), {
        s3_keys: keys(key_file_mapper),
      });
      
      // Collect promises for all the uploads
      const uploadPromises = map(preSignedResponse.data, (item) =>
        this.uploadFileToS3(item, key_file_mapper[item.fields.key])
      );

      // Wait for all files to finish uploading
      await Promise.all(uploadPromises);
      
      
      // Show the footer once all uploads are complete
      setTimeout(() => {
        this.showFooter = true;
      }, 1000);

    

      // Optionally trigger other events after uploads
      this.triggerPlansProcessing({ pbt_id: this.getActiveBatchId });
    },

    async uploadFileToS3(preSignedResponse, file) {
      let formData = new FormData();
      let s3Fileds = preSignedResponse.fields;
      forEach(keys(s3Fileds), (key) => {
        formData.set(key, s3Fileds[key]);
      });
      formData.append("file", file);
      
      // Upload to S3 and track progress
      await axios.post(preSignedResponse.url, formData, {
        onUploadProgress: (progressEvent) => {
          file.uploadingProgress = Math.round(
            (progressEvent.loaded / progressEvent.total) * 100
          );
          this.fileProgress = {
            ...this.fileProgress,
            [file.name]: file.uploadingProgress,
          };
        },
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
    },

    clickCloseModal() {
      this.$emit("closePlansUploadModal");
    },
    async onClickProceed() {
      this.updateBatchDetails({
        pbt_id: this.getActiveBatchId,
      });
      this.setAllBatchFiles({ pbt_id: this.activeBatchId });
      this.$emit("closePlansUploadModal");
    },
  },
};
</script>

<style scoped lang="css">
/* .plan-upload__card {
 width:500px;
 height:500px;
} */
.plans-progress-list {
  height: 350px;
  overflow-y: auto;
  /* width: 600px; */
}
</style>
