<template>
  <div class="plans-upload__actions">
    <!-- <div style="cursor: pointer" @click="setUploadedFiles">
      <Icon color="baseRoyalBlue" name="refresh" />
    </div> -->
    <!-- <div class="plans-upload__actions__members">
      <div><HeaderMembers /></div>
    </div> -->
    <div class="plans-upload__actions__upload">
      <!-- <div class="plans-upload__actions__upload__icon">
        <Icon :name="'cloudUpload'" :color="'secondary'" />
      </div>
      <div class="plans-upload__actions__upload__name">Upload</div> -->
      <Button
        :label="'Upload'"
        type="baseLight4"
        textColor="secondary"
        :backgroundFill="false"
        :displayBorder="false"
        size="large"
        icon="cloudUpload"
        :isDisabled="showUploadButton"
        @onClick="clickUpload"
      />
    </div>
    <div v-if="enablePublishIcon" id="plan_publish" class="plans-upload__actions__publish" :title="'Publish'" @click="clickPublish">
        <div class="px-2 py-1">Publish</div>
        <div class="plans-upload__actions__publish__button">
          <Icon
          color="secondary"
          size="medium"
          name="publish"
          />
        </div>
      </div>
    <div v-if="enableShareIcon" id="plan_share" class="plans-share-button" :title="'Share'" @click="clickAccessModal">
      <div class="px-2 py-1">Share</div>
      <div class="plans__actions__share__button">
        <Icon
          color="secondary"
          size="medium"
          name="share"
        />
      </div>
    </div>
    <div v-if="enableDeleteIcon" id="plan_delete" class="plans-upload__actions__delete" :title="'Delete'" @click="clickDelete" >
      <div class="px-2 py-1">Delete</div>
      <div class="plans-upload__actions__delete__button">
        <Icon
          color="primary"
          size="medium"
          name="trash2"
        />
      </div>
    </div>
    <AccessModal
      v-if="showAccessShareModal"
      :prjId="prjId"
      :pcmId="pcmId"
       objectType="planset"
      :objectId="objectId"
      clickedRow={}
      :loggedInUser="userId"
      @closeModal="closeModal"
    />
  </div>
</template>

<script>
// import HeaderMembers from "./HeaderMembers.vue";
import { mapGetters } from "vuex";

import config from "../../../../config";
export default {
  name: "PlanUploadActions",
  props: {
    userId: {
      type: String,
      default: "",
    }, 
    prjId: {
      type: String,
      default: "",
    },
    pcmId: {
      type: String,
      default: "",
    },
    objectId: {
      type: String,
      default: "",
    },
    clickUpload: {
      type: Function,
      default: () => {},
    },
    clickPublish: {
      type: Function,
      default: () => {},
    },
    setUploadedFiles: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      showAccessShareModal : false
    };
  },
  computed: {
    ...mapGetters({
      getBatchPublishedStatus: "PlanUploadBatch/getBatchPublishedStatus",
      activeBatchId: "PlanUploadSet/getActiveBatchId",
      getBatchFiles: "PlanUploadFile/getBatchFiles",
      getActiveBatchOwnerId: "PlanUploadSet/getActiveBatchOwnerId",
    }),
    enableShareIcon() {
      return this.getActiveBatchOwnerId === config.getUserId() && this.getBatchPublishedStatus(this.activeBatchId);
    },
    enablePublishIcon() {
      if (
        this.getBatchPublishedStatus(this.activeBatchId) ||
        this.getBatchFiles(this.activeBatchId).length === 0
      ) {
        return false;
      } else {
        return true;
      }
    },
    enableDeleteIcon() {
      if (this.getBatchPublishedStatus(this.activeBatchId)) {
        return false;
      } else {
        return true;
      }
    },
    disableShareButton() {
      return this.getBatchPublishedStatus(this.activeBatchId);
    },
    showUploadButton() {
      return this.getBatchPublishedStatus(this.activeBatchId);
    },
  },
  methods: {
    clickDelete() {
      this.$emit("clickDelete", this.getBatchFiles(this.activeBatchId).length);
    },
    clickAccessModal(){
      this.showAccessShareModal = true;
    },
    closeModal() {
      this.showAccessShareModal = false;
    }
  },

};
</script>

<style lang="scss" scoped>

.plans-share-button {
    padding: 10px;
    cursor: pointer;
}

.plans-upload__actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 320px;
  padding-right: 20px;
  height: 100%;
  &__members {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &__upload {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 0px 20px;
    // &__name {
    //   font-family: Lato;
    //   font-style: normal;
    //   font-weight: bold;
    //   font-size: 18px;
    //   line-height: 24px;
    //   display: flex;
    //   align-items: center;
    //   color: #2b446c;
    //   padding-left: 5px;
    // }
  }
  &__publish {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 0px 20px;
  }
  &__delete {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 0px 20px;
  }
  
}
</style>
