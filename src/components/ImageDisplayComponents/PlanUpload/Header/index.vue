<template>
  <div class="page-upload-header">
    <HeaderTitle />
    <HeaderActions
      :prjId="prjId"
      :objectId="objectId"
      :pcmId="pcmId"
      :userId="userId"
      :clickUpload="clickUpload"
      :clickPublish="clickPublish"
      :setUploadedFiles="setUploadedFiles"
      @clickDelete="clickDeleteSet"
    />
  </div>
</template>

<script>
import HeaderTitle from "./HeaderTitle.vue";
import HeaderActions from "./HeaderActions.vue";
export default {
  name: "PlanUploadHeader",
  components: {
    HeaderTitle,
    HeaderActions,
  },
  props: {
    clickUpload: {
      type: Function,
      default: () => {},
    },
    clickPublish: {
      type: Function,
      default: () => {},
    },
    setUploadedFiles: {
      type: Function,
      default: () => {},
    },
    userId: {
      type: String,
      default: "",
    },
    prjId: {
      type: String,
      default: "",
    },
    objectId: {
      type: String,
      default: "",
    },
    pcmId: {
      type: String,
      default: "",
    },
  },
  methods: {
    clickDeleteSet(filesLength) {
      this.$emit("deleteSet", filesLength);
    },
  },
};
</script>

<style lang="scss" scoped>
.page-upload-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  border-bottom: 1px solid #e7edf5;
}
</style>
