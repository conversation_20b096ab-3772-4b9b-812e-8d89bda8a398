<template>
  <div class="plans-upload__header">
    <div class="plans-upload__header__title">
      <div>{{ setTitle(activeBatchId) }} - {{ setKey(activeBatchId) }}</div>
    </div>
    <div class="plans-upload__header__details">
      <div class="plans-upload__header__details__time">
        <div class="plans-upload__header__details__items" >
          Created By : 
          {{ getUserName }}
        </div>
        <div class="plans-upload__header__details__items">
          Created on :
          {{ batchCreatedDate }}
        </div>
      </div>
      <div class="plans-upload__header__details__status">
        <div class="plans-upload__header__details__status__key">Status :</div>
        <div class="plans-upload__header__details__status__value">
          {{ setStatus(activeBatchId) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "PlanUploadHeader",
  computed: {
    ...mapGetters({
      setTitle: "PlanUploadBatch/getBatchTitle",
      setKey: "PlanUploadBatch/getBatchSetPrefix",
      setStatus: "PlanUploadBatch/getBatchStatus",
      lastUpdatedTime: "PlanUploadBatch/getBatchLastUpdated",
      getBatchCreatedDate: "PlanUploadBatch/getBatchCreatedDate",
      activeBatchId: "PlanUploadSet/getActiveBatchId",
      getUploadFullName: "PlanUploadBatch/getUploadFullName",
    }),
    getUserName() {
      return this.getUploadFullName(this.activeBatchId);
    },
    batchCreatedDate() {
      const time = this.getBatchCreatedDate(this.activeBatchId);
      return this.$moment(time).format("DD/MM/YYYY");
    },
  },
};
</script>

<style lang="scss" scoped>
.plans-upload__header {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  height: 50px;
  &__title {
    font-weight: bold;
    font-size: 18px;
    line-height: 24px;
    display: flex;
    align-items: center;
    color: #2b446c;
  }
  &__details {
    display: flex;
    align-items: center;
    &__time {
      font-weight: normal;
      font-size: 14px;
      line-height: 16px;
      display: flex;
      align-items: center;
      color: #2b446c;
    }
    &__items {
        padding-right: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    &__status {
      display: flex;
      align-items: center;
      &__key {
        font-size: 14px;
        line-height: 18px;
        display: flex;
        align-items: center;
        color: #809fb8;
      }
      &__value {
        font-weight: bold;
        font-size: 14px;
        line-height: 18px;
        display: flex;
        align-items: center;
        color: #2b76e7;
        padding-left: 5px;
      }
    }
  }
}
</style>
