<template>
  <div class="plan-upload__header_members">
    <div class="uploadedBy"  :key="index">
      <!-- <Avatar backgroundColor="secondary" :firstNameInitials="getUserName" /> -->
       {{ (getUploadFullName(getActiveBatchId)) }}
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import toUpper from "lodash/toUpper";
export default {
  name: "",
  data() {
    return {};
  },
  computed: {
    ...mapGetters({
      members: "PlanUploadBatch/getUploadUser",
      getUploadFullName: "PlanUploadBatch/getUploadFullName",
      getActiveBatchId: "PlanUploadSet/getActiveBatchId",
    }),
    getUserName() {
      return toUpper(this.getUploadFullName(this.getActiveBatchId));
    },
  },
  methods: {},
};
</script>

<style scoped lang="scss">
.plan-upload__header_members {
  display: flex;
  align-items: center;
}
.uploadedBy {
  font-weight: bold;
  font-size: 14px;
  line-height: 16px;
  display: flex;
  align-items: center;
  color: #2b446c;
}
</style>
