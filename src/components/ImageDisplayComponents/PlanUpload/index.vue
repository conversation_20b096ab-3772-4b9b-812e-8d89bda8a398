<template>
  <div class="plans-upload">
    <div class="plans-upload__content">
      <div class="plans-upload__content__header">
        <div>
          <Header
          :prjId="prjId"
          :objectId="psmId"
          :pcmId="pcmId"
          :userId="userId"
            :clickUpload="clickUpload"
            :clickPublish="clickPublish"
            :setUploadedFiles="setUploadedFiles"
            @deleteSet="clickDelete"
          />
        </div>
      </div>
      <div class="plans-upload__content__body">
        <div v-if="showList">
          <PlansList ref="batchPlansList"
            :clickUpload="clickUpload"
            :handleUploadedFiles="setUploadedFiles"
          />
        </div>
      </div>
      <div v-if="openUploadModal">
        <div>
          <UploadModal v-if="false"
            :clickCloseModal="clickCloseModal"
            :setUploadedFiles="setUploadedFiles"
          />
          <PlansFileUpload
          @closePlansUploadModal="closePlansUploadModal"
          />
        </div>
      </div>
      <div v-if="openPublishConfirmationModel">
        <div>
          <PublishConfirmation
            :clickCloseModal="clickClosePublishModal"
            :clickConfirmModal="confirmPublish"
          />
        </div>
      </div>
      <div v-if="openDeleteConfirmationModel">
        <div>
          <PublishConfirmation
            :clickCloseModal="clickClosePublishModal"
            :clickConfirmModal="deleteSetFunction"
            modalTitle="Confirm Delete"
            label="Delete"
            content="Are you sure you want to delete this set?"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Header from "./Header";
import PlansList from "./PlansList";
import UploadModal from "./UploadModal";
import config from "../../../config";
import PublishConfirmation from "./PublishConfirmation";
import { mapActions } from "vuex";
import PlansFileUpload from "./PlansFileUpload";

export default {
  name: "Plansupload",
  components: {
    Header,
    PlansList,
    UploadModal,
    PlansFileUpload,
    PublishConfirmation,
  },
  props: {
    prjId: {
      type: String,
      required: true,
    },
    pbtId: {
      type: String,
      required: true,
    },
    psmId: {
      type: String,
      required: true,
    },
    onPublish: {
      type: Function,
      required: true,
    },
    onDelete: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      file: null,
      title: "This is Plans Upload",
      openUploadModal: false,
      openPublishConfirmationModel: false,
      showList: true,
      openDeleteConfirmationModel: false,
      pcmId: "",
      userId: "",
    };
  },
  computed: {},
  async mounted() {
    this.pcmId = config.getProjectCompanyId();
    this.userId = config.getUserId();
    await this.setProjectBatches({ prj_id: this.prjId , pcm_id : config.getProjectCompanyId() });
    this.setExistingSets({ prj_id: this.prjId });
    this.setActiveBatch({ pbt_id: this.pbtId });
    this.setActiveSet({ psm_id: this.psmId });
    await this.setAllBatchFiles({ pbt_id: this.pbtId });
    // if(this.showList){
    //   this.$refs.batchPlansList.setLoadertoFalse();
    // }
  },
  methods: {
    ...mapActions({
      testPlans: "PlanUploadSet/createSet",
      setProjectBatches: "PlanUploadBatch/setProjectBatches",
      setExistingSets: "PlanUploadSet/setExistingSets",
      setUploadingBatchFiles: "PlanUploadFile/setUploadingBatchFiles",
      setActiveBatch: "PlanUploadSet/setActiveBatch",
      setActiveSet: "PlanUploadSet/setActiveSet",
      uploadFilesToBatch: "PlanUploadFile/uploadFilesToBatch",
      setAllBatchFiles: "PlanUploadFile/setAllBatchFiles",
      publishBatch: "PlanUploadBatch/publishBatch",
      deleteBatch: "PlanUploadBatch/deleteBatch",
    }),
    clickUpload() {
      this.openUploadModal = true;
    },
    clickCloseModal() {
      this.openUploadModal = false;
    },
    clickClosePublishModal() {
      this.openPublishConfirmationModel = false;
      this.openDeleteConfirmationModel = false;
    },
    clickPublish() {
      this.openPublishConfirmationModel = true;
    },
    async deleteSetFunction() {
      const response = await this.deleteBatch({
        pbt_id: this.pbtId,
        prj_id: this.prjId,
      });
      if (response.status === 204) {
        this.$toast.success("Set Deleted Successfully");
        await this.setExistingSets({ prj_id: this.prjId });
        this.onDelete();
      } else {
        this.$toast.error(response.data["message"]);
      }
      this.openDeleteConfirmationModel = false;
    },
    clickDelete(filesLength) {
      if (filesLength === 0) {
        this.deleteSetFunction();
      } else {
        this.openDeleteConfirmationModel = true;
      }
    },
    async confirmPublish() {
      const response = await this.publishBatch({
        pbt_id: this.pbtId,
        prj_id: this.prjId,
      });
      if (response.status === 200) {
        this.$toast.success("Sheets are Published");
        this.onPublish();
      } else {
        this.$toast.error(response.data["message"]);
      }
      this.openPublishConfirmationModel = false;
    },
    setUploadedFiles() {
      this.setAllBatchFiles({ pbt_id: this.pbtId });
      this.showList = false;
      setTimeout(() => {
        this.showList = true;
      }, 10);
    },
    closePlansUploadModal() {
      this.openUploadModal = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.plans-upload {
  width: 100%;
  height: 100%;

  &__content {
    width: 100%;
    height: 100%;
    padding: 10px;
    border-radius: 15px;
    background: white;
    &__body {
      background: #ffffff;
      border: 1px solid #e7edf5;
      box-sizing: border-box;
      border-radius: 15px;
      height: 90%;
      margin: 10px 10px 20px 10px;
    }
  }
}
</style>
