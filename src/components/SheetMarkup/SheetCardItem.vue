<template>
  <Box :columnSize="2">
    <template #body>
      <AlertWrapper :objectId="getplfId">
        <div class="card-items">
          <Box :columnSize="2">
            <template #body>
              <div class="card-top">
                <img
                  :src="getThumbnail"
                  alt="plan-thumbnail"
                  @click="$emit('clickCard', cardData)"
                />
              </div>
            </template>
          </Box>
          <Box :columnSize="2">
            <template #body>
              <div class="card-middle" @click="$emit('clickCard', cardData)">
                <div class="middle-item">
                  <div class="plan-code">
                    <p :title="getPlanName" class="plan-name">
                    {{ getPlanCode}} &nbsp; &nbsp;  {{ getPlanName }}
                  </p>
                  </div>
                  <div class="key-value">
                    <div class="middle-card-data-key">
                    <div class="">
                      Set Name :
                    </div>
                    <div class="">
                      Added By:  
                    </div>
                  </div>
                  <div class="middle-card-data-value">
                    <div class="">
                      {{ getPlanSetName }}
                    </div>
                    <div class="">
                      {{ getAddedBy }}
                    </div>
                  </div>
                  </div>
                </div>
                <div
                  v-if="false"
                  class="middle-item remove"
                  @click="$emit('remove', getplfId)"
                >
                  <Icon
                    name="trash"
                    size="huge"
                    color="baseLight"
                    class="remove"
                  />
                </div>
              </div>
            </template>
          </Box>
          <Box :columnSize="3">
            <template #body>
              <div class="card-bottom" @click="openVersion">
                <Icon
                  name="eye"
                  size="tiny"
                  color="baseLight"
                  class="eye-icon"
                />
                <small :class="['bottom-item', isVersionHighlight ? 'highlight-version' : '']">Versions ({{ getVersionCount }})</small>
                <p class="update">{{ getPlanCreatedOn }}</p>
              </div>
            </template>
          </Box>
        </div>
      </AlertWrapper>
    </template>
  </Box>
</template>

<script>
import get from "lodash/get";
export default {

  props: {
    cardData: {
      type: Object,
      required: true,
    },
  },

  data() {
    return {};
  },
  computed: {
    updatedFromNow() {
      return this.$moment(this.getLastUpdated).fromNow();
    },
    getPlanCode() {
      return get(this.cardData, "plf_sheet_number", "");
    },
    getPlanName() {
      return get(this.cardData, "plf_sheet_name", "");
    },
    getVersions() {
      return get(this.cardData, "versions", []);
    },
    getThumbnail() {
      return get(this.cardData, "thumbnail_url", "");
    },
    getLastUpdated() {
      return get(this.cardData, "plf_updated_on", "");
    },
    getplfId() {
      return get(this.cardData, "plf_id", "");
    },
    getAddedBy() {
      return get(this.cardData, "plf_uploaded_by_name", "");
    },
    isVersionHighlight() {
      let versions = get(this.cardData, "plf_sheet_version_count", [])
      return versions > 1;
    },
    getVersionCount() {
      return get(this.cardData, "plf_sheet_version_count", 1);
    },
    getPlanSetName() {
      return get(this.cardData, "planset_name", "");
    },
    getPlanCreatedOn() {
      let date = get(this.cardData, "plf_created_on", "");
      return this.$moment(date).format("DD/MM/YYYY");
    },
  },
  mounted() {},
  methods: {
    openVersion() {
      this.$emit("onVersionsClicked", this.getPlanCode);
    },
  },
};
</script>

<style lang="scss" scoped>
.plan-code{
  width: 100%;
  display: flex;
  justify-content: flex-start;
  font-weight: 600;
}
.key-value{
  display: flex;
  justify-content: flex-start;
  width: 100%;
}
.card-items {
  // width: 300px;
  //height: 340px;
  box-sizing: border-box;
  margin: 10px;
  border-radius: 16px;
  background: #ffffff;

  .card-top {
    img {
      cursor: pointer;
      // width: 300px;
      width: 100%;
      height: 17vh;
      border-top-right-radius: 16px;
      border-top-left-radius: 16px;
    }
  }

  .card-middle {
    color: #06152b;
    // padding: 15px 20px 0px 20px;
    height: 75px;
    display: flex;

    .middle-item {
      cursor: pointer;
      display: flex;
      align-items: center;
      width: 100%;
      margin-left: 10px;
      flex-direction: column
    }
    .middle-card-data-key,
    .middle-card-data-value {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 70%;
      font-size: 13px;
      font-family: Lato;
    }
    .middle-card-data-key {
      //styleName: Caption;
      font-weight: 500;
      text-align: left;
      margin-left: 5px;

    }
    .middle-card-data-value {
      margin-left: 20px;
      font-weight: 500;
      text-align: left;
      color: #809FB8;
    }
    .remove {
      margin-left: auto;
      margin-bottom: 10px;
    }
    .plan-name {
      // width: 200px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 10px 5px;

    }
    .sheet-name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 10px;
    }
  }

  .card-bottom {
    height: 35px;
    // margin: 0 10px;
    padding: 10px 10px;
    margin-top: 5px;
    border-top: 1px solid #e7edf5;
    font-family: Lato;
    font-style: normal;
    font-weight: normal;
    color: #adc1d1;

    .eye-icon {
      float: left;
      margin-right: 5px;
      margin-top: 2px;
    }

    .update {
      float: right;
      width: 117px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-left: 30px;
    }
    .bottom-item {
      display: inline-block;
      cursor: pointer;
      margin-bottom: 10px;
    }
  }
  .highlight-version {
    font-weight: bolder;
    color: #06152b;
  }
}
</style>
