<template>
  <div class="feed-header">
    <Grid :columns="12">
      <Box :columnSize="3">
        <template #body>
          <div class="sheet-header-navigation">
            <div
              v-if="onBackButtonClick"
              class="back-button"
              @click="onBackButtonClick"
            >
              <Icon name="arrowBackOutline" color="secondary" />
            </div>
            <div v-if="onPlanChange === undefined" class="feed-title">
              {{ sheetName }}
            </div>
            <div v-else class="sheet-dropdown">
              <Select
                v-model="getCurrentSheet"
                :searchable="true"
                :border="false"
                size="small"
                responsive
                maxDropdownHeight="400px"
                :options="getSheetDropdownOptions"
                placeholder="Select Sheet"
                @change="onSheetChange"
              />
            </div>
          </div>
        </template>
      </Box>
      <Box :columnSize="2">
        <template #body>
          <div class="feed-location">
            <div
              v-if="
                getCurrentMarkupMode == markupModes.DRAFT ||
                getmarkupDrodownOptions.length === 0
              "
              class="markup-title"
            >
              Untitled Markup
            </div>
            <Select
              v-else
              v-model="getCurrentMarkupId"
              :border="false"
              :searchable="true"
              responsive
              size="small"
              maxDropdownHeight="150px"
              :options="getmarkupDrodownOptions"
              placeholder="Select Markup"
              @change="onSelected($event)"
            />
          </div>
        </template>
      </Box>
      <Box :columnSize="7">
        <template #body>
          <div class="plan-markup-actions">
            <div
              v-if="

                userId == getMarkupCreator &&
                getCurrentMarkupMode === markupModes.READONLY &&
                getCurrentMarkupId !== draftMarkupId
              "
              class="feed-actions"
            >
              <Button
                type="secondary"
                icon="editNew"
                label="Edit Markup"
                @onClick="onEditMarkup"
              />
            </div>
            <div
              v-if="

                (getCurrentMarkupMode === markupModes.DRAFT ||
                  getCurrentMarkupMode === markupModes.EDIT)
              "
              class="feed-actions"
            >
              <Button
                type="primary"
                label="Save Markup"
                :isDisabled="disableSaveButton"
                @onClick="onSavePlanMarkup"
              />
            </div>
            <div v-if="false" class="feed-actions">
              <Button type="primary" isDisabled label="Share" />
            </div>
            <div class="feed-actions">
              <Button
                class="mr-2"
                type="secondary"
                icon="compare"
                label="Compare"
                @onClick="onCompareMarkup"
              />
              <Button
                v-if="

                  getCurrentMarkupMode === markupModes.READONLY
                "
                class="feed-actions"
                type="primary"
                icon="plus"
                label="Add Markup"
                :isDisabled="buttonDisabled"
                @onClick="onAddNewMarkup"
              />
            </div>
          </div>
        </template>
      </Box>
    </Grid>
  </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import { DRAFT_MARKUP, PLAN_MARKUP_MODES } from "@/constants";
import keys from "lodash/keys";
import values from "lodash/values";
import map from "lodash/map";
import get from "lodash/get";
import forEach from "lodash/forEach";
export default {
  name: "SheetMarkupHeader",
  props: {
    sheetName: {
      type: String,
      default: "",
    },
    prjId: {
      type: String,
      required: true,
    },
    sheetCode: {
      type: String,
      default: "",
    },
    sheetUploadedBy: {
      type: String,
      default: "",
    },
    sheetId: {
      type: String,
      default: "",
    },
    onMarkupSelection: {
      type: Function,
      required: true,
    },
    onPlanChange: {
      type: Function,
      default: undefined,
    },
    onBackButtonClick: {
      type: Function,
      required: true,
    },
    userId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      selectedValue: "",
      sheetOptions: {},
      isMarkupCreator: false,
      markupModes: PLAN_MARKUP_MODES,
      draftMarkupId: DRAFT_MARKUP.id,
      buttonDisabled: true,
    };
  },
  computed: {
    ...mapGetters({
      getMarkupObjectsByPlmId: "MarkupCanvas/getMarkupObjectsByPlmId",
      getMarkupObjectById: "MarkupCanvas/getMarkupObjectById",
      getMarkupIdsBySheetId: "MarkupCanvas/getMarkupIdsBySheetId",
      getPlanMarkupById: "MarkupCanvas/getPlanMarkupById",
      getCurrentMarkupId: "MarkupCanvas/getCurrentMarkupId",
      getCurrentMarkupMode: "MarkupCanvas/getCurrentMarkupMode",
      getModifiedMarkupJson: "MarkupCanvas/getModifiedMarkupJson",
      getMarkupCreator: "MarkupCanvas/getMarkupCreator",
      getSheetList: "MarkupCanvas/getSheetList",
      //getMarkupIdsByType: "PlanMarkup/getMarkupIdsByType",
    }),
    getMarkupIds() {
      const ids = [...this.getMarkupIdsBySheetId(this.sheetId)];
      return ids;
    },
    getmarkupDrodownOptions() {
      const options = map(this.getMarkupIds, (id) => {
        return {
          label: get(this.getPlanMarkupById(id), "plm_name", ""),
          value: id,
        };
      });
      return options;
    },
    getCurrentSheet() {
      return this.getSheetDropdownOptions.length > 0 ? this.sheetId : null;
    },

    getSheetDropdownOptions() {
      const options = this.getSheetList;
      forEach(options, (sheet) => {
        this.sheetOptions[sheet.id] = {
          label: sheet.label,
          value: sheet.id,
          plf_sort_order: sheet.plf_sort_order,
        };
      });
      // sorting the data
      const sortedOptions = values(this.sheetOptions);

      // sort by plf_sort_order
      sortedOptions.sort((a, b) => a.plf_sort_order - b.plf_sort_order);
      return sortedOptions;
    },

    disableSaveButton() {
      return keys(this.getModifiedMarkupJson.elements).length === 0;
    },
  },
  mounted() {
    this.getSheetListAction(this.prjId); //calling api to get all list of sheets

    const currentMarkupId = this.getCurrentMarkupId;
    if (currentMarkupId) {
      const planMarkup = this.getPlanMarkupById(currentMarkupId);
      this.selectedValue = get(planMarkup, "plm_name") || "Untitled";
    }

    setTimeout(() => {
      this.buttonDisabled = false;
    }, 3000);
  },
  methods: {
    ...mapActions({
      setCurrentMarkupId: "MarkupCanvas/setCurrentMarkupId",
      getSheetListAction: "MarkupCanvas/getSheetLists",
    }),
    onSavePlanMarkup() {
      this.$emit("onSavePlanMarkup");
    },
    onSelected(selectedValue) {
      this.selectedValue =
        get(this.getPlanMarkupById(selectedValue), "plm_name") || "Untitled";
      this.setCurrentMarkupId(selectedValue);
      this.$emit("triggerMarkupChange", selectedValue);
      // this.onMarkupSelection(selectedValue, PLAN_MARKUP_MODES.READONLY);
    },
    onSheetChange(selectedId) {
      const currentSheedData = this.getSheetList[selectedId];
      // this.onPlanChange(currentSheedData);
      this.$emit("triggerPlanChange", currentSheedData);
    },

    onAddNewMarkup() {
      // this.onMarkupSelection(DRAFT_MARKUP.id, PLAN_MARKUP_MODES.DRAFT);
      this.$emit("onAddNewMarkup");
    },
    onEditMarkup() {
      // this.onMarkupSelection(this.getCurrentMarkupId, PLAN_MARKUP_MODES.EDIT);
      this.$emit("onEditMarkup");
    },
    onCompareMarkup() {
      this.$emit("onClickSheetCompare");
    },
  },
};
</script>

<style scoped lang="scss">
.feed-header {
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #2b446c;
  border-bottom: 1px solid #adc1d1;
  border-radius: 6px 6px 0px 0px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 40px;
  background: white;
  padding-top: 5px;
  .sheet-header-navigation {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 5px;
    padding-top: 5px;
    .back-button {
      cursor: pointer;
    }
    .feed-title {
      font-weight: 700;
      padding: 2px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      text-transform: capitalize;
    }
    .sheet-dropdown {
      padding: 0px 10px;
      padding-bottom: 5px;
      white-space: nowrap;
      text-overflow: ellipsis;
      width: 100%;
    }
  }

  .feed-created-by {
    border-left: 1px solid #adc1d1;
    padding: 7px 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .feed-location {
    border-left: 1px solid #adc1d1;
    padding: 5px;
    // white-space: nowrap;
    // overflow: hidden;
    text-overflow: ellipsis;
    .markup-title {
      padding: 7px 10px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .plan-markup-actions {
    display: flex;
    justify-content: flex-end;
    cursor: pointer;
  }
  .feed-actions {
    display: flex;
    justify-content: flex-end;
    padding: 3px 10px 5px 0px;
    cursor: pointer;
  }
}
</style>
