// headerConfig.js
import { GRID_V2_COLUMN_TYPE } from '@/constants';

export const headerConfig = [
  {
    label: 'S.No',
    field: '',
    type: GRID_V2_COLUMN_TYPE.SEQUENCE,
  },
  {
    label: 'Sheet Number',
    field: 'plf_sheet_number',
    type: GRID_V2_COLUMN_TYPE.SLOT,
    customProps: {
      internalDataType: GRID_V2_COLUMN_TYPE.SHORT_TEXT,
    },
    width: '150px',
  },
  {
    label: 'Set Name',
    field: 'planset_name',
    type: GRID_V2_COLUMN_TYPE.SHORT_TEXT,
    customProps: {
      treatAsSelectFilter: true,
    },
    width: '150px',
  },
  {
    label: 'Sheet Name',
    field: 'plf_sheet_name',
    type: GRID_V2_COLUMN_TYPE.SLOT,
    customProps: {
      internalDataType: GRID_V2_COLUMN_TYPE.SHORT_TEXT,
    },
    width: '700px',
  },
  {
    label: 'Versions',
    field: 'plf_sheet_version_count',
    type: GRID_V2_COLUMN_TYPE.SLOT,
    customProps: {
      internalDataType: GRID_V2_COLUMN_TYPE.NUMBER,
    },
    width: '100px',
  },
  {
    label: 'Created On',
    field: 'plf_created_on',
    type: GRID_V2_COLUMN_TYPE.DATE,
    customProps: { inputFormat: 'YYYY-MM-DD' },
    width: '150px',
  },
  {
    label: 'Created By',
    field: 'plf_uploaded_by_name',
    type: GRID_V2_COLUMN_TYPE.SHORT_TEXT,
    customProps: {
      treatAsSelectFilter: true,
    },
    width: '150px',
  },
  {
    label: 'Discipline',  
    field: 'plf_sheet_tag_name',
    type: GRID_V2_COLUMN_TYPE.SHORT_TEXT,
    customProps: {
      treatAsSelectFilter: true,
    },
    width: '150px',
  },
  {
    label: 'Sheet Thumbnail',
    field: 'thumbnail_url',
    type: GRID_V2_COLUMN_TYPE.SLOT,
    width: '150px',
  },
];

export default { headerConfig };
