<template>
  <div>
    <SheetList
      :prjId="prjId"
      :componentView="componentView"
      :onVersionSelection="onVersionSelection"
      @OpenSheetMarkup="OpenSheetMarkup"
    />
    <SheetUpload
      v-if="plansUploadModal"
      :prjId="prjId"
      :openPlansUploadComponent="openPlansUploadComponent"
      @closeModal="closePlansUploadModal"
    />
  </div>
</template>

<script>
import SheetList from "./SheetList.vue";
import SheetUpload from "@/components/ImageDisplayComponents/PlanList/SheetUpload";
import { mapActions } from "vuex";

export default {
  name: "PlanSheetList",
  components: {
    SheetList,
    SheetUpload,
  },
  props: {
    prjId: {
      type: String,
      required: true,
    },
    openPlansUploadComponent: {
      type: Function,
      required: true,
    },
    componentView: {
      type: String,
      default:'gallery'
    },
    onVersionSelection: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      plansUploadModal: false,
    };
  },
  async mounted() {
    await this.setExistingSets({ prj_id: this.prjId });
  },

  methods: {
    ...mapActions({
      setExistingSets: "PlanUploadSet/setExistingSets",
    }),
    openPlansUploadModal() {
      this.plansUploadModal = true;
    },
    closePlansUploadModal() {
      this.plansUploadModal = false;
    },
    OpenSheetMarkup(sheetId) {
      this.$emit("OpenSheetMarkup", sheetId);
    },
  },
};
</script>
