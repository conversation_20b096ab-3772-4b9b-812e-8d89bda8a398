<template>
  <Grid>
    <VersionModal
      v-if="showVersionModal"
      :prjId="prjId"
      :sheetName="currentSheetName"
      :onVersionSelection="onVersionSelection"
      @OpenSheetMarkup="openSheet"
      @onDeletePlanSheet="onDeletePlanSheet"
      @closeModal="showVersionModal = false"
    />
    <AttachmentModal
      v-if="selectedAttachment"
      :src="selectedAttachment"
      @closeShowModal="onCloseAttachmentModal"
    />
    <Box
      v-if="componentView === 'gallery'"
      bgcolor="baseLight4"
      borderRadius="big"
      :columnSize="12"
      class="plan-list-table"
    >
      <template #body>
        <SmartTable
          ref="plans_sheet_list_view"
          tblId="plans_sheet_list_view"
          :userId="userId"
          :height="getHeight"
          :uniqueKeys="uniqueKeys"
          :headerConfig="headerConfig"
          :dataOptions="dataOptions"
          :showSidePane="true"
          :setKanbanCardsData="setKanbanCardsData"
          noDataPrimaryTitle="No Plans Found"
          noDataTitle="Upload plans from the 'Plan Set' menu."
          :componentView="componentView"
          :showFilter="false"
          :showDownload="false"
        >
          <template
            v-for="cardData in normalizedCardsData"
            :slot="cardData.id"
            slot-scope="slotData"
          >
            <div :key="cardData.id" class="card-item">
              <CardItem
                :cardData="slotData.slotData"
                @onVersionsClicked="openVersionModal"
                @clickCard="openSheet"
              />
            </div>
          </template>
          <template slot="plf_updated_on" slot-scope="{ columnData }">
            <div class="created-date">
              {{ getDateFormatted(columnData.data) }}
            </div>
          </template>
          <template slot="plf_sheet_number" slot-scope="{ rawData }">
            <div class="sheet-number" @click="openSheet(rawData)">
              {{ rawData.plf_sheet_number }}
            </div>
          </template>
          <template slot="thumbnail_url" slot-scope="{ columnData }">
            <div class="plans-image" @click="openPlan(columnData.data)">
              <img
                class="table-cell-image"
                :src="columnData.data"
                alt="plans"
              />
            </div>
          </template>
        </SmartTable>
      </template>
    </Box>
    <div v-else>
      <SpreadSheetDataWrapper
        ref="plans_sheet_list_view"
        tblId="plans_sheet_list_view"
        :params="tableParams"
        :columns="header"
        :prjId="prjId"
      >
        <template #spreadsheet="{ spreadsheetData, columns }">
          <div class="sheetWrapper">
            <SpreadSheetV2
              id="plans_sheet_list_view"
              ref="plans_sheet_list_view_v2"
              :key="columns.length"
              :headerActionsConfig="{
                displayHeaderActions: true,
                topBar: false,
                filter: true,
              }"
              :data="modifyData(spreadsheetData)"
              :header="columns"
              tbleId="plans_sheet_list_view"
              :dataOptions="dataOptions"
              :userId="userId"
            >
              <template slot="plf_sheet_number" slot-scope="{ rowData }">
                <div class="sheet-number" @click="openSheet(rowData)">
                  <TableRedirection
                    :text="rowData.plf_sheet_number"
                    @click.native="openSheet(rowData)"
                  />
                </div>
              </template>
              <template slot="plf_sheet_name" slot-scope="{ rowData }">
                <div class="">
                  <TableRedirection
                    :text="rowData.plf_sheet_name"
                    @click.native="openSheet(rowData)"
                  />
                </div>
              </template>
              <template slot="thumbnail_url" slot-scope="{ rowData }">
                <div
                  class="list-plans-image"
                  @click="openPlanSheetImg(rowData.plf_sheet_signed_url)"
                >
                  <img
                    class="table-cell-image"
                    :src="rowData.thumbnail_url"
                    alt="plans"
                  />
                </div>
              </template>
              <template slot="plf_sheet_version_count" slot-scope="{ rowData }">
                <div class="sheet-number">
                  <TableRedirection
                    :text="JSON.stringify(rowData.plf_sheet_version_count)"
                    @click.native="openVersionModal(rowData.plf_sheet_number)"
                  />
                </div>
              </template>
              <!-- <template #body> </template> -->
            </SpreadSheetV2>
          </div>
        </template>
      </SpreadSheetDataWrapper>
    </div>
  </Grid>
</template>

<script>
import { mapActions } from "vuex";
import plansConfig from "/src/config";
import debounce from "lodash/debounce";
import config from "../../../config";
import CardItem from "../SheetCardItem.vue";
import VersionModal from "@/components/ImageDisplayComponents/PlanList/Modals/VersionModal.vue";
import get from "lodash/get";
import TableRedirection from "@/components/tableRedirection/index.vue";
import { headerConfig as headerTableConfig } from "./config";

export default {
  components: {
    CardItem,
    VersionModal,
    TableRedirection,
  },
  props: {
    prjId: {
      type: String,
      required: true,
    },
    componentView: {
      type: String,
      required: true,
    },
    onVersionSelection: {
      type: Function,
      default: () => {},
    }
  },
  data() {
    return {
      tableHeight: window.innerHeight,
      normalizedCardsData: [],
      uniqueKeys: ["plf_id"],
      selectedAttachment: null,
      header: headerTableConfig,
      headerConfig: {
        plf_updated_on: {
          component: "slot",
        },
        plf_sheet_number: {
          component: "slot",
        },
        thumbnail_url: {
          component: "slot",
        },
      },
      showVersionModal: false,
      currentSheetName: "",
    };
  },
  computed: {
    dataOptions() {
      return {
        user_id: this.userId,
        prj_id: this.prjId,
        pcm_id: config.getProjectCompanyId(),
        latest: true,
      };
    },
    getHeight() {
      return this.tableHeight - 190 + "px";
    },
    tableParams() {
      return {
        latest: true,
        user_id: this.userId,
        prj_id: this.prjId,
        pcm_id: config.getProjectCompanyId(),
      };
    },
  },

  created() {
    this.userId = plansConfig.getUserId();
    window.addEventListener(
      "resize",
      debounce(() => {
        this.tableHeight = window.innerHeight;
      }, 150)
    );
  },
  beforeDestroy() {
    window.removeEventListener(
      "resize",
      () => (this.tableHeight = window.innerHeight)
    );
  },
  beforeCreate() {
    const baseUrl = plansConfig.getBaseUrl();
    this.$designComponentsConfig.init(
      `${baseUrl}/api/v1`,
      plansConfig.getAccessToken(),
      plansConfig.getUserId() ,plansConfig.getProjectId(), plansConfig.getProjectCompanyId()
    );
  },
  methods: {
    ...mapActions({
      setSheetMeta: "MarkupCanvas/setSheetMeta",
      deletePlanSheet: "MarkupCanvas/deletePlanSheet",
    }),
    async openSheet(raw) {
      //this.$emit("openSheet", raw);
      //await this.setSheetMeta({ sheetId: raw.plf_id, metaData: raw });
      this.$emit("OpenSheetMarkup", raw);
    },
    getDateFormatted(date) {
      return this.$moment(date).format("MM/DD/YYYY");
    },
    openPlan(plf_sheet_signed_url) {
      window.open(plf_sheet_signed_url);
    },
    setKanbanCardsData(data) {
      this.normalizedCardsData = data;
    },
    openPlanSheetImg(plf_sheet_signed_url) {
      this.selectedAttachment = plf_sheet_signed_url;
    },
    onCloseAttachmentModal() {
      this.selectedAttachment = null;
    },
    openVersionModal(sheetData) {
      this.currentSheetName = sheetData;
      this.showVersionModal = true;
    },
    getUserId() {
      return this.userId;
    },
    modifyData(data) {
      return data;
    },
    async onDeletePlanSheet(sheetId) {
      const response = await this.deletePlanSheet(sheetId);
      if (get(response, "status") === 200) {
        this.$toast.success("Sheet deleted successfully");
        this.$refs.plans_sheet_list_view.fetchTableData(); // to refresh the table
        this.showVersionModal = false;
      } else {
        console.log("error", response);

        this.$toast.error(
          get(response, "response.data.message") || 
          get(response, "response.data.detail") || 
          "Something went wrong"
        );
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.sheetWrapper {
  height: calc(100vh - 215px);
  width: calc(100vw - 100px);
}

.plan-list-table {
  height: 100%;
  overflow: auto;
}
.card-item {
  box-sizing: border-box;
  margin: 5px;
  border-radius: 16px;
  background: #ffffff;
  //shadow
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  border: 1px solid #dccece;
}
.plans-image {
  width: 7rem;
  height: 2.25 rem;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3px 20px;
  cursor: pointer;
}
.table-cell-image {
  height: 22 px;
}
.created-date {
  padding-left: 40px;
}
.list-plans-image {
  width: 4rem;
  height: 2 rem;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3px 20px;
  cursor: pointer;
}
.sheet-number {
  cursor: pointer;
  padding-left: 20px;
  //text-align: center;
}
</style>
