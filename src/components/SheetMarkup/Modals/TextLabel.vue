<template>
  <div>
    <Modal
      :title="getTitleText"
      noSecondaryBtn
      :primaryLabel="getTitleText"
      width="408"
      minHeight="308"
      @onClickButton="onClickAddText"
      @closeModal="closeEditModal"
    >
      <template #modal-body>
        <div class="edit-text-input">
          <div class="input-text">
            <Input
              v-model="TextLabel"
              placeholder="Enter text here"
              responsive
              size="large"
            />
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
export default {
  name: "TextLabelModal",
  props: {
    savedtextLabel: {
      type: String,
      default: null,
    },
    mode: {
      type: String,
      default: "add",
    },
  },
  data() {
    return {
      TextLabel: null,
    };
  },
  computed: {
    getTitleText() {
      return this.mode === "add" ? "Add Text" : "Update Text";
    },
  },
  mounted() {
    if (this.savedtextLabel) this.TextLabel = this.savedtextLabel;
  },
  methods: {
    onClickAddText() {
      this.mode === "add"
        ? this.$emit("onClickAddText", this.TextLabel)
        : this.$emit("onClickUpdateText", this.TextLabel);
    },
    closeEditModal() {
      this.$emit("closeAddTextModal");
    },
  },
};
</script>

<style scoped lang="scss">
.edit-text-input {
  padding: 7px 0px;
  .input-label {
    font-weight: 400;
    font-size: 10px;
    line-height: 14px;
    display: flex;
    align-items: center;
    color: #2b446c;
    padding: 5px 0px;
  }
  .input-text {
    padding: 5px 0px;
  }
}
</style>
