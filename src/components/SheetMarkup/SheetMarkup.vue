<template>
  <div class="plans-canvas-container">
    <div v-if="loading" class="img-loader">
      <PulseLoader :loading="true" color="#f57947"></PulseLoader>
    </div>
    <SheetmarkupHeader
      v-if="showVersionContainer"
      :sheetName="getSheetName"
      :sheetCode="getSheetCode"
      :sheetUploadedBy="getSheetUploadedby"
      :sheetId="sheetId"
      :userId="userId"
      :prjId="prjId"
      :onMarkupSelection="onMarkupSelection"
      :onBackButtonClick="onBackButtonClick"
      :onPlanChange="onPlanChange"
      @triggerPlanChange="performPlanChange"
      @triggerMarkupChange="triggerMarkupChange"
      @onAddNewMarkup="onAddNewMarkup"
      @onEditMarkup="onEditMarkup"
      @onSavePlanMarkup="onSavePlanMarkup"
      @onClickSheetCompare="onClickSheetCompare"
    />
    <SheetCompareSelectSheetsModal
      v-if="showSheetCompareSelectSheetsModal"
      :prjId="prjId"
      :sheetId="sheetId"
      @closeModal="showSheetCompareSelectSheetsModal = false"
      @clickCompare="onCompareSheets"
    />
    <PlansCanvas
      ref="plans-sheet-canvas"
      :key="sheetId"
       :sheetUrl="sheetUrl"
      :sheetId="sheetId"
      :sheetHyperlinks="sheetHyperlinks"
      :onClickHyperlink="onClickHyperlink"
      :prjId="prjId"
      :objMarkup="objMarkup"
      @showScaleModal="showScale = true"
      @showAttachmentModal="onShowAttachmentModal"
      @openTextModal="onOpenTextModal"
    />
    <div v-if="showToolboxContainer" id="markup-tool-bar">
      <ToolboxContainer @toolClicked="toolClicked" />
    </div>
    <VersionContainer
      v-if="showVersionContainer"
      ref="version-container"
      :canvasRefs="getPlansCanvasRefs"
      :sheetId="sheetId"
      :onVersionSelection="triggerVersionSelection"
      @onDeleteElement="onDeleteElement"
      @onChangeMarkup="onChangeMarkup"
      @toggleListPopover="toggleListPopover"
    />

    <ShapePopover
      v-if="showShapePopover"
      :yPos="yPos"
      @shapeClicked="shapeClicked"
      @closeModal="showShapePopover = false"
    />
    <ScaleModal
      v-if="showScale"
      @doneScaling="doneScaling"
      @closeModal="showScale = false"
    />
    <LinePopover
      v-if="showLinePopover"
      :yPos="yPos"
      @shapeClicked="shapeClicked"
      @closeModal="showLinePopover = false"
    />
    <StrokeFillPopover
      v-if="showStrokeFillPopover"
      :isStroke="isStroke"
      :yPos="yPos"
      :setFillAndStroke="setFillAndStroke"
      @closeModal="showStrokeFillPopover = false"
      @colorPicked="colorPicked"
      @hardnessChanged="hardnessChanged"
      @strokeChanged="strokeChanged"
      @versionChanged="versionChanged"
    />
    <PublishModal
      v-if="showPublishModal"
      @closeModal="showPublishModal = false"
      @done="onSavePlan"
    />
    <AttachmentModal
      v-if="showAttachmentModal"
      :src="attachmentSrc"
      @closeShowModal="onCloseAttachmentModal"
    />
    <AddTextLabelModal
      v-if="showAddTextLabelModal"
      @onClickAddText="onClickAddText"
      @closeAddTextModal="onCloseAddTextLabel"
    />
  </div>
</template>

<script>
import PlansCanvas from "@/components/PlansNew1/PlansCanvas";
import ToolboxContainer from "@/components/ImageDisplayComponents/MarkupPlan/ToolboxContainer.vue";
import ShapePopover from "@/components/ImageDisplayComponents/MarkupPlan/Popover/ShapePopover.vue";
import LinePopover from "@/components/ImageDisplayComponents/MarkupPlan/Popover/LinePopover.vue";
import StrokeFillPopover from "@/components/ImageDisplayComponents/MarkupPlan/Popover/StrokeFillPopover.vue";
import ScaleModal from "@/components/ImageDisplayComponents/MarkupPlan/Modals/ScaleModal.vue";
import PublishModal from "@/components/ImageDisplayComponents/MarkupPlan/Modals/PublishModal.vue";
import VersionContainer from "@/components/ImageDisplayComponents/MarkupPlan/VersionContainer.vue";
import AddTextLabelModal from "@/components/SheetMarkup/Modals/TextLabel.vue";
import SheetCompareSelectSheetsModal from "@/components/SheetCompare/SheetSelectionModal/index.vue";
import { PLAN_MARKUP_MODES } from "@/constants";
import { DRAFT_MARKUP } from "@/constants";

import config from "/src/config";
import { mapGetters, mapActions } from "vuex";
import { Axios, urls } from "/src/utils/Axios";
// import { Button } from "@development/linarc-design-components";
import SheetmarkupHeader from "./SheetmarkupHeader.vue";
import PulseLoader from "vue-spinner/src/PulseLoader";

import forEach from "lodash/forEach";
import keys from "lodash/keys";
import get from "lodash/get";
export default {
  name: "SheetMarkup",
  components: {
    PlansCanvas,
    ToolboxContainer,
    ShapePopover,
    LinePopover,
    StrokeFillPopover,
    ScaleModal,
    PublishModal,
    VersionContainer,
    //Button,
    SheetmarkupHeader,
    AddTextLabelModal,
    PulseLoader,
    SheetCompareSelectSheetsModal,
  },
  props: {
    sheetId: {
      type: String,
      required: true,
    },
    prjId: {
      type: String,
      required: true,
    },
    objMarkup: {
      type: Object,
      required: true,
    },
    onVersionSelection: {
      type: Function,
      required: true,
    },
    onMarkupSelection: {
      type: Function,
      required: true,
    },
    onBackButtonClick: {
      type: Function,
      required: true,
    },
    onPlanChange: {
      type: Function,
      default: undefined
    },
    objIdCheck: {
      type: Function,
      default: () => {
        return () => {};
      },
      required: false,
    },
    userId: {
      type: String,
      required: true,
    },
    onClickHyperlink: {
      type: Function,
      default: () => {},
    },
  },
  data: () => ({
    showShapePopover: false,
    showLinePopover: false,
    showStrokeFillPopover: false,
    showPublishModal: false,
    isStroke: false,
    showScale: false,
    yPos: 0,
    showVersionContainer: false,
    showAttachmentModal: false,
    showAddTextLabelModal: false,
    attachmentSrc: "",
    textCords: {},
    counter: 0,
    loading: false,
    showSheetCompareSelectSheetsModal: false,
  }),
  computed: {
    showToolboxContainer() {
      return (
        this.getCurrentMarkupMode === PLAN_MARKUP_MODES.EDIT ||
        this.getCurrentMarkupMode === PLAN_MARKUP_MODES.DRAFT
      );
    },
    ...mapGetters({
      getModifiedMarkupJson: "MarkupCanvas/getModifiedMarkupJson",
      getCurrentMarkupId: "MarkupCanvas/getCurrentMarkupId",
      getCurrentMarkupMode: "MarkupCanvas/getCurrentMarkupMode",
      getMarkupType: "MarkupCanvas/getMarkupType",
      getCurrentMarkupJson: "MarkupCanvas/getCurrentMarkupJson",
      sheetMetaById: "MarkupCanvas/sheetMetaById",

    }),
    sheet() {

      return this.sheetMetaById(this.sheetId);

    },

    sheetUrl() {
      return get(this.sheet, "plf_sheet_signed_url");
    },
    // sheetId() {
    //   return get(this.sheet, "plf_id");
    // },
    sheetHyperlinks() {
      return get(this.sheet, "plf_hyperlink.hyperlink");
    },
    getPlansCanvasRefs() {
      return this.$refs ? this.$refs["plans-sheet-canvas"] : null;
    },
    getSheetCode() {
      return get(this.sheet, "plf_sheet_number");
    },
    getSheetName() {
      return get(this.sheet, "plf_sheet_name");
    },
    getSheetUploadedby() {
      return get(this.sheet, "plf_uploaded_by");
    },
  },
  mounted() {
    // this.showVersionContainer = true;
    setTimeout(() => {
      this.showVersionContainer = true;
    }, 800);
  },
  methods: {
    ...mapActions({
      updateSheetMeta: "MarkupCanvas/updateSheetMeta",
      updatePlanMarkup: "MarkupCanvas/updatePlanMarkup",
      setMarkupMode: "MarkupCanvas/setMarkupMode",
      switchMarkupType: "MarkupCanvas/switchMarkupType",
      setCurrentMarkupId: "MarkupCanvas/setCurrentMarkupId",

    }),
    async performPlanChange(raw) {
      await this.setMarkupMode(PLAN_MARKUP_MODES.READONLY);
      this.$emit("triggerPlanChange", raw);
    },
    async triggerMarkupChange() {
      await this.setMarkupMode(PLAN_MARKUP_MODES.READONLY);
      this.$emit("triggerMarkupChange")
    },
    async triggerVersionSelection(raw) {
      console.log("sheetmarkup", raw);
      await this.setMarkupMode(PLAN_MARKUP_MODES.READONLY);
      this.$emit("triggerVersionSelection", raw);
      this.onVersionSelection(raw);
    },
    async onDeleteElement() {
      await this.$refs["plans-sheet-canvas"].drawCanvasWithSheet({
        redrawSelectedMarkup: true,
      })
      
    },
    onChangeMarkup() {
      this.$refs["plans-sheet-canvas"].drawSavedMarkups()
    },
    async onAddNewMarkup() {
      await this.setCurrentMarkupId(DRAFT_MARKUP.id);
      // await this.switchMarkupType(MARKUP_TYPES.TAKE_OFF);
      await this.setMarkupMode(PLAN_MARKUP_MODES.DRAFT);
      this.$emit("triggerMarkupChange");
      // when the user select new markup the plm_type should be shown as Markup not on the previous state 
      // So this is used to reset the type
      this.$refs["version-container"].changeType(false);
    },
    async onEditMarkup() {
      await this.setMarkupMode(PLAN_MARKUP_MODES.EDIT);
      this.$emit("triggerMarkupChange")


    },
    async onShowAttachmentModal(object) {
      const response = await Axios.get(urls.getAttachedImageById(object.id));
      this.attachmentSrc = get(response, "data[0].url");
      this.showAttachmentModal = true;
    },
    onOpenTextModal({ x, y }) {
      this.textCords = { x, y };
      this.showAddTextLabelModal = true;
      //this.$refs["plans-sheet-canvas"].setText(x, y, "text");
    },
    onClickSheetCompare() {
      this.showSheetCompareSelectSheetsModal = true;
    },
    onCloseAddTextLabel() {
      this.showAddTextLabelModal = false;
      this.textCords = {};
    },
    onClickAddText(text) {
      if (text) {
        this.$refs["plans-sheet-canvas"].setText(
          this.textCords.x,
          this.textCords.y,
          text
        );
        this.onCloseAddTextLabel();
      } else this.$toast.error("Please enter text");
    },
    onCloseAttachmentModal() {
      this.attachmentSrc = "";
      this.showAttachmentModal = false;
    },
    toggleListPopover() {
      this.showVersionListPopover = !this.showVersionListPopover;
    },
    async onEditPlanMarkup() {
      this.loading = true;
      const elementsWithoutMeasurements = {};
      const modifiedElements = this.getModifiedMarkupJson.elements;
      console.log("modifiedElements", modifiedElements);
      for (const elementId of keys(modifiedElements)) {
        elementsWithoutMeasurements[elementId] = {
          ...modifiedElements[elementId],
        };
        if (get(modifiedElements[elementId], "measurements")) {
          delete elementsWithoutMeasurements[elementId].measurements;
        }
      }

      const markUp = {
        normal: [],
        compound_elements: [],
        ...this.getModifiedMarkupJson,
        elements: elementsWithoutMeasurements,
      };
      const markupData = {
        plm_markup: markUp,
      };
      const data = await this.updatePlanMarkup({
        markupData: markupData,
        prjId: this.prjId,
      });
      if (data.status === 200) {
        await this.$toast.success("Markup Updated Successfully");
        this.$emit("markupSaved", data.data);
      }
      this.setMarkupMode(PLAN_MARKUP_MODES.READONLY);
      this.$emit("triggerMarkupChange");
      this.loading = false;
    },
    onSavePlanMarkup() {
      if (this.getCurrentMarkupMode === PLAN_MARKUP_MODES.DRAFT) {
        this.showPublishModal = true;
      } else if (this.getCurrentMarkupMode === PLAN_MARKUP_MODES.EDIT) {
        this.onEditPlanMarkup();
      }
    },
    toolClicked(tool) {
      if (tool.val === "shape") {
        this.yPos = tool.yPos;
        this.showShapePopover = true;
      } else if (tool.val === "line") {
        this.yPos = tool.yPos;
        this.showLinePopover = true;
      } else if (tool.val === "measure") {
        this.$refs["plans-sheet-canvas"].setTool("ruler");
      } else if (tool.val === "text") {
        this.$refs["plans-sheet-canvas"].setTool("text");
      } else if (tool.val === "stroke") {
        this.yPos = tool.yPos;
        this.isStroke = true;
        this.showStrokeFillPopover = true;
      } else if (tool.val === "fill") {
        this.yPos = tool.yPos;
        this.isStroke = false;
        this.showStrokeFillPopover = true;
      } else if (tool.val === "image") {
        this.$refs["plans-sheet-canvas"].setTool("camera");
      } else if (tool.val === "location") {
        this.$refs["plans-sheet-canvas"].setTool("locationpin");
      } else if (tool.val === "pen") {
        this.$refs["plans-sheet-canvas"].setTool("pencil");
      } else if (tool.val === "highlighter") {
        this.$refs["plans-sheet-canvas"].setTool("highlighter");
      } else if (tool.val === "arrow") {
        this.yPos = tool.yPos;
        this.showLinePopover = true;
      } else if (tool.val === "select") {
        // this.$refs.imageDisplay.offDrawMode();
        // this.setSelectedMarkupTool(tool.val);
      } else if (tool.val === "hand") {
        // this.$refs.imageDisplay.allowHandMode();
        // this.setSelectedMarkupTool(tool.val);
      } else if (tool.val === "cloud") {
        this.$refs["plans-sheet-canvas"].setTool("cloud");
      }
      else if(tool.val==="polygon"){
        this.$refs["plans-sheet-canvas"].setTool("polygon");
      }
    },
    shapeClicked(val) {
      if (val === "square") {
        this.$refs["plans-sheet-canvas"].setTool("rect");
      } else if (val === "circle") {
        this.$refs["plans-sheet-canvas"].setTool("ellipse");
      } else if (val === "triangle") {
        this.$refs["plans-sheet-canvas"].setTool("triangle");
      } else if (val === "arrow") {
        this.$refs["plans-sheet-canvas"].setTool("arrow");
      } else if (val === "line") {
        this.$refs["plans-sheet-canvas"].setTool("line");
      } else return "";
      this.showShapePopover = false;
      this.showLinePopover = false;
      //this.setSelectedMarkupTool(val);
    },
    doneScaling(scale) {
      this.$refs["plans-sheet-canvas"].caliberateRuler(scale);
      this.showScale = false;
    },
    setFillAndStroke() {
      this.$refs.imageDisplay.setFillAndStroke();
    },
    colorPicked(color) {
      this.$refs["plans-sheet-canvas"].setColor(color);
    },
    hardnessChanged(hardness) {
      this.$refs["plans-sheet-canvas"].setOpacity(hardness);
    },
    strokeChanged(stroke) {
      this.$refs["plans-sheet-canvas"].setStrokeWidth(stroke);
    },
    async onSavePlan(markupName) {
      this.loading = true;

      const elementsWithoutMeasurements = {};
      const modifiedElements = this.getModifiedMarkupJson.elements;

      for (const elementId of keys(modifiedElements)) {
        elementsWithoutMeasurements[elementId] = {
          ...modifiedElements[elementId],
        };
        if (get(modifiedElements[elementId], "measurements")) {
          delete elementsWithoutMeasurements[elementId].measurements;
        }
      }

      const markUp = {
        normal: [],
        compound_elements: [],
        ...this.getModifiedMarkupJson,
        elements: elementsWithoutMeasurements,
      };
      const markupData = {
        plm_sheet_version: get(this.sheet, "plf_sheet_version"),
        plm_createdby: config.userId,
        obj_id: this.objMarkup.objId,
        obj_type: this.objMarkup.objType,
        pcm_id: this.objMarkup.pcmId,
        plm_markup: markUp,
        plm_type : this.getMarkupType,
      };

      // While linking the plan Markup - plm_access is set to PB
      // if (this.objMarkup.access) markupData.plm_access = this.objMarkup.access;

      const formData = new FormData();

      this.planImageAttachment = await this.$refs[
        "plans-sheet-canvas"
      ].canvasToBlob();

      if (markupName) markupData.plm_name = markupName;
      let plfId = this.sheetId;
      forEach(keys(markupData), (key) => {
        formData.set(key, markupData[key]);
      });

      const res = await Axios.post(urls.addMarkup(plfId), markupData);
      if (res.status === 200) {
        if (this.objMarkup.objType !== "plansfile"){
          //   await Axios.patch(urls.setMarkupPrivate(res.data.plm_id),{
            //   access: "PB",
            //   permission_objects : [{prj_id:this.prjId}]
            // });
        await this.onSaveImage(this.planImageAttachment, markupName);
        this.$emit("markupSaved", res.data);
        }
        else {
        //   await Axios.patch(urls.setMarkupPrivate(res.data.plm_id),{
        //   access: "PR",
        //   permission_objects : [{user_id:config.userId}]
        // });
          this.$toast.success("Markup saved successfully");
          this.$emit("markupSaved",res.data);
        }

        // await this.updateSheetMeta({
        //   plf_id: this.sheetId,
        //   plf_sheet_scale_factor:
        //     this.$refs["plans-sheet-canvas"].getCaliberatedScale(),
        // });
      }
      await this.setMarkupMode(PLAN_MARKUP_MODES.READONLY);
      this.setCurrentMarkupId(null);
      this.triggerMarkupChange();
      this.loading = false;
    },
    onCompareSheets(sheets) {
      this.$emit("onCompareSheets", sheets);
    },
    async onSaveImage(obj, name) {
      let objectId = this.objMarkup.objId;
      // if (!this.objMarkup.objId)
      //   //getting object if not present
      //   const objectId= await this.objIdCheck();

      if (!objectId) {
        objectId = await this.objIdCheck();
      }

      const attachment = {
        file_name: name,
        model_name: this.objMarkup.objType,
        object_id: objectId,
        file_type: "image/jpeg",
      };
      const response = await Axios.post(urls.preSignedPost(), attachment);

      const storeS3File = {
        key: response.data.fields.key,
        AWSAccessKeyId: response.data.fields.AWSAccessKeyId,
        policy: response.data.fields.policy,
        signature: response.data.fields.signature,
      };
      const formData = new FormData(); //for image
      forEach(keys(storeS3File), (key) => {
        formData.set(key, storeS3File[key]);
      });

      const storeThumbFile = {
        key: response.data.thumbnail_fields.key,
        AWSAccessKeyId: response.data.thumbnail_fields.AWSAccessKeyId,
        policy: response.data.thumbnail_fields.policy,
        signature: response.data.thumbnail_fields.signature,
      };
      const formDataThumbnail = new FormData(); //for thumbnail
      forEach(keys(storeThumbFile), (key) => {
        formDataThumbnail.set(key, storeThumbFile[key]);
      });
      formData.append("file", obj.imageSrc);
      formDataThumbnail.append("file", obj.thumbSrc);
      const p1 = await this.$axios.post(response.data.url, formData); //we are uploading image to s3
      const p2 = await this.$axios.post(response.data.url, formDataThumbnail); //we are uploading thumbnail to s3
      Promise.all([p1, p2]).then(() => {
        this.$toast.success("Image Uploaded Successfully");
        this.$emit("markupSaved");
      });
    },
    saveFileScaleCaliberation() {},
  },
};
</script>

<style scoped lang="scss">
.plans-canvas-container {
  width: 100%;
  height: 100%;

  // border: 1px solid #adc1d1;
  // border-radius: 6px 6px 0px 0px;
}
.save-plan-button {
  display: flex;
  justify-content: flex-end;
  padding-bottom: 10px;
}

.img-loader {
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 50;
}
</style>
