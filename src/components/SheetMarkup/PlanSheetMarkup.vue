<template>
  <div class="sheet-container">
    <!-- :sheet="getSheetData" -->
    <!-- :onMarkupSelection="onMarkupSelection" -->
    <SheetMarkup
    v-if="showMarkupSheet"
      ref="sheet-markup"
      :prjId="prjId"
      :sheetId="currentSheetId"
      :objMarkup="getObjMarkup"
      :objIdCheck="objIdCheck"
      :onVersionSelection="onVersionSelection"
      :onBackButtonClick="onBackButtonClick"
      :userId="userId"
      :onClickHyperlink="onClickHyperlink"
      :onPlanChange="onPlanChange"  
      @triggerPlanChange="triggerPlanChange"
      @triggerMarkupChange="triggerMarkupChange"
      @onAddMarkup="onAddMarkup"
      @onEditMarkup="onEditMarkup"
      @triggerVersionSelection="triggerVersionChange"
      @markupSaved="onMarkupSaved"
      @onSavePlanMarkup="onSavePlanMarkup"
      @onCompareSheets="onCompareSheets"
    />
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import SheetMarkup from "./SheetMarkup.vue";
import get from "lodash/get";
export default {
  name: "PlanSheetMarkup",
  components: {
    SheetMarkup,
  },
  props: {
    prjId: {
      type: String,
      required: true,
    },
    sheetId: {
      type: String,
      required: true,
    },
    objMarkup: {
      type: Object,
      required: true,
    },
    onVersionSelection: {
      type: Function,
      required: true,
    },
    onMarkupSelection: {
      type: Function,
      required: true,
    },
    markupId: {
      type: String,
      default: undefined,
    },
    onBackButtonClick: {
      type: Function,
      required: true,
    },
    markupMode: {
      type: String,
      required: true,
    },
    objIdCheck: {
      type: Function,
      default: () => {
        return () => {};
      },
      required: false,
    },
    userId: {
      type: String,
      required: true,
    },
    onClickHyperlink: {
      type: Function,
      default: () => {},
    },
    onPlanChange: {
      type: Function,
      default: undefined
    },
  },
  data() {
    return {
      showMarkupSheet: false,
      currentSheetId: this.sheetId,
      currentObjMarkup: this.objMarkup,      
    };
  },
  computed: {
    ...mapGetters({
      sheetMetaById: "MarkupCanvas/sheetMetaById",
    }),
    getSheetData() {
      return this.sheetMetaById(this.currentSheetId);
    },
    getObjMarkup() {
      return this.currentObjMarkup;
    },
  },
  async mounted() {
    await this.setCurrentMarkupId(this.markupId);
    await this.setMarkupMode(this.markupMode);
    this.setSheetVersionsVuex();
  },
  methods: {
    ...mapActions({
      setPlanMarkup: "MarkupCanvas/setPlanMarkup",
      setSheetVersions: "MarkupCanvas/setSheetVersions",
      setCurrentMarkupId: "MarkupCanvas/setCurrentMarkupId",
      setMarkupMode: "MarkupCanvas/setMarkupMode",
    }),

    async triggerPlanChange(raw) {
      await this.setSheetVersions({
        prjId: this.prjId,
        sheetNumber: get(raw, "number"),
      });
      await this.setCurrentMarkupId(null);
      this.currentSheetId = get(raw, "id");
      this.currentSheetNumber = get(raw, "number");
    },
    triggerMarkupChange() {
      const currentSheetId = this.currentSheetId;
      this.currentSheetId = null;      
      setTimeout(() => {
        this.currentSheetId = currentSheetId;
      }, 0);
    },
    async triggerVersionChange(id) {
      this.currentSheetId = id;
      this.currentObjMarkup = {
        ...this.currentObjMarkup,
        plfId: id,
      }
    },
    onMarkupSaved(data) {
      this.setSheetVersionsVuex();
      this.$emit("onPlanMarkupSaved", data);
    },
    onSavePlanMarkup() {
      this.$refs["sheet-markup"].onSavePlanMarkup();
    },
    async setSheetVersionsVuex() {
      await this.setSheetVersions({
        prjId: this.prjId,
        sheetNumber: get(this.objMarkup, "sheetNumber"),
      });
      this.showMarkupSheet = true;
    },
    onCompareSheets(sheets) {
      this.$emit("onCompareSheets", sheets);
    },
  },
};
</script>

<style scoped>
.sheet-container {
  width: 100%;
  height: 100%;
}
</style>
