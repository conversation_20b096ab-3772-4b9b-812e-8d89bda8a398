<template>
  <div v-if="sheetUrl" class="plans-canvas-container">
    <PlanCanvasViewer :key="sheetUrl" :prjId="prjId" :sheetUrl="sheetUrl" />
    <SheetToggleVue
      :sheetCompareimages="getSheetCompareImages"
      :hideSheet1="hideSheet1"
      :hideSheet2="hideSheet2"
      :onClickHide="onClickHide"
    />
  </div>
  <PulseLoader
    v-else
    :loading="true"
    color="#f57947"
    class="pulse-loader"
  ></PulseLoader>
</template>

<script>
import PlanCanvasViewer from "@/components/PlansNew1/PlansCanvas/PlansCanvasViewer";
import { mapActions, mapGetters } from "vuex";
import get from "lodash/get";
import SheetToggleVue from "./SheetToggle.vue";
import PulseLoader from "vue-spinner/src/PulseLoader";

export default {
  name: "SheetCompareViewer",
  components: {
    PlanCanvasViewer,
    SheetToggleVue,
    PulseLoader,
  },
  props: {
    prjId: {
      type: String,
      required: true,
    },
    sheetId1: {
      type: String,
      required: true,
    },
    sheetId2: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      sheetUrl: null,
      hideSheet1: false,
      hideSheet2: false,
      sheet1Url: null,
      sheet2Url: null,
      overlayUrl: null,
    };
  },
  computed: {
    ...mapGetters({
      getSheetCompareImages: "MarkupCanvas/getSheetCompareImages",
    }),
  },
  async mounted() {
    try {
      await this.setSheetCompareImages({
        plfId1: this.sheetId1,
        plfId2: this.sheetId2,
      });
      this.overlayUrl = get(
        this.getSheetCompareImages,
        "overlay_result.sheet_compare_url",
        null
      );
      this.sheet1Url = get(
        this.getSheetCompareImages,
        "sheet1.sheet_compare_url",
        null
      );
      this.sheet2Url = get(
        this.getSheetCompareImages,
        "sheet2.sheet_compare_url",
        null
      );
      this.sheetUrl = this.overlayUrl;
    } catch (e) {
      const error = get(e, "response.data.message", "Something went wrong!");
      this.$notifier.error(error);
    }
  },
  methods: {
    ...mapActions({
      setSheetCompareImages: "MarkupCanvas/setSheetCompareImages",
    }),
    onClickHide(sheet) {
      if (sheet === "sheet1") {
        this.hideSheet1 = !this.hideSheet1;
      } else if (sheet === "sheet2") {
        this.hideSheet2 = !this.hideSheet2;
      }
      this.setUrlonToggle();
    },
    setUrlonToggle() {
      if (this.hideSheet1 && this.hideSheet2) {
        this.sheetUrl = this.overlayUrl;
      } else if (this.hideSheet1) {
        this.sheetUrl = this.sheet2Url;
      } else if (this.hideSheet2) {
        this.sheetUrl = this.sheet1Url;
      } else {
        this.sheetUrl = this.overlayUrl;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.plans-canvas-container {
  width: 100%;
  height: 100%;

  // border: 1px solid #adc1d1;
  // border-radius: 6px 6px 0px 0px;
}

.pulse-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
