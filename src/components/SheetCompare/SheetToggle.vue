<template>
  <div class="sheet-toggle-modal">
    <div class="title">Sheet Compare</div>
    <div class="sheets-container">
      <div class="sheet">
        <div class="sheet-actions">
          <div class="square-icon"><Icon name="square" color="decline" /></div>
          <div class="sheet-code">{{ getSheetCode1 }}</div>
          <div class="eye-icon" @click="onClickHide('sheet1')">
            <Icon :name="getSheet1Icon" color="secondary" />
          </div>
        </div>
        <div class="sheet-name" :title="getSheetName1">{{ getSheetName1 }}</div>
      </div>
      <div class="sheet">
        <div class="sheet-actions">
          <div class="square-icon">
            <Icon name="square" color="baseRoyalBlue" />
          </div>
          <div class="sheet-code">
            {{ getSheetCode2 }}
          </div>
          <div class="eye-icon" @click="onClickHide('sheet2')">
            <Icon :name="getSheet2Icon" color="secondary" />
          </div>
        </div>
        <div class="sheet-name">
          {{ getSheetName2 }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import get from "lodash/get";
export default {
  name: "SheetToggleModal",
  props: {
    sheetCompareimages: {
      type: Object,
      default: () => ({}),
    },
    hideSheet1: {
      type: Boolean,
      default: false,
    },
    hideSheet2: {
      type: Boolean,
      default: false,
    },
    onClickHide: {
      type: Function,
      default: () => {},
    },
  },
  computed: {
    getSheetCode1() {
      return `${get(
        this.sheetCompareimages,
        "sheet1.plf_sheet_number",
        ""
      )} (V-${get(this.sheetCompareimages, "sheet1.plf_sheet_version", "")})`;
    },
    getSheetCode2() {
      return `${get(
        this.sheetCompareimages,
        "sheet2.plf_sheet_number",
        ""
      )} (V-${get(this.sheetCompareimages, "sheet2.plf_sheet_version", "")})`;
    },
    getSheetName1() {
      return `${get(this.sheetCompareimages, "sheet1.plf_sheet_name", "")}`;
    },
    getSheetName2() {
      return `${get(this.sheetCompareimages, "sheet2.plf_sheet_name", "")}`;
    },
    getSheet1Icon() {
      return this.hideSheet1 ? "eyeOff" : "eye";
    },
    getSheet2Icon() {
      return this.hideSheet2 ? "eyeOff" : "eye";
    },
  },
};
</script>

<style scoped lang="scss">
.sheet-toggle-modal {
  position: absolute;
  top: 10px;
  width: 300px;
  z-index: 100;
  border-radius: 6px;
  border: 1px solid var(--Base-Light3, #e7edf5);
  background: #fff;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  .title {
    font-size: 16px;
    font-weight: 500;
    padding: 10px;
    border-bottom: 1px solid var(--Base-Light3, #e7edf5);
  }
  .sheets-container {
    padding: 5px;
  }
  .sheet {
    padding: 5px;
    .sheet-actions {
      display: flex;
      align-items: center;
      .square-icon {
        padding-right: 10px;
        width: 30px;
      }
      .sheet-code {
        font-size: 12px;
        font-weight: 400;
        padding-right: 10px;
        width: 220px;
      }
      .eye-icon {
        width: 30px;
        cursor: pointer;
      }
    }
    .sheet-name {
      font-size: 14px;
      font-weight: 400;
      padding-left: 30px;
      width: 250px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
