<template>
  <div>
    <Modal
      title="Compare Sheets"
      :disablePrimaryBtn="disableCompareButton"
      primaryLabel="Compare"
      width="800"
      @onClickButton="clickCreate"
      @closeModal="closeModal"
    >
      <template #modal-body>
        <div class="sheet-selectors">
          <SelectSheet
            ref="sheet1-ref"
            :key="sheetId1"
            :sheetId="sheetId1"
            :prjId="prjId"
            :excludeSheetIds="exludeForSheet1"
            @onSheetCodeSelect="onSheetCodeSelect1"
            @sheetDimensions="storeSheetDimensions"
          />
          <SelectSheet
            ref="sheet2-ref"
            :key="sheetId2"
            :sheetId="sheetId2"
            :prjId="prjId"
            :excludeSheetIds="exludeForSheet2"
            @onSheetCodeSelect="onSheetCodeSelect2"
            @sheetDimensions="storeSheetDimensions"
          />
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
// import { Modal } from "@development/linarc-design-components";
import SelectSheet from "./selectSheet.vue";
import get from "lodash/get";
export default {
  name: "SelectSheetsModal",
  components: {
    // Modal,
    SelectSheet,
  },
  props: {
    prjId: {
      type: String,
      required: true,
    },
    sheetId: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      sheetId1: null,
      sheetId2: null,
      // create a object which will store the sheet details with sheetId as key
      sheetDetails: {},
    };
  },
  computed: {
    disableCompareButton() {
      return !this.sheetId1 || !this.sheetId2;
    },
    exludeForSheet1() {
      return [this.sheetId2];
    },
    exludeForSheet2() {
      return [this.sheetId1];
    },
  },
  mounted() {
    if (this.sheetId) {
      this.sheetId1 = this.sheetId;
    }
  },
  methods: {
    closeModal() {
      this.$emit("closeModal");
    },
    clickCreate() {
      let isDimensionsSame = this.checkSheetsDimensions();
      if (!isDimensionsSame) {
        return;
      }
      this.$emit("clickCompare", [this.sheetId1, this.sheetId2]);
    },
    storeSheetDimensions(dimensions) {
      this.sheetDetails = {
        ...this.sheetDetails,
        ...dimensions
      };
    },
    onSheetCodeSelect1(sheetId) {
      this.sheetId1 = sheetId;
    },
    onSheetCodeSelect2(sheetId) {
      this.sheetId2 = sheetId;
    },
    checkSheetsDimensions() {
      let sheet1 = this.sheetDetails[this.sheetId1];
      let sheet2 = this.sheetDetails[this.sheetId2];
      if (
        get(sheet1, 'height') !== get(sheet2, 'height') ||
        get(sheet1, 'width') !== get(sheet2, 'width')
      ) {
        this.$notifier.error(
          "Sheet dimensions are not same. Please select sheets with same dimensions."
        );
        return false;
      }
      return true;
    },
  },
};
</script>

<style scoped lang="scss">
.sheet-selectors {
  display: flex;
  justify-content: space-around;
}
</style>
