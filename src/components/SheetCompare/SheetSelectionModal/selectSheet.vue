<template>
  <div class="select-sheet-container">
    <div class="sheet-title">
      <div v-if="!sheetId">Select Sheet</div>
      <div v-if="sheetId" class="flex">
        <div>{{ getSheetCode }}</div>
        <div>{{ getSheetVersion }}</div>
      </div>
      <div
        v-if="sheetId"
        class="change-sheet"
        @click="$emit('onSheetCodeSelect', null)"
      >
        Change
      </div>
    </div>
    <div class="sheet-wrapper">
      <div v-if="!sheetId" class="select-sheet">
        <div class="auto-search">
          <AutoComplete
            :options="sheetCodeOptions"
            label="label"
            responsive
            placeholder="Search Sheet Code"
            size="large"
            autosearch
            @onInputChange="onSheetCodeChange"
            @onSelected="onSheetCodeSelect"
          />
        </div>
      </div>
      <div v-if="sheetId" class="selected-sheet">
        <img :src="sheetDetails.thumbnail_url" alt="sheet" />
      </div>
    </div>
  </div>
</template>

<script>
import { Axios, urls } from "/src/utils/Axios";
// import { AutoComplete } from "@development/linarc-design-components";
import get from "lodash/get";
import filter from "lodash/filter";
import includes from "lodash/includes";
import { mapActions } from "vuex";
export default {
  name: "SheetSelectionModal",
  components: {
    // AutoComplete,
  },
  props: {
    sheetId: {
      type: String,
      default: null,
    },
    prjId: {
      type: String,
      required: true,
    },
    excludeSheetIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      sheetDetails: {},
      sheetCodeOptions: [],
    };
  },
  computed: {
    getSheetCode() {
      return get(this.sheetDetails, "plf_sheet_number", "");
    },
    getSheetVersion() {
      return `(V-${get(this.sheetDetails, "plf_sheet_version", "")})`;
    },
    getSheetName() {
      return ` ${get(this.sheetDetails, "plf_sheet_name", "")}`;
    },
  },
  async mounted() {
    if (this.sheetId) {
      await this.setSheetDetails();
    }
  },
  methods: {
    ...mapActions({
      getSheetCodeoptions: "MarkupCanvas/getSheetCodeoptions",
    }),
    async onSheetCodeChange(e) {
      if (e.target.value) {
        const sheetCodeOptions = await this.getSheetCodeoptions({
          prjId: this.prjId,
          query: e.target.value,
        });
        this.sheetCodeOptions = filter(
          sheetCodeOptions,
          (sheet) => !includes(this.excludeSheetIds, sheet.value)
        );
      } else {
        this.sheetCodeOptions = [];
      }
    },
    onSheetCodeSelect(e) {
      this.$emit("onSheetCodeSelect", e.value);
    },
    getSheetDimensions() {
      return get(this.sheetDetails, "plf_sheet_data", "");
    },
    async setSheetDetails() {
      const response = await Axios.get(urls.getPlanFileDetails(this.sheetId));
      this.sheetDetails = response.data;
      let sheetData = response.data.plf_sheet_data;
      //emit event to parent sending the sheet height and width as values for key of sheet id
      this.$emit("sheetDimensions", {
        [this.sheetId]: {
          height: sheetData.height,
          width: sheetData.width,
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.select-sheet-container {
  width: 370px;
}
.sheet-title {
  display: flex;
  width: 350px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  justify-content: space-between;
  padding-bottom: 5px;
}
.change-sheet {
  color: var(--Base-Royal-Blue, #2b76e7);
  text-align: center;
  font-size: 10px;
  font-weight: 700;
  cursor: pointer;
}
.sheet-wrapper {
  border-radius: 6px;
  border: 1px solid var(--Base-Light2, #adc1d1);
  background: var(--White, #fff);
  height: 350px;
}
.selected-sheet {
  height: 100%;
  width: 100%;
  background: #e7edf5;
  img {
    height: 100%;
    width: 100%;
    object-fit: fill;
  }
}
.select-sheet {
  height: 100%;
  width: 100%;
  background: #e7edf5;
  .auto-search {
    padding: 50px 5px 5px 5px;
  }
}
</style>
