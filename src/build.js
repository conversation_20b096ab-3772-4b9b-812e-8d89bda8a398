import PlanUpload from "./components/ImageDisplayComponents/PlanUpload";
import PlanUploadBatch from "./store/modules/PlanUploadBatch";
import PlanUploadFile from "./store/modules/PlanUploadFile";
import PlanUploadSet from "./store/modules/PlanUploadSet";
import MarkupCanvas from "./store/modules/MarkupCanvas";
import PlanMarkup from "./store/modules/PlanMarkup";
import config from "./config";
import PlanList from "./components/ImageDisplayComponents/PlanList/";
import PlanDraft from "./components/ImageDisplayComponents/PlanDraft";
import MarkupPlan from "./components/ImageDisplayComponents/MarkupPlan";
import ShareModal from "./components/ImageDisplayComponents/MarkupPlan/Modals/ShareModal";
import SetCompanyModal from "./components/ImageDisplayComponents/MarkupPlan/Modals/SetCompanyModal";
import imageDisplay from "./components/imageDisplay/imageDisplay.vue";
//import SheetUpload from "./components/ImageDisplayComponents/PlanList/SheetUpload"

import MarkupJson from "@/components/PlansNew1/Classes/MarkupJson.js";
import MarkupObjects from "@/components/PlansNew1/Classes/MarkupObjects.js";
import MarkupCanvasClass from "@/components/PlansNew1/Classes//MarkupCanvas";

import SheetList from "@/components/SheetMarkup/SheetListContainer";
import SheetMarkup from "@/components/SheetMarkup/PlanSheetMarkup.vue";
import SheetCompareViewer from "@/components/SheetCompare/index.vue";

const ImageDisplayStore = {
  PlanUploadBatch,
  PlanUploadFile,
  PlanUploadSet,
  PlanMarkup,
  MarkupCanvas,
};

export default {
  PlanUpload,
  PlanList,
  PlanDraft,
  MarkupPlan,
  ShareModal,
  SetCompanyModal,
  // SheetUpload,
  ImageDisplayStore,
  config,
  imageDisplay,
  SheetList,
  SheetMarkup,
  SheetCompareViewer,
  install(Vue, options) {
    Vue.prototype.$moment = options.moment;
    Vue.prototype.$axios = options.axios;
    Vue.prototype.$designComponentsConfig = options.config;

    Vue.component("Button", options.designComponents.Button);
    Vue.component("Checkbox", options.designComponents.Checkbox);
    Vue.component("SpreadSheetV2", options.designComponents.SpreadSheetV2)
    Vue.component("SpreadSheetDataWrapper", options.designComponents.SpreadSheetDataWrapper)
    Vue.component("AutoComplete", options.designComponents.AutoComplete)
    Vue.component("DatePicker", options.designComponents.DatePicker);
    Vue.component("Icon", options.designComponents.Icon);
    Vue.component("Dropdown", options.designComponents.Dropdown);
    Vue.component("SpreadSheet", options.designComponents.SpreadSheet);
    Vue.component("Modal", options.designComponents.Modal);
    Vue.component("Radio", options.designComponents.Radio);
    Vue.component("WorkflowModal", options.designComponents.WorkflowModal);
    Vue.component("Slider", options.designComponents.Slider);
    Vue.component("Input", options.designComponents.Input);
    Vue.component("ColorPicker", options.designComponents.ColorPicker);
    Vue.component("Avatar", options.designComponents.Avatar);
    Vue.component("Box", options.designComponents.Box);
    Vue.component("ProgressBar", options.designComponents.ProgressBar);
    Vue.component("Tag", options.designComponents.Tag);
    Vue.component("AlertWrapper", options.designComponents.AlertWrapper);
    Vue.component("Grid", options.designComponents.Grid);
    Vue.component("NoData", options.designComponents.NoData);
    Vue.component("SmartTable", options.designComponents.SmartTable);
    Vue.component("MarkupPlan", MarkupPlan);
    Vue.component("sheetList", SheetList);
    Vue.component("sheetMarkup", SheetMarkup);
    Vue.component("sheetCompareViewer", SheetCompareViewer);
    Vue.component("AttachmentModal", options.designComponents.AttachmentModal);
    Vue.component("Toggle", options.designComponents.Toggle);
    MarkupJson.setStore(options.store);
    MarkupObjects.setStore(options.store);
    MarkupCanvasClass.setStore(options.store);
    MarkupCanvasClass.setCameraImageSrc(options.cameraIcon);
    MarkupCanvasClass.setLocationImageSrc(options.locationIcon);
  },
};
