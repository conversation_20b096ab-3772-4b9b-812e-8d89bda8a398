import axios from "axios";

class GdriveService {
  constructor() {
    this.axios = axios;
  }

  preSignedPost(baseURL, token) {
    let URL = `${baseURL}/api/v1/integrations/gdrive/oauth/token/?redirect={redirect_url}`;
    // https://dev-dev.linarc.io/projectPortal/LQf5mvf6Fx2iFtWy/newPlans/9bC3icap6oFUtutz/upload
    return this.axios({
      method: "get",
      url: URL,
      headers: {
        Authorization: token,
      },
      withCredentials: true,
    }).then(
      (response) => {
        return response;
      },
      (error) => {
        return error.response;
      }
    );
  }

  uploadPlanData(baseURL, token, pbt_id, data) {
    let URL = `${baseURL}/api/v1/integrations/selected-files/plans/batch/${pbt_id}/?uploaded_from=drive`;
    return this.axios({
      method: "post",
      url: URL,
      headers: {
        Authorization: token,
      },
      data,
      withCredentials: true,
    }).then(
      (response) => {
        return response;
      },
      (error) => {
        return error.response;
      }
    );
  }
}

export default new GdriveService();


