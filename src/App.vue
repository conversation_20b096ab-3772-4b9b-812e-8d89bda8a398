<template>
  <div id="app">
    <div class="layout">
      <div class="side-menu" :class="{ 'side-menu-collapsed': isMenuCollapsed }">
        <div class="menu-toggle" @click="toggleMenu">
          <i :class="isMenuCollapsed ? 'icon-right' : 'icon-left'"></i>
        </div>
        <div class="menu-items">
          <div 
            class="menu-item" 
            :class="{ active: showList }"
            @click="openSheets"
          >
            <span>Sheets</span>
          </div>
          <div 
            class="menu-item" 
            :class="{ active: showDraft }"
            @click="openPlans"
          >
            <span>Plans</span>
          </div>
        </div>
      </div>
      <div class="main-content" :class="{ 'content-expanded': isMenuCollapsed }">
        <SheetCompareViewer
          v-if="showSheetCompare"
          :prjId="projectId"
          :sheetId1="sheetId1"
          :sheetId2="sheetId2"
        />
        <SheetSelectionModal
          v-if="false"
          :prjId="projectId"
          :sheetId1="sheetId1"
          :sheetId2="sheetId2"
        />
        <!-- :key="newPlfId" -->
        <SheetMarkup
          v-if="newPlfId && showSheetMarkup"
          userId="projectm1295"
          :sheetId="newPlfId"
          :prjId="projectId"
          :objIdCheck="objIdCheck"
          :objMarkup="objMarkup"
          :onVersionSelection="onVersionSelection"
          :onPlanChange="onPlanChange"
          :onBackButtonClick="onBackButtonClick"
          :onMarkupSelection="onMarkupSelection"
          :markupId="markupId"
          :markupMode="markupMode"
          :onClickHyperlink="onClickHyperlink"
          @triggerMarkupChange="triggerMarkupChange"
          @onPlanMarkupSaved="onPlanMarkupSaved"
          @onCompareSheets="onCompareSheets"
          />
        <SheetList
          v-if="showList"
          ref="plan-sheet-list"
          :prjId="projectId"
          :openPlansUploadComponent="openPlansUploadComponent"
          :onVersionSelection="onVersionSelection"
          componentView="list"
          @OpenSheetMarkup="OpenSheetMarkup"
        />
        <MarkupPlan
          v-if="false"
          :objMarkup="objMarkupImageViewer"
          :changeMarkupVersion="changeMarkupVersion"
          :type="'imageViewer'"
          @goBack="openPlanListComponent"
        />
        <MarkupPlan
          v-if="false"
          :objMarkup="objMarkupImageViewer"
          type="imageViewer"
        />
        <PlanDraft
          v-if="showDraft"
          :prjId="projectId"
          :cmpId="pcmId"
          :openPlansUploadComponent="openPlansUploadComponent"
          :openPlanListComponent="openPlanListComponent"
        />
        <PlanUpload
          v-if="showUpload"
          prjId="MSrK3WQfbvyUviU4"
          objectId="TzRXhBzYKEsCgcya"
          :pcmId="pcmId"
          :userId="userId"
          :pbtId="pbtId"
          :psmId="psmId"
          :onPublish="onPublish"
          :onDelete="onDelete"
        />
      </div>
    </div>
  </div>
</template>

<script>
import PlanDraft from "./components/ImageDisplayComponents/PlanDraft/index.vue";
import PlanUpload from "./components/ImageDisplayComponents/PlanUpload/index.vue";
import SheetList from "./components/SheetMarkup/SheetListContainer";
import SheetMarkup from "./components/SheetMarkup/PlanSheetMarkup.vue";
import SheetCompareViewer from "./components/SheetCompare/index.vue";
import { PLAN_MARKUP_MODES } from "@/constants";
import { config as SmartTableConfig } from '@development/linarc-design-components';
import config from "./config";
import SheetSelectionModal from "./components/SheetCompare/SheetSelectionModal/index.vue";

import { mapActions, mapGetters } from "vuex";

export default {
  name: "App",
  components: {
    PlanDraft,
    PlanUpload,
    SheetList,
    SheetMarkup,
    SheetCompareViewer,
    SheetSelectionModal,
  },
  data() {
    return {
      isMenuCollapsed: false,
      datee:"2029-10-28T09:00:59.267473Z",
      prjId: "MSrK3WQfbvyUviU4",
      pcmId: "Mz24SaMF5ms9chfA",
      objectId:"TTAkVFCnjM58QMZE",
      userId:"projectm1295",
      filteredDrafts:[1,1,1,1,11,1,1],
      showSheetCompare: false,
      sheetCount: 20,
      showDraft: false,
      showUpload: false,
      showList: true,
      showPlans: false,
      showMarkup: true,
      showSheetMarkup: false,
      token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoyNzM1OTY4NzYxLCJqdGkiOiIxOWIxMDYzZTg1Mjg0ZmM5YTdmOGUxODJmMDQ4NzZkZCIsInVzZXJfaWQiOiI1Q2JLaHBiQ0cyQUJDcTJRIiwidXNlck5hbWUiOiJwcm9qZWN0bTEyOTUiLCJ1c2VyVHlwZSI6IklVIiwiZ2VuZXJhdGVkX29uIjoxNzM1ODgyMzYxLjkzNDQxNX0.v-BsqNQnXJaKH3uotc9ikRYTvMtsJ7QvXa8urmM7ZUQ",
      projectId: "MSrK3WQfbvyUviU4",
      // projectId: "MSrK3WQfbvyUviU4",
      // pbtId: "jVFqjndrLWxJE5WR",
      pbtId: "7dmnWWUzokxjNC56",
      progress: 10,
      planProgress:{"isProcessed":false,"isProcessing":false,"isFailed":false},
      psmId: "",
      imageMeta: {},
      markupId: null,
      markupMode: null,
      objMarkup: { 
        "prjId":"MSrK3WQfbvyUviU4",
        "pcmId":"Mz24SaMF5ms9chfA",
        "plfId":"k2zKGr32N37XfR3R",
        "sheetNumber":"A0.3",
        "objType":"plansfile",
        "objId":"k2zKGr32N37XfR3R",
        "vishal":"vishal"
      },
      objMarkupImageViewer: {
        objType:"rfiquestions",
        plfId:"k2zKGr32N37XfR3R",
        sheetNumber:"A0.3",
        objId:"6NM2ToA4yr9Nx62L",
        pcmId:"Mz24SaMF5ms9chfA",
        prjId:"MSrK3WQfbvyUviU4",

      },
      newPlfId: 'k2zKGr32N37XfR3R',
      sheetId1: "SnD6Y6GPeqdzKqUa",
      sheetId2: "ab7gwNgi792qurmH",
    };
  },
  created() {

    config.init(
      // "https://dev-api.linarc.io",
      // "http://0.0.0.0:8000",
      "https://qa-api.linarc.io",
      // "http://127.0.0.1:8000",
      "Bearer " + this.token,
      "projectm1295",
      this.prjId,
      this.pcmId,
    );
    SmartTableConfig.init(
      // "http://127.0.0.1:yarn",
      // "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNjU3MjU0OTY0LCJqdGkiOiJiNDhkOTdkNzUzMWM0MjJlYTY5OWRmMmRiOTNhYTYwZiIsInVzZXJfaWQiOiI1NyIsInVzZXJOYW1lIjoiYW5kcmV3czU2MDgifQ.MPQMz5GSyWMzsYrf_aFJRGR-kL0j4ZfQ-K3hEPOz-9Q",
      // "andrews5608"
      // "http://0.0.0.0:8000",
      "https://qa-api.linarc.io",
      // "http://127.0.0.1:8000/api/v1",
  'Bearer ' + this.token, 
      "projectm1295",
      this.prjId,
      this.pcmId,
    );

    console.log("config", config);
  },
  mounted() {
    this.showMarkup = false;
    this.loading = true;
  },
  methods: {
    ...mapActions({
      setSelectedVersion: "PlanMarkup/setSelectedVersion",
    }),
    ...mapGetters({
      getAllVersion: "PlanMarkup/getAllVersion",
    }),

    toggleMenu() {
      this.isMenuCollapsed = !this.isMenuCollapsed;
    },
    onPlanMarkupSaved(raw) {
      setTimeout(() => {
        console.log("onPlanMarkupSaved", raw);
        this.markupId = raw.plm_id;
        this.markupMode = PLAN_MARKUP_MODES.READONLY;
        const plfId = this.newPlfId ? this.newPlfId : raw.plf_id;
        this.newPlfId = plfId;
      }, 2000);

      // this.showSheetMarkup = false;
      // this.showList = true;
    },
    triggerMarkupChange() {
      console.log("triggerMarkupChange");
      const currentSheetId = this.newPlfId;
      this.newPlfId = null;      
      setTimeout(() => {
        this.newPlfId = currentSheetId;
      }, 0);
    },
    onPlanChange(raw){
      console.log("onPlanChange",raw);
      this.markupMode = PLAN_MARKUP_MODES.READONLY;
      this.newPlfId = raw.id;
      this.showSheetMarkup = true;
      this.showList = false;
      this.objMarkup.plfId = raw.id;
      this.objMarkup.sheetNumber = raw.number;
    },
    openPlans(){
      this.showDraft = true;
      this.showList = false;
      this.showUpload = false;
      this.showSheetMarkup = false;
      this.showSheetCompare = false;

    },
    openSheets(){
      this.showList = true;
      this.showDraft = false;
      this.showUpload = false;
      this.showSheetMarkup = false;
      this.showSheetCompare = false;


    },
    onPublish() {
      this.showList = true;
      this.showDraft = false;
      this.showUpload = false;

    },
    onDelete() {
      console.log("onDelete");
    },
    formatDate(dateString) {
      const date = this.$moment(dateString).format("MM/DD/YYYY");

      return date
},
    openPlansMarkupComponent(prj_id, plf_id, sheetNumber) {
      this.objMarkup.objId = "k2zKGr32N37XfR3R";
      this.objMarkup.plfId = plf_id;
      this.objMarkup.prjId = prj_id;
      this.objMarkup.pcmId = "Mz24SaMF5ms9chfA";
      this.objMarkup.objType = "plansfile";
      this.objMarkup.sheetNumber = sheetNumber;
      this.showUpload = false;
      this.showDraft = false;
      this.showList = false;
      this.showMarkup = true;
    },
    openPlansUploadComponent({ prjId, psmId, pbtId }) {
      console.log("openPlansUploadComponent", prjId, psmId, pbtId);
      this.pbtId = pbtId;
      this.psmId = psmId;
      this.showUpload = true;
      this.showDraft = false;
      this.showList = false;
    },

    openDraftsComponent() {
      console.log("Reached the Draft Route Code");
      this.$router.push(`/projectPortal/252/newPlans/drafts`);
    },
    openPlanListComponent() {
      console.log("Reached the Plan List Route Code");
      this.showUpload = false;
      this.showDraft = false;
      this.showList = true;
      this.showMarkup = false;
    },
    changeMarkupVersion(plf_id, sheetNumber) {
      console.log(plf_id, sheetNumber);
    },
    OpenSheetMarkup(raw) {
      console.log("OpenSheetMarkup", raw);
      this.markupMode = PLAN_MARKUP_MODES.READONLY;
      this.newPlfId = raw.plf_id;
      this.projectId = raw.prj_id;
      this.showSheetMarkup = true;
      this.showList = false;
      this.objMarkup.objId = "k2zKGr32N37XfR3R";
      this.objMarkup.plfId = raw.plf_id;
      this.objMarkup.prjId = this.projectId;
      this.objMarkup.pcmId = "Mz24SaMF5ms9chfA";
      this.objMarkup.objType = "plansfile";
      this.objMarkup.sheetNumber = raw.plf_sheet_number;
    },
    onVersionSelection(raw) {
      // // console.log("onVersionSelection", raw);
      // this.newPlfId = undefined;
      // setTimeout(() => {
      //   this.newPlfId = raw.plf_id;
      // }, 1000);
      // //this.setSelectedVersion(raw);
      this.OpenSheetMarkup(raw);
    },
    onMarkupSelection(raw, markupMode) {
      console.log("onMarkupSelection", raw, markupMode);
      const plfId = this.newPlfId ? this.newPlfId : raw;
      this.newPlfId = undefined;
      setTimeout(() => {
        this.markupId = raw;
        this.markupMode = markupMode;
        this.newPlfId = plfId;
      }, 1000);
      //this.setSelectedVersion(raw);
    },
    objIdCheck() {
      this.objMarkup.objId = "k2zKGr32N37XfR3R";
    },
    onBackButtonClick() {
      // alert("Back Button Clicked");
      this.showList = true;
      this.showSheetMarkup = false;
    },
    onUploadPlans() {
      this.$refs["plan-sheet-list"].openPlansUploadModal();
    },
    onCompareSheets(sheets) {
      console.log("onCompareSheets", sheets);
      this.showSheetCompare = true;
      this.showList = false;
      this.newPlfId = null;
      this.sheetId1 = sheets[0];
      this.sheetId2 = sheets[1];
    },
    onClickHyperlink(obj) {
      console.log("Hyperlink Clicked", obj);
    },
  },
};
</script>

<style lang="scss" scoped>
#app {
  width: 100vw;
  height: 100vh;
  background: #e7edf5;
  overflow: hidden; // Add this to prevent scrolling issues during transition
}

.layout {
  display: flex;
  height: 100%;
  position: relative; // Add this for proper positioning
}

.side-menu {
  min-width: 250px; // Change width to min-width
  width: 250px;
  background: #fff;
  border-right: 1px solid #ddd;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden; // Add this to handle content overflow

  &-collapsed {
    min-width: 60px; // Add min-width here too
    width: 60px;
    
    .menu-item {
      padding: 12px 15px; // Adjust padding when collapsed
      
      span {
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease;
      }
    }
  }
}

.menu-toggle {
  padding: 15px;
  text-align: right;
  cursor: pointer;
  border-bottom: 1px solid #ddd;

  .icon-left::before {
    content: "←";
  }
  
  .icon-right::before {
    content: "→";
  }
}

.menu-items {
  padding: 15px 0;
}

.menu-item {
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f5f5;
  }

  &.active {
    background-color: #e7edf5;
    font-weight: bold;
  }
}

.main-content {
  flex: 1;
  padding: 20px;
  transition: all 0.3s ease; // Change to all instead of just margin-left
  overflow: auto;
  min-width: 0; // Add this to prevent content from expanding too much
}

.content-expanded {
  margin-left: 0; // Change this from -190px to 0
}
</style>
